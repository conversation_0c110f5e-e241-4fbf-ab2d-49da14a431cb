#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار محلل الصور الذكي Gemini 2.5 Pro
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.gemini_image_analyzer import gemini_image_analyzer
from modules.logger import logger

async def create_test_images():
    """إنشاء صور اختبار متنوعة"""
    test_images = []
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # صورة 1: صورة ألعاب مع نص
        img1 = Image.new('RGB', (400, 300), color='#1a1a2e')
        draw1 = ImageDraw.Draw(img1)
        
        # رسم عناصر ألعاب
        draw1.rectangle([50, 50, 350, 250], fill='#16213e', outline='#0f3460', width=3)
        draw1.text((120, 140), "Call of Duty", fill='white')
        draw1.text((150, 160), "PlayStation 5", fill='#00d4ff')
        
        # رسم أزرار تحكم
        draw1.ellipse([320, 80, 340, 100], fill='#e94560', outline='white', width=2)
        draw1.ellipse([320, 110, 340, 130], fill='#0f3460', outline='white', width=2)
        
        img1_path = 'test_gaming_image_with_text.png'
        img1.save(img1_path, 'PNG')
        test_images.append(img1_path)
        
        # صورة 2: صورة عامة بدون ألعاب
        img2 = Image.new('RGB', (400, 300), color='#f0f0f0')
        draw2 = ImageDraw.Draw(img2)
        
        draw2.rectangle([100, 100, 300, 200], fill='#333333', outline='#666666', width=2)
        draw2.text((150, 140), "Regular Image", fill='white')
        draw2.text((170, 160), "No Gaming", fill='#666666')
        
        img2_path = 'test_regular_image.png'
        img2.save(img2_path, 'PNG')
        test_images.append(img2_path)
        
        # صورة 3: صورة ألعاب بدون نص
        img3 = Image.new('RGB', (400, 300), color='#000000')
        draw3 = ImageDraw.Draw(img3)
        
        # رسم وحدة تحكم
        draw3.rounded_rectangle([100, 120, 300, 180], radius=20, fill='#333333', outline='#666666', width=2)
        
        # أزرار التحكم
        draw3.ellipse([250, 135, 270, 155], fill='#ff0000', outline='white', width=1)
        draw3.ellipse([270, 135, 290, 155], fill='#00ff00', outline='white', width=1)
        draw3.ellipse([250, 155, 270, 175], fill='#0000ff', outline='white', width=1)
        draw3.ellipse([270, 155, 290, 175], fill='#ffff00', outline='white', width=1)
        
        img3_path = 'test_controller_image.png'
        img3.save(img3_path, 'PNG')
        test_images.append(img3_path)
        
        logger.info(f"✅ تم إنشاء {len(test_images)} صورة اختبار")
        return test_images
        
    except ImportError:
        logger.warning("⚠️ PIL غير متوفر، سيتم استخدام صور افتراضية")
        return []
    except Exception as e:
        logger.error(f"❌ فشل في إنشاء صور الاختبار: {e}")
        return []

async def test_single_image_analysis():
    """اختبار تحليل صورة واحدة"""
    
    print("\n🔍 اختبار تحليل صورة واحدة")
    print("-" * 40)
    
    # إنشاء صورة اختبار
    test_images = await create_test_images()
    
    if not test_images:
        print("❌ لا توجد صور للاختبار")
        return
    
    # اختبار الصورة الأولى
    image_path = test_images[0]
    print(f"📸 تحليل الصورة: {os.path.basename(image_path)}")
    
    try:
        result = await gemini_image_analyzer.analyze_image(
            image_path=image_path,
            analysis_types=['ocr', 'gaming_detection']
        )
        
        print(f"✅ نجح التحليل: {result.success}")
        print(f"📝 يحتوي على نص: {result.has_text}")
        if result.has_text:
            print(f"📖 النص المستخرج: '{result.extracted_text}'")
        
        print(f"🎮 متعلق بالألعاب: {result.is_gaming_related}")
        if result.is_gaming_related:
            print(f"🎯 مستوى الثقة: {result.gaming_confidence:.2f}")
            print(f"🔍 العناصر المكتشفة: {', '.join(result.gaming_elements)}")
        
        if not result.success:
            print(f"❌ خطأ: {result.error_message}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

async def test_batch_analysis():
    """اختبار تحليل مجموعة صور"""
    
    print("\n🔍 اختبار تحليل مجموعة صور")
    print("-" * 40)
    
    # إنشاء صور اختبار
    test_images = await create_test_images()
    
    if not test_images:
        print("❌ لا توجد صور للاختبار")
        return
    
    print(f"📸 تحليل {len(test_images)} صورة...")
    
    try:
        results = await gemini_image_analyzer.batch_analyze_images(
            image_paths=test_images,
            analysis_types=['ocr', 'gaming_detection']
        )
        
        print(f"\n📊 نتائج التحليل المجمع:")
        print("=" * 50)
        
        for i, result in enumerate(results, 1):
            image_name = os.path.basename(result.image_path)
            print(f"\n{i}. {image_name}")
            print(f"   ✅ نجح: {result.success}")
            
            if result.success:
                print(f"   📝 نص: {'✅' if result.has_text else '❌'}")
                if result.has_text:
                    print(f"       '{result.extracted_text[:50]}...'")
                
                print(f"   🎮 ألعاب: {'✅' if result.is_gaming_related else '❌'}")
                if result.is_gaming_related:
                    print(f"       ثقة: {result.gaming_confidence:.2f}")
                    print(f"       عناصر: {', '.join(result.gaming_elements[:3])}")
            else:
                print(f"   ❌ خطأ: {result.error_message}")
        
        # إحصائيات
        successful = sum(1 for r in results if r.success)
        with_text = sum(1 for r in results if r.has_text)
        gaming_related = sum(1 for r in results if r.is_gaming_related)
        
        print(f"\n📈 الإحصائيات:")
        print(f"   نجح: {successful}/{len(results)}")
        print(f"   يحتوي على نص: {with_text}/{len(results)}")
        print(f"   متعلق بالألعاب: {gaming_related}/{len(results)}")
        
    except Exception as e:
        print(f"❌ خطأ في التحليل المجمع: {e}")

async def test_performance():
    """اختبار الأداء والسرعة"""
    
    print("\n⚡ اختبار الأداء")
    print("-" * 40)
    
    # إنشاء صورة اختبار
    test_images = await create_test_images()
    
    if not test_images:
        print("❌ لا توجد صور للاختبار")
        return
    
    image_path = test_images[0]
    
    # اختبار سرعة التحليل
    start_time = datetime.now()
    
    try:
        result = await gemini_image_analyzer.analyze_image(
            image_path=image_path,
            analysis_types=['ocr']  # OCR فقط للسرعة
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"⏱️ وقت التحليل: {duration:.2f} ثانية")
        print(f"✅ نجح: {result.success}")
        
        if result.success:
            print(f"📊 معدل المعالجة: {1/duration:.2f} صورة/ثانية")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {e}")

async def display_usage_stats():
    """عرض إحصائيات الاستخدام"""
    
    print("\n📊 إحصائيات الاستخدام")
    print("-" * 40)
    
    stats = gemini_image_analyzer.get_usage_stats()
    
    for key, value in stats.items():
        print(f"   {key}: {value}")

async def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    
    test_files = [
        'test_gaming_image_with_text.png',
        'test_regular_image.png',
        'test_controller_image.png'
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"🗑️ تم حذف ملف الاختبار: {file_path}")
            except Exception as e:
                logger.warning(f"⚠️ فشل في حذف {file_path}: {e}")

async def main():
    """الدالة الرئيسية"""
    
    print("\n" + "="*60)
    print("🧪 اختبار محلل الصور الذكي Gemini 2.5 Pro")
    print("="*60)
    
    if not gemini_image_analyzer.enabled:
        print("❌ محلل الصور غير مفعل - تحقق من مفتاح API")
        return
    
    try:
        # تشغيل الاختبارات
        await test_single_image_analysis()
        await test_batch_analysis()
        await test_performance()
        await display_usage_stats()
        
        print("\n🎉 اكتملت جميع الاختبارات!")
        
        # حفظ تقرير
        stats = gemini_image_analyzer.get_usage_stats()
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'analyzer_stats': stats,
            'test_completed': True
        }
        
        report_file = f"gemini_image_analyzer_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"💾 تم حفظ التقرير في: {report_file}")
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في التشغيل: {e}")
    finally:
        # تنظيف الملفات
        await cleanup_test_files()

if __name__ == "__main__":
    asyncio.run(main())
