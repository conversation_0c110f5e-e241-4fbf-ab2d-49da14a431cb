#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل وكيل أخبار الألعاب مع واجهة الويب
يشغل خادم الواجهة الويب والوكيل معاً
"""

import os
import sys
import asyncio
import threading
import time
import subprocess
from pathlib import Path

# إضافة المسار الحالي لـ Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from modules.logger import logger

def start_web_server():
    """تشغيل خادم الواجهة الويب"""
    try:
        logger.info("🌐 بدء تشغيل خادم الواجهة الويب...")

        # استيراد وتشغيل الخادم
        from web_api import app, load_settings

        # تحميل الإعدادات
        load_settings()

        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False  # منع إعادة التحميل التلقائي
        )

    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل خادم الواجهة الويب: {e}")

def start_agent():
    """تشغيل الوكيل"""
    try:
        logger.info("🤖 بدء تشغيل وكيل أخبار الألعاب...")

        # استيراد وتشغيل الوكيل
        from main import main
        asyncio.run(main())

    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الوكيل: {e}")

def main():
    """تشغيل النظام الكامل"""
    try:
        print("🎮 بدء تشغيل وكيل أخبار الألعاب مع الواجهة الويب")
        print("=" * 60)
        print("🌐 واجهة الويب: http://localhost:5000")
        print("🤖 الوكيل: سيبدأ تلقائياً")
        print("🚀 سيتم فتح المتصفح تلقائياً...")
        print("=" * 60)

        # بدء خادم الواجهة الويب في thread منفصل
        web_thread = threading.Thread(target=start_web_server, daemon=True)
        web_thread.start()

        # انتظار قصير لضمان تشغيل الخادم
        time.sleep(2)

        # تشغيل الوكيل في الـ thread الرئيسي
        start_agent()

    except KeyboardInterrupt:
        print("\n⌨️ تم الإيقاف بواسطة المستخدم")
        logger.info("⌨️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        logger.error(f"❌ خطأ في تشغيل النظام: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()