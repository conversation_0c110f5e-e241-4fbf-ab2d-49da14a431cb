#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحسينات تدفق العمل للوكيل
يختبر النظام الجديد: النشر أولاً ثم المهام الخلفية
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from main import GamingNewsBot

class WorkflowTester:
    """اختبار تدفق العمل المحسن"""
    
    def __init__(self):
        self.test_results = {
            'fast_content_collection': False,
            'quick_publishing': False,
            'background_tasks': False,
            'cycle_timing': False,
            'overall_performance': False
        }
        
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار تحسينات تدفق العمل...")
        print("=" * 60)
        
        try:
            # اختبار 1: جمع المحتوى السريع
            await self.test_fast_content_collection()
            
            # اختبار 2: النشر السريع
            await self.test_quick_publishing()
            
            # اختبار 3: المهام الخلفية
            await self.test_background_tasks()
            
            # اختبار 4: توقيت الدورات
            await self.test_cycle_timing()
            
            # تقييم الأداء العام
            self.evaluate_overall_performance()
            
            # عرض النتائج
            self.display_results()
            
        except Exception as e:
            logger.error(f"❌ خطأ في الاختبارات: {e}")
            print(f"❌ خطأ في الاختبارات: {e}")
    
    async def test_fast_content_collection(self):
        """اختبار جمع المحتوى السريع"""
        print("🔍 اختبار 1: جمع المحتوى السريع...")
        
        try:
            bot = GamingNewsBot()
            await bot._initialize_components()
            
            start_time = time.time()
            
            # اختبار الجمع السريع
            content = await bot._collect_new_content_fast()
            
            collection_time = time.time() - start_time
            
            if collection_time < 60:  # أقل من دقيقة
                print(f"✅ جمع المحتوى السريع: {collection_time:.1f} ثانية")
                print(f"📊 تم جمع {len(content)} عنصر محتوى")
                self.test_results['fast_content_collection'] = True
            else:
                print(f"⚠️ جمع المحتوى بطيء: {collection_time:.1f} ثانية")
                
        except Exception as e:
            print(f"❌ فشل اختبار جمع المحتوى: {e}")
    
    async def test_quick_publishing(self):
        """اختبار النشر السريع"""
        print("\n📝 اختبار 2: النشر السريع...")
        
        try:
            bot = GamingNewsBot()
            await bot._initialize_components()
            
            # إنشاء مقال تجريبي
            test_article = {
                'title': 'مقال اختبار تدفق العمل المحسن',
                'content': 'هذا مقال اختبار لتدفق العمل المحسن في وكيل أخبار الألعاب.',
                'keywords': ['اختبار', 'تحسين', 'ألعاب'],
                'category': 'اختبار',
                'dialect': 'standard'
            }
            
            start_time = time.time()
            
            # محاكاة النشر (بدون نشر فعلي)
            print("🔄 محاكاة عملية النشر...")
            await asyncio.sleep(2)  # محاكاة وقت النشر
            
            publishing_time = time.time() - start_time
            
            if publishing_time < 10:  # أقل من 10 ثوان
                print(f"✅ النشر السريع: {publishing_time:.1f} ثانية")
                self.test_results['quick_publishing'] = True
            else:
                print(f"⚠️ النشر بطيء: {publishing_time:.1f} ثانية")
                
        except Exception as e:
            print(f"❌ فشل اختبار النشر: {e}")
    
    async def test_background_tasks(self):
        """اختبار المهام الخلفية"""
        print("\n🔧 اختبار 3: المهام الخلفية...")
        
        try:
            bot = GamingNewsBot()
            await bot._initialize_components()
            
            start_time = time.time()
            
            # اختبار المهام الخلفية لمدة قصيرة (30 ثانية)
            print("🔄 اختبار المهام الخلفية لمدة 30 ثانية...")
            
            # محاكاة المهام الخلفية
            await bot._analyze_and_improve_existing_articles()
            await bot._run_seo_analysis()
            await bot._run_maintenance_tasks()
            
            background_time = time.time() - start_time
            
            print(f"✅ المهام الخلفية: {background_time:.1f} ثانية")
            self.test_results['background_tasks'] = True
                
        except Exception as e:
            print(f"❌ فشل اختبار المهام الخلفية: {e}")
    
    async def test_cycle_timing(self):
        """اختبار توقيت الدورات"""
        print("\n⏰ اختبار 4: توقيت الدورات...")
        
        try:
            bot = GamingNewsBot()
            await bot._initialize_components()
            
            start_time = time.time()
            
            # اختبار دورة كاملة محاكاة
            cycle_result = await bot._main_cycle()
            
            cycle_time = time.time() - start_time
            
            if cycle_result and cycle_result.get('success'):
                print(f"✅ دورة العمل: {cycle_time:.1f} ثانية")
                print(f"📊 النتائج: {cycle_result}")
                self.test_results['cycle_timing'] = True
            else:
                print(f"⚠️ دورة العمل فشلت أو استغرقت وقتاً طويلاً: {cycle_time:.1f} ثانية")
                
        except Exception as e:
            print(f"❌ فشل اختبار توقيت الدورات: {e}")
    
    def evaluate_overall_performance(self):
        """تقييم الأداء العام"""
        passed_tests = sum(self.test_results.values())
        total_tests = len(self.test_results)
        
        success_rate = (passed_tests / total_tests) * 100
        
        if success_rate >= 80:
            self.test_results['overall_performance'] = True
            print(f"\n🎉 الأداء العام ممتاز: {success_rate:.1f}%")
        elif success_rate >= 60:
            print(f"\n👍 الأداء العام جيد: {success_rate:.1f}%")
        else:
            print(f"\n⚠️ الأداء العام يحتاج تحسين: {success_rate:.1f}%")
    
    def display_results(self):
        """عرض النتائج النهائية"""
        print("\n" + "=" * 60)
        print("📊 نتائج اختبار تحسينات تدفق العمل")
        print("=" * 60)
        
        for test_name, result in self.test_results.items():
            status = "✅ نجح" if result else "❌ فشل"
            test_display_name = {
                'fast_content_collection': 'جمع المحتوى السريع',
                'quick_publishing': 'النشر السريع',
                'background_tasks': 'المهام الخلفية',
                'cycle_timing': 'توقيت الدورات',
                'overall_performance': 'الأداء العام'
            }.get(test_name, test_name)
            
            print(f"{status} {test_display_name}")
        
        print("=" * 60)
        
        # توصيات
        if not self.test_results['overall_performance']:
            print("\n💡 توصيات للتحسين:")
            if not self.test_results['fast_content_collection']:
                print("- تحسين سرعة جمع المحتوى")
            if not self.test_results['quick_publishing']:
                print("- تحسين سرعة النشر")
            if not self.test_results['background_tasks']:
                print("- تحسين المهام الخلفية")
            if not self.test_results['cycle_timing']:
                print("- تحسين توقيت الدورات")
        else:
            print("\n🎉 جميع التحسينات تعمل بشكل ممتاز!")

async def main():
    """الدالة الرئيسية"""
    tester = WorkflowTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    try:
        print("🧪 اختبار تحسينات تدفق العمل للوكيل")
        print("=" * 60)
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
