#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد مفاتيح APIs للصور المرخصة
"""

import os
import sys
from modules.logger import logger

def setup_licensed_images_apis():
    """إعداد مفاتيح APIs للصور المرخصة"""
    
    print("🔧 إعداد مفاتيح APIs للصور المرخصة")
    print("="*50)
    
    # قائمة بالمفاتيح المطلوبة
    required_apis = {
        'TWITCH_CLIENT_ID': {
            'description': 'معرف العميل لـ Twitch (مطلوب لـ IGDB API)',
            'url': 'https://dev.twitch.tv/console/apps',
            'optional': False
        },
        'TWITCH_CLIENT_SECRET': {
            'description': 'سر العميل لـ Twitch (مطلوب لـ IGDB API)',
            'url': 'https://dev.twitch.tv/console/apps',
            'optional': False
        },
        'RAWG_API_KEY': {
            'description': 'مفتاح API لـ RAWG.io',
            'url': 'https://rawg.io/apidocs',
            'optional': True
        },
        'STEAM_API_KEY': {
            'description': 'مفتاح API لـ Steam Web API (اختياري)',
            'url': 'https://steamcommunity.com/dev/apikey',
            'optional': True
        }
    }
    
    # فحص المفاتيح الحالية
    current_keys = {}
    missing_keys = []
    
    for key_name, info in required_apis.items():
        current_value = os.getenv(key_name, '')
        current_keys[key_name] = current_value
        
        if not current_value and not info['optional']:
            missing_keys.append(key_name)
    
    # عرض الحالة الحالية
    print("📊 حالة مفاتيح APIs الحالية:")
    for key_name, info in required_apis.items():
        current_value = current_keys[key_name]
        status = "✅ متوفر" if current_value else ("⚠️ مفقود (اختياري)" if info['optional'] else "❌ مفقود (مطلوب)")
        masked_value = f"{current_value[:4]}...{current_value[-4:]}" if current_value else "غير محدد"
        
        print(f"  {key_name}: {status}")
        if current_value:
            print(f"    القيمة: {masked_value}")
        print(f"    الوصف: {info['description']}")
        print(f"    الرابط: {info['url']}")
        print()
    
    # إذا كانت هناك مفاتيح مفقودة
    if missing_keys:
        print("⚠️ مفاتيح مطلوبة مفقودة:")
        for key in missing_keys:
            print(f"  - {key}: {required_apis[key]['description']}")
        
        print("\n📝 لإعداد المفاتيح المفقودة:")
        print("1. قم بزيارة الروابط المذكورة أعلاه")
        print("2. أنشئ حساب وأحصل على المفاتيح")
        print("3. أضف المفاتيح لمتغيرات البيئة:")
        
        for key in missing_keys:
            print(f"   export {key}='your_key_here'")
        
        print("\n4. أو أضفها لملف .env:")
        for key in missing_keys:
            print(f"   {key}=your_key_here")
    
    else:
        print("✅ جميع المفاتيح المطلوبة متوفرة!")
    
    # اختبار الاتصال
    print("\n🧪 اختبار الاتصال بـ APIs...")
    test_apis_connection()
    
    return len(missing_keys) == 0

def test_apis_connection():
    """اختبار الاتصال بـ APIs"""
    try:
        import asyncio
        from modules.licensed_image_manager import licensed_image_manager
        
        async def test_connection():
            if licensed_image_manager:
                results = await licensed_image_manager.test_all_providers()
                
                print("📊 نتائج اختبار الاتصال:")
                for provider, result in results.items():
                    status = "✅ يعمل" if result['available'] else "❌ لا يعمل"
                    print(f"  {provider.upper()}: {status}")
                    
                    if result['available']:
                        print(f"    صور موجودة: {result['images_found']}")
                    else:
                        print(f"    خطأ: {result.get('error', 'غير محدد')}")
                
                # حساب معدل النجاح
                working_providers = sum(1 for r in results.values() if r['available'])
                total_providers = len(results)
                success_rate = working_providers / total_providers if total_providers > 0 else 0
                
                print(f"\n🎯 معدل نجاح APIs: {success_rate:.1%} ({working_providers}/{total_providers})")
                
                if success_rate >= 0.5:
                    print("✅ نظام الصور المرخصة جاهز للاستخدام!")
                else:
                    print("⚠️ نظام الصور المرخصة يحتاج إعداد إضافي")
            else:
                print("❌ مدير الصور المرخصة غير متوفر")
        
        asyncio.run(test_connection())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")

def create_env_template():
    """إنشاء ملف .env نموذجي"""
    template_content = """# مفاتيح APIs للصور المرخصة

# IGDB API (عبر Twitch)
# احصل على المفاتيح من: https://dev.twitch.tv/console/apps
TWITCH_CLIENT_ID=your_twitch_client_id_here
TWITCH_CLIENT_SECRET=your_twitch_client_secret_here

# RAWG API (اختياري)
# احصل على المفتاح من: https://rawg.io/apidocs
RAWG_API_KEY=your_rawg_api_key_here

# Steam Web API (اختياري)
# احصل على المفتاح من: https://steamcommunity.com/dev/apikey
STEAM_API_KEY=your_steam_api_key_here

# مفاتيح Google Search (للبحث العام)
GOOGLE_SEARCH_API_KEY_1=your_google_search_key_1
GOOGLE_SEARCH_API_KEY_2=your_google_search_key_2
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id

# مفاتيح Gemini AI
GEMINI_API_KEY_1=your_gemini_key_1
GEMINI_API_KEY_2=your_gemini_key_2
"""
    
    try:
        with open('.env.template', 'w', encoding='utf-8') as f:
            f.write(template_content)
        print("📄 تم إنشاء ملف .env.template")
        print("💡 انسخ الملف إلى .env وأضف مفاتيحك الحقيقية")
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف .env.template: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إعداد مفاتيح APIs للصور المرخصة...")
    
    # إعداد المفاتيح
    success = setup_licensed_images_apis()
    
    # إنشاء ملف نموذجي
    create_env_template()
    
    print("\n" + "="*50)
    if success:
        print("🎉 تم إعداد جميع المفاتيح المطلوبة بنجاح!")
        print("✅ نظام الصور المرخصة جاهز للاستخدام")
    else:
        print("⚠️ بعض المفاتيح المطلوبة مفقودة")
        print("📝 يرجى إضافة المفاتيح المفقودة وإعادة تشغيل الاختبار")
    
    print("\n💡 نصائح:")
    print("- استخدم ملف .env لحفظ المفاتيح بأمان")
    print("- لا تشارك مفاتيح API مع أحد")
    print("- راقب استخدام المفاتيح لتجنب تجاوز الحدود")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
