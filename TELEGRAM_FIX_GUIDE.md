# دليل إصلاح مشاكل تيليجرام

## المشاكل الشائعة وحلولها

### 1. مشكلة "Chat not found"

**الأعراض:**
```
❌ خطأ من Whisper API: لم يتم رفع ملف صوتي
⚠️ فشل في إرسال الإشعار: Chat not found
```

**الأسباب:**
- معرف المدير غير صحيح أو منتهي الصلاحية
- المدير لم يرسل رسالة للبوت من قبل
- استخدام اسم مستخدم (@username) بدلاً من المعرف الرقمي

**الحلول:**

#### الحل السريع (تلقائي):
```bash
python fix_admin_id.py
```

#### الحل اليدوي:
1. **احصل على المعرف الصحيح:**
   ```bash
   python get_admin_id.py
   ```

2. **أرسل رسالة للبوت أولاً:**
   - اذهب إلى البوت في تيليجرام
   - أرسل أي رسالة (مثل: /start أو مرحبا)

3. **حدث ملف الإعدادات:**
   ```python
   # في config/settings.py
   TELEGRAM_ADMIN_ID = "123456789"  # استخدم المعرف الرقمي
   ```

### 2. مشكلة "Unauthorized"

**الأعراض:**
```
❌ توكن البوت غير صحيح أو منتهي الصلاحية
```

**الحل:**
1. تحقق من `TELEGRAM_BOT_TOKEN` في `config/settings.py`
2. تأكد من أن البوت مفعل في BotFather
3. أنشئ بوت جديد إذا لزم الأمر

### 3. مشكلة "Forbidden"

**الأعراض:**
```
❌ البوت لا يملك صلاحية النشر في القناة
```

**الحل:**
1. أضف البوت كمدير في القناة
2. امنح البوت صلاحية "Post Messages"
3. تأكد من صحة معرف القناة

## أدوات الاختبار والإصلاح

### 1. اختبار شامل للنظام
```bash
python test_telegram_system.py
```
يختبر جميع مكونات تيليجرام ويعطي تقرير مفصل.

### 2. إصلاح معرف المدير تلقائياً
```bash
python fix_admin_id.py
```
يجد المعرف الصحيح ويحدث ملف الإعدادات تلقائياً.

### 3. الحصول على معرف المدير
```bash
python get_admin_id.py
```
يعرض جميع المعرفات المتاحة مع تفاصيل كل مستخدم.

### 4. اختبار إرسال رسالة للمدير
```bash
python test_admin_chat.py
```
يختبر إرسال رسالة للمدير فقط.

## التحقق من الإعدادات

### ملف config/settings.py
```python
# تأكد من هذه الإعدادات:
TELEGRAM_BOT_TOKEN = "YOUR_BOT_TOKEN"           # من BotFather
TELEGRAM_CHANNEL_ID = "@your_channel"          # معرف القناة
TELEGRAM_ADMIN_ID = "123456789"                # المعرف الرقمي للمدير
```

### متطلبات البوت:
1. **في BotFather:**
   - البوت مفعل
   - التوكن صحيح

2. **في القناة:**
   - البوت مضاف كمدير
   - صلاحية "Post Messages" مفعلة

3. **للمدير:**
   - أرسل رسالة للبوت مرة واحدة على الأقل
   - استخدم المعرف الرقمي في الإعدادات

## رسائل الخطأ الشائعة

| الخطأ | السبب | الحل |
|-------|--------|------|
| `Chat not found` | معرف خاطئ | `python fix_admin_id.py` |
| `Unauthorized` | توكن خاطئ | تحديث `TELEGRAM_BOT_TOKEN` |
| `Forbidden` | لا صلاحية | إضافة البوت كمدير |
| `Bad Request` | معرف قناة خاطئ | تحديث `TELEGRAM_CHANNEL_ID` |

## خطوات الإعداد الأولي

1. **إنشاء البوت:**
   ```
   /newbot في BotFather
   احفظ التوكن
   ```

2. **إعداد القناة:**
   ```
   أضف البوت كمدير
   امنح صلاحية النشر
   ```

3. **تحديث الإعدادات:**
   ```bash
   # حدث config/settings.py
   python get_admin_id.py
   python test_telegram_system.py
   ```

4. **اختبار النظام:**
   ```bash
   python test_telegram_system.py
   ```

## الدعم

إذا استمرت المشاكل:
1. شغل `python test_telegram_system.py` واحفظ النتائج
2. تحقق من ملف السجلات
3. تأكد من جميع الإعدادات المطلوبة

---

**ملاحظة:** جميع الأدوات تعمل بشكل آمن ولا تؤثر على البيانات الموجودة.
