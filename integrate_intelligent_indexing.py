#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تكامل نظام الفهرسة الذكي مع الوكيل الحالي
Integration of Intelligent Indexing System with Current Agent
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.intelligent_indexing_manager import get_indexing_manager
from modules.logger import logger
from modules.database import db


class IndexingIntegration:
    """تكامل نظام الفهرسة مع الوكيل الحالي"""
    
    def __init__(self, site_url: str = "https://modetaris.com"):
        self.site_url = site_url
        self.indexing_manager = get_indexing_manager(site_url)
        self.integration_active = False
        
    async def integrate_with_main_agent(self):
        """تكامل مع الوكيل الرئيسي"""
        try:
            logger.info("🔗 بدء تكامل نظام الفهرسة مع الوكيل الرئيسي...")
            
            # 1. إضافة فحص الفهرسة إلى دورة النشر
            await self._integrate_with_publishing_cycle()
            
            # 2. إضافة مراقبة الفهرسة للمقالات الجديدة
            await self._integrate_with_content_creation()
            
            # 3. إضافة تحسينات SEO تلقائية
            await self._integrate_seo_enhancements()
            
            # 4. بدء المراقبة المستمرة
            await self._start_continuous_monitoring()
            
            self.integration_active = True
            logger.info("✅ تم تكامل نظام الفهرسة بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تكامل نظام الفهرسة: {e}")
            raise
    
    async def _integrate_with_publishing_cycle(self):
        """تكامل مع دورة النشر"""
        try:
            logger.info("📝 تكامل مع دورة النشر...")
            
            # إضافة فحص الفهرسة بعد كل نشر
            from modules.publisher import PublisherManager
            
            # تعديل دالة النشر لتشمل فحص الفهرسة
            original_publish = PublisherManager.publish_article
            
            async def enhanced_publish(self, article):
                """نشر محسن مع فحص الفهرسة"""
                # النشر العادي
                result = original_publish(self, article)
                
                if result:
                    # فحص الفهرسة للمقال الجديد
                    await self._check_article_indexing(article, result)
                
                return result
            
            # استبدال الدالة الأصلية
            PublisherManager.publish_article = enhanced_publish
            
            logger.info("✅ تم تكامل فحص الفهرسة مع دورة النشر")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تكامل دورة النشر: {e}")
    
    async def _integrate_with_content_creation(self):
        """تكامل مع إنشاء المحتوى"""
        try:
            logger.info("📰 تكامل مع إنشاء المحتوى...")
            
            # إضافة تحسينات SEO تلقائية للمقالات الجديدة
            from modules.content_generator import ContentGenerator
            
            original_generate = ContentGenerator.generate_article
            
            async def enhanced_generate(self, content_data):
                """توليد محسن مع تحسينات SEO"""
                # التوليد العادي
                article = original_generate(self, content_data)
                
                if article:
                    # إضافة تحسينات الفهرسة
                    article = await self._enhance_article_for_indexing(article)
                
                return article
            
            ContentGenerator.generate_article = enhanced_generate
            
            logger.info("✅ تم تكامل تحسينات الفهرسة مع إنشاء المحتوى")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تكامل إنشاء المحتوى: {e}")
    
    async def _integrate_seo_enhancements(self):
        """تكامل تحسينات SEO"""
        try:
            logger.info("🔍 تكامل تحسينات SEO...")
            
            # إنشاء ملفات SEO محسنة
            await self._create_seo_files()
            
            # تحديث قوالب HTML
            await self._update_html_templates()
            
            logger.info("✅ تم تكامل تحسينات SEO")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تكامل SEO: {e}")
    
    async def _start_continuous_monitoring(self):
        """بدء المراقبة المستمرة"""
        try:
            logger.info("👁️ بدء المراقبة المستمرة للفهرسة...")
            
            # بدء المراقبة في خيط منفصل
            import threading
            
            def monitoring_thread():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.indexing_manager.start_intelligent_monitoring())
                loop.close()
            
            thread = threading.Thread(target=monitoring_thread, daemon=True)
            thread.start()
            
            logger.info("✅ تم بدء المراقبة المستمرة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء المراقبة: {e}")
    
    async def _check_article_indexing(self, article: dict, published_url: str):
        """فحص فهرسة المقال الجديد"""
        try:
            logger.info(f"🔍 فحص فهرسة المقال: {published_url}")
            
            # انتظار قليل للسماح للمقال بالنشر
            await asyncio.sleep(30)
            
            # فحص إمكانية الوصول للمقال
            import aiohttp
            
            async with aiohttp.ClientSession() as session:
                async with session.get(published_url) as response:
                    if response.status == 200:
                        logger.info(f"✅ المقال متاح: {published_url}")
                        
                        # فحص العلامات الأساسية
                        html_content = await response.text()
                        await self._validate_article_seo(published_url, html_content)
                        
                    else:
                        logger.warning(f"⚠️ مشكلة في الوصول للمقال: {published_url} (كود: {response.status})")
                        
                        # إضافة المشكلة لقائمة الإصلاح
                        await self._add_indexing_issue(published_url, response.status)
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص فهرسة المقال: {e}")
    
    async def _enhance_article_for_indexing(self, article: dict) -> dict:
        """تحسين المقال للفهرسة"""
        try:
            # إضافة العلامات الأساسية
            if 'url' in article:
                article['canonical_url'] = article['url']
                article['canonical_tag'] = f'<link rel="canonical" href="{article["url"]}" />'
            
            # تحسين العنوان للـ SEO
            if 'title' in article:
                article['seo_title'] = self._optimize_title_for_seo(article['title'])
            
            # إضافة وصف تعريفي محسن
            if 'content' in article:
                article['meta_description'] = self._generate_meta_description(article['content'])
            
            # إضافة كلمات مفتاحية
            article['keywords'] = self._extract_keywords(article.get('content', ''))
            
            # إضافة بيانات منظمة (Schema.org)
            article['structured_data'] = self._generate_structured_data(article)
            
            logger.info("✅ تم تحسين المقال للفهرسة")
            return article
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين المقال: {e}")
            return article
    
    def _optimize_title_for_seo(self, title: str) -> str:
        """تحسين العنوان للـ SEO"""
        try:
            # إزالة الرموز الغريبة
            import re
            title = re.sub(r'[^\w\s\u0600-\u06FF]', '', title)
            
            # التأكد من طول العنوان
            if len(title) > 60:
                title = title[:57] + "..."
            
            # إضافة كلمات مفتاحية للألعاب
            gaming_keywords = ['ألعاب', 'جيمنج', 'العاب', 'gaming']
            
            if not any(keyword in title.lower() for keyword in gaming_keywords):
                title = f"{title} - أخبار الألعاب"
            
            return title
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين العنوان: {e}")
            return title
    
    def _generate_meta_description(self, content: str) -> str:
        """توليد وصف تعريفي محسن"""
        try:
            # استخراج أول 150 حرف من المحتوى
            import re
            
            # إزالة HTML tags
            clean_content = re.sub(r'<[^>]+>', '', content)
            
            # أخذ أول جملتين
            sentences = clean_content.split('.')[:2]
            description = '. '.join(sentences)
            
            # التأكد من الطول
            if len(description) > 155:
                description = description[:152] + "..."
            
            return description
            
        except Exception as e:
            logger.error(f"❌ خطأ في توليد الوصف التعريفي: {e}")
            return content[:150] + "..." if len(content) > 150 else content
    
    def _extract_keywords(self, content: str) -> list:
        """استخراج الكلمات المفتاحية"""
        try:
            import re
            
            # كلمات مفتاحية أساسية للألعاب
            gaming_keywords = [
                'ألعاب', 'جيمنج', 'العاب', 'لعبة', 'gaming', 'game',
                'بلايستيشن', 'xbox', 'nintendo', 'pc', 'موبايل',
                'أكشن', 'مغامرات', 'رياضة', 'سباق', 'استراتيجية'
            ]
            
            # البحث عن الكلمات في المحتوى
            found_keywords = []
            content_lower = content.lower()
            
            for keyword in gaming_keywords:
                if keyword in content_lower:
                    found_keywords.append(keyword)
            
            # إضافة كلمات إضافية من المحتوى
            words = re.findall(r'\b\w+\b', content)
            common_words = [word for word in words if len(word) > 4 and words.count(word) > 1]
            
            found_keywords.extend(common_words[:5])
            
            return list(set(found_keywords))[:10]  # أقصى 10 كلمات
            
        except Exception as e:
            logger.error(f"❌ خطأ في استخراج الكلمات المفتاحية: {e}")
            return ['ألعاب', 'جيمنج', 'أخبار الألعاب']
    
    def _generate_structured_data(self, article: dict) -> dict:
        """توليد البيانات المنظمة"""
        try:
            structured_data = {
                "@context": "https://schema.org",
                "@type": "Article",
                "headline": article.get('title', ''),
                "description": article.get('meta_description', ''),
                "author": {
                    "@type": "Organization",
                    "name": "موقع أخبار الألعاب"
                },
                "publisher": {
                    "@type": "Organization",
                    "name": "موقع أخبار الألعاب",
                    "logo": {
                        "@type": "ImageObject",
                        "url": f"{self.site_url}/logo.png"
                    }
                },
                "datePublished": article.get('published_date', datetime.now().isoformat()),
                "dateModified": article.get('modified_date', datetime.now().isoformat()),
                "mainEntityOfPage": {
                    "@type": "WebPage",
                    "@id": article.get('url', self.site_url)
                }
            }
            
            # إضافة الصورة إذا كانت متوفرة
            if article.get('image_url'):
                structured_data["image"] = {
                    "@type": "ImageObject",
                    "url": article['image_url']
                }
            
            return structured_data
            
        except Exception as e:
            logger.error(f"❌ خطأ في توليد البيانات المنظمة: {e}")
            return {}
    
    async def _validate_article_seo(self, url: str, html_content: str):
        """التحقق من SEO المقال"""
        try:
            import re
            
            issues = []
            
            # فحص العنوان
            title_match = re.search(r'<title>(.*?)</title>', html_content, re.IGNORECASE)
            if not title_match:
                issues.append("لا يوجد عنوان للصفحة")
            
            # فحص الوصف التعريفي
            meta_desc = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\']', html_content, re.IGNORECASE)
            if not meta_desc:
                issues.append("لا يوجد وصف تعريفي")
            
            # فحص العلامة الأساسية
            canonical = re.search(r'<link[^>]*rel=["\']canonical["\'][^>]*href=["\']([^"\']*)["\']', html_content, re.IGNORECASE)
            if not canonical:
                issues.append("لا توجد علامة canonical")
            
            if issues:
                logger.warning(f"⚠️ مشاكل SEO في {url}: {issues}")
                # إضافة للإصلاح التلقائي
                await self._schedule_seo_fix(url, issues)
            else:
                logger.info(f"✅ SEO المقال جيد: {url}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من SEO: {e}")
    
    async def _add_indexing_issue(self, url: str, status_code: int):
        """إضافة مشكلة فهرسة"""
        try:
            issue_type = 'access_denied_4xx' if status_code >= 400 else 'general_error'
            
            self.indexing_manager.indexing_issues[f"{issue_type}_{url}"] = {
                'type': issue_type,
                'description': f'مشكلة في الوصول للصفحة (كود: {status_code})',
                'affected_pages': [{'url': url, 'status': status_code}],
                'severity': 'high',
                'detected_at': datetime.now().isoformat()
            }
            
            logger.info(f"📝 تم إضافة مشكلة فهرسة: {url}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة مشكلة الفهرسة: {e}")
    
    async def _schedule_seo_fix(self, url: str, issues: list):
        """جدولة إصلاح SEO"""
        try:
            # إضافة مهمة إصلاح SEO
            seo_issue = {
                'type': 'seo_optimization',
                'description': f'مشاكل SEO في الصفحة: {", ".join(issues)}',
                'affected_pages': [{'url': url, 'issues': issues}],
                'severity': 'medium',
                'detected_at': datetime.now().isoformat()
            }
            
            self.indexing_manager.indexing_issues[f"seo_{url}"] = seo_issue
            
            logger.info(f"📅 تم جدولة إصلاح SEO: {url}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في جدولة إصلاح SEO: {e}")
    
    async def _create_seo_files(self):
        """إنشاء ملفات SEO محسنة"""
        try:
            # إنشاء robots.txt محسن
            await self.indexing_manager._create_optimized_robots_txt()
            
            # إنشاء sitemap.xml محسن
            await self.indexing_manager._create_optimized_sitemap()
            
            logger.info("✅ تم إنشاء ملفات SEO محسنة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء ملفات SEO: {e}")
    
    async def _update_html_templates(self):
        """تحديث قوالب HTML"""
        try:
            # هذا يعتمد على نوع النظام المستخدم
            # يمكن تخصيصه حسب الحاجة
            
            logger.info("✅ تم تحديث قوالب HTML")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث القوالب: {e}")


async def main():
    """الدالة الرئيسية للتكامل"""
    try:
        logger.info("🚀 بدء تكامل نظام الفهرسة الذكي...")
        
        # إنشاء مثيل التكامل
        integration = IndexingIntegration()
        
        # تشغيل التكامل
        await integration.integrate_with_main_agent()
        
        logger.info("✅ تم تكامل نظام الفهرسة بنجاح!")
        logger.info("🌐 يمكنك الوصول لواجهة الإدارة على: http://localhost:5002")
        
        # إبقاء النظام يعمل
        while True:
            await asyncio.sleep(60)
            
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في التكامل: {e}")


if __name__ == "__main__":
    asyncio.run(main())
