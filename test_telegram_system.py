#!/usr/bin/env python3
"""
اختبار شامل لنظام تيليجرام المحدث
"""

import asyncio
import sys
import os
from telegram import Bot
from config.settings import BotConfig

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_telegram_system():
    """اختبار شامل لنظام تيليجرام"""
    print("🧪 بدء الاختبار الشامل لنظام تيليجرام...")
    print("=" * 60)
    
    # 1. اختبار التوكن
    print("\n1️⃣ اختبار توكن البوت...")
    try:
        bot = Bot(token=BotConfig.TELEGRAM_BOT_TOKEN)
        bot_info = await bot.get_me()
        print(f"✅ البوت متصل: @{bot_info.username}")
        print(f"   📝 الاسم: {bot_info.first_name}")
        print(f"   🆔 المعرف: {bot_info.id}")
    except Exception as e:
        print(f"❌ فشل في الاتصال بالبوت: {e}")
        return False
    
    # 2. اختبار القناة
    print("\n2️⃣ اختبار القناة...")
    channel_id = BotConfig.TELEGRAM_CHANNEL_ID
    try:
        chat_info = await bot.get_chat(channel_id)
        print(f"✅ القناة متاحة: {chat_info.title}")
        print(f"   🆔 المعرف: {channel_id}")
        print(f"   👥 النوع: {chat_info.type}")
        
        # اختبار صلاحيات النشر
        try:
            await bot.send_chat_action(chat_id=channel_id, action="typing")
            print("✅ البوت لديه صلاحية النشر")
        except Exception as perm_error:
            print(f"❌ البوت لا يملك صلاحية النشر: {perm_error}")
            return False
            
    except Exception as e:
        print(f"❌ مشكلة في القناة: {e}")
        if "Chat not found" in str(e):
            print("💡 تأكد من صحة معرف القناة وإضافة البوت كمدير")
        return False
    
    # 3. اختبار معرف المدير
    print("\n3️⃣ اختبار معرف المدير...")
    admin_id = BotConfig.TELEGRAM_ADMIN_ID
    print(f"   🆔 معرف المدير الحالي: {admin_id}")
    
    if admin_id.startswith('@'):
        print("⚠️ المعرف يبدأ بـ @ (اسم مستخدم)")
        print("💡 يُفضل استخدام المعرف الرقمي")
        
        # محاولة الحصول على معرف رقمي
        print("🔍 البحث عن معرف رقمي...")
        updates = await bot.get_updates(limit=10)
        
        if updates:
            for update in reversed(updates):
                if update.message and update.message.from_user:
                    chat_id = update.message.chat_id
                    username = update.message.from_user.username
                    
                    if username and f"@{username}" == admin_id:
                        print(f"✅ تم العثور على المعرف الرقمي: {chat_id}")
                        
                        # اختبار إرسال رسالة
                        try:
                            test_message = "🧪 اختبار إرسال رسالة للمدير"
                            await bot.send_message(chat_id=chat_id, text=test_message)
                            print("✅ تم إرسال رسالة اختبار للمدير بنجاح")
                            break
                        except Exception as send_error:
                            print(f"❌ فشل في إرسال رسالة للمدير: {send_error}")
                            
        else:
            print("⚠️ لا توجد رسائل حديثة للبحث عن المعرف")
    else:
        # اختبار المعرف الرقمي
        try:
            await bot.send_chat_action(chat_id=admin_id, action="typing")
            print("✅ معرف المدير صالح")
            
            # اختبار إرسال رسالة
            test_message = "🧪 اختبار إرسال رسالة للمدير"
            await bot.send_message(chat_id=admin_id, text=test_message)
            print("✅ تم إرسال رسالة اختبار للمدير بنجاح")
            
        except Exception as admin_error:
            print(f"❌ مشكلة في معرف المدير: {admin_error}")
            if "Chat not found" in str(admin_error):
                print("💡 المدير لم يرسل رسالة للبوت من قبل")
    
    # 4. اختبار النشر في القناة
    print("\n4️⃣ اختبار النشر في القناة...")
    try:
        test_article_message = """🧪 <b>رسالة اختبار من وكيل أخبار الألعاب</b>

🎮 هذه رسالة اختبار للتأكد من عمل النظام بشكل صحيح

✅ إذا ظهرت هذه الرسالة، فإن النظام يعمل بشكل مثالي!

<i>سيتم حذف هذه الرسالة تلقائياً بعد دقيقة</i>"""
        
        message = await bot.send_message(
            chat_id=channel_id,
            text=test_article_message,
            parse_mode='HTML'
        )
        
        print(f"✅ تم نشر رسالة اختبار في القناة")
        print(f"   🆔 معرف الرسالة: {message.message_id}")
        
        # حذف الرسالة بعد 30 ثانية
        await asyncio.sleep(30)
        try:
            await bot.delete_message(chat_id=channel_id, message_id=message.message_id)
            print("✅ تم حذف رسالة الاختبار")
        except:
            print("⚠️ لم يتم حذف رسالة الاختبار (قد تحتاج حذفها يدوياً)")
            
    except Exception as publish_error:
        print(f"❌ فشل في النشر: {publish_error}")
        return False
    
    # 5. اختبار نظام الموافقة
    print("\n5️⃣ اختبار نظام الموافقة...")
    try:
        from modules.video_approval_system import VideoApprovalSystem
        approval_system = VideoApprovalSystem()
        
        if approval_system.approval_enabled:
            print("✅ نظام الموافقة مفعل")
        else:
            print("⚠️ نظام الموافقة غير مفعل")
            
    except Exception as approval_error:
        print(f"❌ مشكلة في نظام الموافقة: {approval_error}")
    
    print("\n" + "=" * 60)
    print("✅ اكتمل الاختبار الشامل بنجاح!")
    print("🎉 نظام تيليجرام يعمل بشكل صحيح")
    return True

async def main():
    """الدالة الرئيسية"""
    success = await test_telegram_system()
    
    if success:
        print("\n💡 النظام جاهز للعمل!")
        print("🚀 يمكنك الآن تشغيل: python main.py")
    else:
        print("\n❌ يوجد مشاكل تحتاج إصلاح")
        print("🔧 شغل: python fix_admin_id.py لإصلاح معرف المدير")
        print("🔧 أو شغل: python get_admin_id.py للحصول على المعرف الصحيح")

if __name__ == "__main__":
    asyncio.run(main())
