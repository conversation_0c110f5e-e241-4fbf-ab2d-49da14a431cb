#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لإصلاحات Whisper
"""

import asyncio
from datetime import datetime
from modules.whisper_quality_checker import WhisperQualityChecker
from modules.advanced_text_analyzer import AdvancedTextAnalyzer
from modules.logger import logger

async def test_whisper_quality_checker():
    """اختبار نظام فحص جودة Whisper"""
    
    print("🔍 اختبار نظام فحص جودة Whisper...")
    print("=" * 50)
    
    checker = WhisperQualityChecker()
    
    # نصوص اختبار مختلفة
    test_texts = [
        {
            'name': 'نص جيد',
            'text': 'This is a great gaming video about the latest game releases. The player shows amazing skills in this new adventure game.',
            'video_data': {'id': 'test1', 'title': 'Gaming Video Test'}
        },
        {
            'name': 'نص قصير',
            'text': 'Short text',
            'video_data': {'id': 'test2', 'title': 'Short Video'}
        },
        {
            'name': 'نص فارغ',
            'text': '',
            'video_data': {'id': 'test3', 'title': 'Empty Video'}
        }
    ]
    
    success_count = 0
    
    for i, test in enumerate(test_texts, 1):
        print(f"\n   {i}. {test['name']}:")
        
        try:
            result = await checker.check_transcript_quality(test['text'], test['video_data'])
            
            if result:
                score = result.get('score', 0)
                level = result.get('quality_level', 'غير محدد')
                acceptable = result.get('is_acceptable', False)
                
                print(f"      ✅ النقاط: {score:.1f}/100")
                print(f"      📊 المستوى: {level}")
                print(f"      🎯 مقبول: {'نعم' if acceptable else 'لا'}")
                
                success_count += 1
            else:
                print("      ❌ فشل في الفحص")
                
        except Exception as e:
            print(f"      ❌ خطأ: {e}")
    
    print(f"\n📊 النتائج: {success_count}/{len(test_texts)} اختبار نجح")
    return success_count == len(test_texts)

def test_database_logging():
    """اختبار تسجيل قاعدة البيانات"""
    
    print("\n💾 اختبار تسجيل قاعدة البيانات...")
    print("=" * 50)
    
    from modules.database import db
    
    # بيانات اختبار متنوعة
    test_cases = [
        {
            'name': 'بيانات كاملة',
            'data': {
                'timestamp': datetime.now().isoformat(),
                'quality_score': 95.5,
                'quality_level': 'ممتاز',
                'is_acceptable': True,
                'video_id': 'complete_test',
                'video_title': 'Complete Test Video'
            }
        },
        {
            'name': 'بيانات أساسية فقط',
            'data': {
                'quality_score': 60.0,
                'quality_level': 'مقبول',
                'is_acceptable': True
            }
        },
        {
            'name': 'قيم حدية',
            'data': {
                'quality_score': 0.0,
                'quality_level': 'سيء جداً',
                'is_acceptable': False,
                'video_id': '',
                'video_title': None
            }
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n   {i}. {test_case['name']}:")
        
        try:
            result = db.log_whisper_quality_check(test_case['data'])
            
            if result:
                print("      ✅ تم التسجيل بنجاح")
                success_count += 1
            else:
                print("      ❌ فشل في التسجيل")
                
        except Exception as e:
            print(f"      ❌ خطأ: {e}")
    
    print(f"\n📊 النتائج: {success_count}/{len(test_cases)} حالة نجحت")
    return success_count == len(test_cases)

async def test_integration():
    """اختبار التكامل الكامل"""
    
    print("\n🔗 اختبار التكامل الكامل...")
    print("=" * 50)
    
    try:
        # محاكاة عملية كاملة
        checker = WhisperQualityChecker()
        
        test_text = "Welcome to this amazing gaming tutorial. In this video, we will explore the new features of the latest action RPG game."
        video_data = {
            'id': 'integration_test_video',
            'title': 'Gaming Tutorial - New RPG Features'
        }
        
        print("   1. فحص جودة Whisper...")
        quality_result = await checker.check_transcript_quality(test_text, video_data)
        
        if quality_result:
            score = quality_result.get('score', 0)
            level = quality_result.get('quality_level', 'غير محدد')
            acceptable = quality_result.get('is_acceptable', False)
            
            print(f"      ✅ النقاط: {score:.1f}/100")
            print(f"      📊 المستوى: {level}")
            print(f"      🎯 مقبول: {'نعم' if acceptable else 'لا'}")
            
            return True
        else:
            print("      ❌ فشل في الفحص")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التكامل: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار شامل لإصلاحات Whisper")
    print("=" * 60)
    
    tests = [
        ("فحص جودة Whisper", test_whisper_quality_checker()),
        ("تسجيل قاعدة البيانات", test_database_logging()),
        ("التكامل الكامل", test_integration())
    ]
    
    results = []
    
    for test_name, test_coro in tests:
        print(f"\n🔍 تشغيل اختبار: {test_name}")
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print("-" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 الإجمالي: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! نظام Whisper يعمل بشكل مثالي")
    else:
        print("⚠️ بعض الاختبارات فشلت، راجع التفاصيل أعلاه")

if __name__ == "__main__":
    asyncio.run(main())
