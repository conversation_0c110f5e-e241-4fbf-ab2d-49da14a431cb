#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام إنشاء الصور اليدوية
Test Manual Image Generation System
"""

import asyncio
import os
import sys
import json
from datetime import datetime
from typing import Dict, List

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.manual_image_generator import ManualImageGenerator
from modules.logger import logger

class ManualImageSystemTester:
    """فئة اختبار نظام إنشاء الصور اليدوية"""
    
    def __init__(self):
        self.generator = ManualImageGenerator("Gaming News Test")
        self.test_results = []
        self.output_dir = "test_results/manual_images"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # مقالات اختبار متنوعة
        self.test_articles = [
            {
                'title': 'PlayStation 5 Pro يحصل على تحديث جديد يحسن الأداء',
                'content': 'سوني تطلق تحديث جديد لجهاز PlayStation 5 Pro يحسن من أداء الألعاب ويضيف ميزات جديدة',
                'expected_theme': 'playstation'
            },
            {
                'title': 'Nintendo Switch 2 Announcement Coming Soon',
                'content': 'Nintendo is preparing to announce the next generation Switch console with improved graphics and performance',
                'expected_theme': 'nintendo'
            },
            {
                'title': 'Xbox Game Pass Ultimate Gets New Features',
                'content': 'Microsoft announces new features for Xbox Game Pass Ultimate including cloud gaming improvements',
                'expected_theme': 'xbox'
            },
            {
                'title': 'أفضل ألعاب الكمبيوتر لعام 2024',
                'content': 'قائمة بأفضل ألعاب الكمبيوتر الشخصي التي صدرت في عام 2024 مع مراجعات شاملة',
                'expected_theme': 'pc_gaming'
            },
            {
                'title': 'Mobile Gaming Revenue Reaches New Heights',
                'content': 'Mobile gaming industry continues to grow with record-breaking revenue in 2024',
                'expected_theme': 'mobile_gaming'
            },
            {
                'title': 'بطولة الرياضات الإلكترونية العالمية 2024',
                'content': 'انطلاق بطولة الرياضات الإلكترونية العالمية بجوائز تصل إلى مليون دولار',
                'expected_theme': 'esports'
            },
            {
                'title': 'Gaming Industry News and Updates',
                'content': 'Latest news and updates from the gaming industry including new releases and announcements',
                'expected_theme': 'general'
            }
        ]
    
    async def test_theme_detection(self) -> Dict:
        """اختبار تحديد المواضيع"""
        logger.info("🎯 اختبار تحديد المواضيع...")
        
        results = {
            'total_tests': len(self.test_articles),
            'correct_detections': 0,
            'incorrect_detections': 0,
            'details': []
        }
        
        for article in self.test_articles:
            detected_theme = self.generator.detect_theme_from_article(article)
            expected_theme = article['expected_theme']
            is_correct = detected_theme == expected_theme
            
            if is_correct:
                results['correct_detections'] += 1
            else:
                results['incorrect_detections'] += 1
            
            results['details'].append({
                'title': article['title'],
                'expected': expected_theme,
                'detected': detected_theme,
                'correct': is_correct
            })
            
            logger.info(f"📝 {article['title'][:30]}... -> {detected_theme} ({'✅' if is_correct else '❌'})")
        
        accuracy = (results['correct_detections'] / results['total_tests']) * 100
        logger.info(f"🎯 دقة تحديد المواضيع: {accuracy:.1f}%")
        
        return results
    
    async def test_language_detection(self) -> Dict:
        """اختبار تحديد اللغة"""
        logger.info("🌐 اختبار تحديد اللغة...")
        
        test_texts = [
            {'text': 'PlayStation 5 Pro Gaming Console', 'expected': 'english'},
            {'text': 'أفضل ألعاب الكمبيوتر لعام 2024', 'expected': 'arabic'},
            {'text': 'Nintendo Switch 2 Announcement', 'expected': 'english'},
            {'text': 'بطولة الرياضات الإلكترونية العالمية', 'expected': 'arabic'},
            {'text': 'Xbox Game Pass Ultimate Features', 'expected': 'english'},
            {'text': 'تحديث جديد لجهاز PlayStation', 'expected': 'arabic'},
            {'text': 'Gaming News and Updates 2024', 'expected': 'english'}
        ]
        
        results = {
            'total_tests': len(test_texts),
            'correct_detections': 0,
            'incorrect_detections': 0,
            'details': []
        }
        
        for test in test_texts:
            detected_lang = self.generator.detect_text_language(test['text'])
            expected_lang = test['expected']
            is_correct = detected_lang == expected_lang
            
            if is_correct:
                results['correct_detections'] += 1
            else:
                results['incorrect_detections'] += 1
            
            results['details'].append({
                'text': test['text'],
                'expected': expected_lang,
                'detected': detected_lang,
                'correct': is_correct
            })
            
            logger.info(f"🌐 '{test['text'][:30]}...' -> {detected_lang} ({'✅' if is_correct else '❌'})")
        
        accuracy = (results['correct_detections'] / results['total_tests']) * 100
        logger.info(f"🌐 دقة تحديد اللغة: {accuracy:.1f}%")
        
        return results
    
    async def test_text_enhancement(self) -> Dict:
        """اختبار تحسين النصوص"""
        logger.info("✨ اختبار تحسين النصوص...")
        
        test_cases = [
            {
                'original': 'playstation 5 pro gaming console',
                'language': 'english',
                'expected_improvements': ['title_case', 'gaming_emoji']
            },
            {
                'original': 'أفضل ألعاب الكمبيوتر',
                'language': 'arabic',
                'expected_improvements': ['sparkle_emoji']
            },
            {
                'original': 'nintendo switch announcement',
                'language': 'english',
                'expected_improvements': ['title_case', 'gaming_emoji']
            }
        ]
        
        results = {
            'total_tests': len(test_cases),
            'successful_enhancements': 0,
            'details': []
        }
        
        for test in test_cases:
            enhanced = self.generator.enhance_text_for_display(test['original'], test['language'])
            
            # فحص التحسينات
            improvements_found = []
            if test['language'] == 'english' and enhanced != test['original'].lower():
                improvements_found.append('title_case')
            if '🎮' in enhanced or '✨' in enhanced:
                improvements_found.append('emoji_added')
            
            is_enhanced = len(improvements_found) > 0
            if is_enhanced:
                results['successful_enhancements'] += 1
            
            results['details'].append({
                'original': test['original'],
                'enhanced': enhanced,
                'language': test['language'],
                'improvements': improvements_found,
                'enhanced': is_enhanced
            })
            
            logger.info(f"✨ '{test['original']}' -> '{enhanced}' ({'✅' if is_enhanced else '❌'})")
        
        return results
    
    async def test_image_generation(self) -> Dict:
        """اختبار إنشاء الصور الفعلي"""
        logger.info("🎨 اختبار إنشاء الصور الفعلي...")
        
        results = {
            'total_tests': len(self.test_articles),
            'successful_generations': 0,
            'failed_generations': 0,
            'details': []
        }
        
        for i, article in enumerate(self.test_articles):
            logger.info(f"🎨 اختبار {i+1}/{len(self.test_articles)}: {article['title'][:40]}...")
            
            try:
                image_result = await self.generator.generate_manual_image(article)
                
                if image_result:
                    results['successful_generations'] += 1
                    
                    # فحص الملف المُنشأ
                    file_exists = os.path.exists(image_result['local_path'])
                    file_size = os.path.getsize(image_result['local_path']) if file_exists else 0
                    
                    results['details'].append({
                        'article_title': article['title'],
                        'success': True,
                        'filename': image_result['filename'],
                        'file_exists': file_exists,
                        'file_size_kb': round(file_size / 1024, 2),
                        'theme': image_result.get('theme', 'unknown'),
                        'generation_method': image_result.get('generation_method', 'unknown')
                    })
                    
                    logger.info(f"✅ تم إنشاء الصورة بنجاح: {image_result['filename']} ({file_size/1024:.1f} KB)")
                    
                else:
                    results['failed_generations'] += 1
                    results['details'].append({
                        'article_title': article['title'],
                        'success': False,
                        'error': 'No image result returned'
                    })
                    logger.error(f"❌ فشل في إنشاء الصورة للمقال: {article['title']}")
                    
            except Exception as e:
                results['failed_generations'] += 1
                results['details'].append({
                    'article_title': article['title'],
                    'success': False,
                    'error': str(e)
                })
                logger.error(f"❌ خطأ في إنشاء الصورة: {e}")
        
        success_rate = (results['successful_generations'] / results['total_tests']) * 100
        logger.info(f"🎨 معدل نجاح إنشاء الصور: {success_rate:.1f}%")
        
        return results

    async def test_watermark_functionality(self) -> Dict:
        """اختبار وظيفة العلامة المائية"""
        logger.info("🏷️ اختبار وظيفة العلامة المائية...")

        # اختبار تغيير اسم الموقع
        original_name = self.generator.website_name
        test_names = ["Test Gaming Site", "موقع الألعاب العربي", "Gaming News Pro"]

        results = {
            'total_tests': len(test_names),
            'successful_changes': 0,
            'details': []
        }

        for test_name in test_names:
            try:
                self.generator.set_website_name(test_name)
                current_name = self.generator.website_name

                success = (current_name == test_name)
                if success:
                    results['successful_changes'] += 1

                results['details'].append({
                    'test_name': test_name,
                    'current_name': current_name,
                    'success': success
                })

                logger.info(f"🏷️ تغيير اسم الموقع إلى '{test_name}': {'✅' if success else '❌'}")

            except Exception as e:
                results['details'].append({
                    'test_name': test_name,
                    'error': str(e),
                    'success': False
                })
                logger.error(f"❌ خطأ في تغيير اسم الموقع: {e}")

        # إعادة الاسم الأصلي
        self.generator.set_website_name(original_name)

        return results

    async def test_font_loading(self) -> Dict:
        """اختبار تحميل الخطوط"""
        logger.info("🔤 اختبار تحميل الخطوط...")

        font_tests = [
            {'size': 24, 'bold': False, 'arabic_support': True},
            {'size': 48, 'bold': True, 'arabic_support': True},
            {'size': 32, 'bold': False, 'arabic_support': False},
            {'size': 16, 'bold': True, 'arabic_support': False}
        ]

        results = {
            'total_tests': len(font_tests),
            'successful_loads': 0,
            'details': []
        }

        for test in font_tests:
            try:
                font = self.generator.get_font(
                    test['size'],
                    test['bold'],
                    test['arabic_support']
                )

                success = font is not None
                if success:
                    results['successful_loads'] += 1

                results['details'].append({
                    'size': test['size'],
                    'bold': test['bold'],
                    'arabic_support': test['arabic_support'],
                    'success': success,
                    'font_type': type(font).__name__
                })

                logger.info(f"🔤 تحميل خط (حجم: {test['size']}, عربي: {test['arabic_support']}): {'✅' if success else '❌'}")

            except Exception as e:
                results['details'].append({
                    'size': test['size'],
                    'bold': test['bold'],
                    'arabic_support': test['arabic_support'],
                    'success': False,
                    'error': str(e)
                })
                logger.error(f"❌ خطأ في تحميل الخط: {e}")

        return results

    async def run_comprehensive_test(self) -> Dict:
        """تشغيل الاختبار الشامل"""
        logger.info("🚀 بدء الاختبار الشامل لنظام إنشاء الصور اليدوية...")
        logger.info("=" * 60)

        start_time = datetime.now()

        # تشغيل جميع الاختبارات
        test_results = {
            'start_time': start_time.isoformat(),
            'theme_detection': await self.test_theme_detection(),
            'language_detection': await self.test_language_detection(),
            'text_enhancement': await self.test_text_enhancement(),
            'watermark_functionality': await self.test_watermark_functionality(),
            'font_loading': await self.test_font_loading(),
            'image_generation': await self.test_image_generation()
        }

        end_time = datetime.now()
        test_results['end_time'] = end_time.isoformat()
        test_results['duration_seconds'] = (end_time - start_time).total_seconds()

        # حساب الإحصائيات العامة
        total_tests = sum([
            test_results['theme_detection']['total_tests'],
            test_results['language_detection']['total_tests'],
            test_results['text_enhancement']['total_tests'],
            test_results['watermark_functionality']['total_tests'],
            test_results['font_loading']['total_tests'],
            test_results['image_generation']['total_tests']
        ])

        successful_tests = sum([
            test_results['theme_detection']['correct_detections'],
            test_results['language_detection']['correct_detections'],
            test_results['text_enhancement']['successful_enhancements'],
            test_results['watermark_functionality']['successful_changes'],
            test_results['font_loading']['successful_loads'],
            test_results['image_generation']['successful_generations']
        ])

        overall_success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0

        test_results['summary'] = {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'failed_tests': total_tests - successful_tests,
            'overall_success_rate': round(overall_success_rate, 2),
            'duration_minutes': round(test_results['duration_seconds'] / 60, 2)
        }

        # حفظ النتائج
        results_file = os.path.join(self.output_dir, f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)

        # طباعة الملخص
        logger.info("=" * 60)
        logger.info("📊 ملخص نتائج الاختبار الشامل:")
        logger.info(f"⏱️ المدة الإجمالية: {test_results['summary']['duration_minutes']} دقيقة")
        logger.info(f"📈 إجمالي الاختبارات: {test_results['summary']['total_tests']}")
        logger.info(f"✅ الاختبارات الناجحة: {test_results['summary']['successful_tests']}")
        logger.info(f"❌ الاختبارات الفاشلة: {test_results['summary']['failed_tests']}")
        logger.info(f"🎯 معدل النجاح الإجمالي: {test_results['summary']['overall_success_rate']}%")
        logger.info(f"💾 تم حفظ النتائج في: {results_file}")
        logger.info("=" * 60)

        return test_results

    def print_detailed_results(self, results: Dict):
        """طباعة النتائج التفصيلية"""
        logger.info("📋 النتائج التفصيلية:")

        for test_name, test_data in results.items():
            if test_name in ['start_time', 'end_time', 'duration_seconds', 'summary']:
                continue

            logger.info(f"\n🔍 {test_name}:")
            if 'details' in test_data:
                for detail in test_data['details'][:3]:  # عرض أول 3 نتائج فقط
                    logger.info(f"  - {detail}")
                if len(test_data['details']) > 3:
                    logger.info(f"  ... و {len(test_data['details']) - 3} نتيجة أخرى")

async def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🎮 بدء اختبار نظام إنشاء الصور اليدوية للألعاب")

        tester = ManualImageSystemTester()
        results = await tester.run_comprehensive_test()

        # طباعة النتائج التفصيلية
        tester.print_detailed_results(results)

        logger.info("🎉 تم الانتهاء من الاختبار الشامل بنجاح!")

        return results

    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الاختبار: {e}")
        return None

if __name__ == "__main__":
    # تشغيل الاختبار
    results = asyncio.run(main())
