# إعداد مفاتيح النماذج الاحتياطية للذكاء الاصطناعي
import os
import sys
from pathlib import Path

def setup_ai_fallback_keys():
    """إعداد تفاعلي لمفاتيح النماذج الاحتياطية"""
    
    print("🤖 إعداد مفاتيح النماذج الاحتياطية للذكاء الاصطناعي")
    print("=" * 60)
    print()
    
    # قراءة ملف .env الحالي
    env_file = Path(".env")
    env_content = ""
    
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            env_content = f.read()
        print("✅ تم العثور على ملف .env موجود")
    else:
        print("📝 سيتم إنشاء ملف .env جديد")
    
    print()
    
    # معلومات النماذج
    models_info = {
        'GEMINI_2_FLASH_API_KEY': {
            'name': 'Gemini 2.0 Flash',
            'description': 'مجاني مع بحث على الويب (500 طلب يومياً)',
            'priority': 1,
            'url': 'https://ai.google.dev/',
            'required': False
        },
        'DEEPSEEK_API_KEY': {
            'name': 'DeepSeek R1',
            'description': 'تفكير عميق مع بحث على الإنترنت',
            'priority': 2,
            'url': 'https://chat.deepseek.com/',
            'required': False
        },
        'GROQ_API_KEY': {
            'name': 'Groq API',
            'description': 'الأسرع في المعالجة',
            'priority': 3,
            'url': 'https://console.groq.com/',
            'required': False
        },
        'GEMINI_1_5_FLASH_API_KEY': {
            'name': 'Gemini 1.5 Flash',
            'description': 'الحل الاحتياطي النهائي المجاني',
            'priority': 4,
            'url': 'https://ai.google.dev/',
            'required': False
        }
    }
    
    # جمع المفاتيح من المستخدم
    new_keys = {}
    
    for key, info in models_info.items():
        print(f"🔑 {info['name']} (الأولوية {info['priority']})")
        print(f"   📝 الوصف: {info['description']}")
        print(f"   🌐 الموقع: {info['url']}")
        
        # فحص إذا كان المفتاح موجود بالفعل
        current_value = ""
        if f"{key}=" in env_content:
            # استخراج القيمة الحالية
            for line in env_content.split('\n'):
                if line.startswith(f"{key}="):
                    current_value = line.split('=', 1)[1].strip('"\'')
                    break
        
        if current_value:
            print(f"   ✅ المفتاح موجود: {current_value[:20]}...")
            update = input(f"   هل تريد تحديث المفتاح؟ (y/N): ").lower().strip()
            if update in ['y', 'yes', 'نعم']:
                new_key = input(f"   أدخل المفتاح الجديد لـ {info['name']}: ").strip()
                if new_key:
                    new_keys[key] = new_key
                    print(f"   ✅ تم تحديث المفتاح")
                else:
                    print(f"   ⚠️ تم تخطي التحديث")
            else:
                print(f"   ⏭️ تم الاحتفاظ بالمفتاح الحالي")
        else:
            new_key = input(f"   أدخل المفتاح لـ {info['name']} (اتركه فارغاً للتخطي): ").strip()
            if new_key:
                new_keys[key] = new_key
                print(f"   ✅ تم إضافة المفتاح")
            else:
                print(f"   ⏭️ تم تخطي {info['name']}")
        
        print()
    
    # تحديث ملف .env
    if new_keys:
        print("💾 تحديث ملف .env...")
        
        # إضافة التعليقات والمفاتيح الجديدة
        ai_fallback_section = "\n# مفاتيح النماذج الاحتياطية للذكاء الاصطناعي\n"
        
        for key, value in new_keys.items():
            info = models_info[key]
            ai_fallback_section += f"# {info['name']} - {info['description']}\n"
            ai_fallback_section += f"{key}={value}\n\n"
        
        # إزالة المفاتيح القديمة إذا وجدت
        lines = env_content.split('\n')
        filtered_lines = []
        
        for line in lines:
            # تخطي المفاتيح التي سيتم تحديثها
            skip_line = False
            for key in new_keys.keys():
                if line.startswith(f"{key}="):
                    skip_line = True
                    break
            
            if not skip_line:
                filtered_lines.append(line)
        
        # إضافة المحتوى الجديد
        updated_content = '\n'.join(filtered_lines).rstrip() + ai_fallback_section
        
        # كتابة الملف
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print(f"✅ تم تحديث ملف .env بـ {len(new_keys)} مفتاح جديد")
    else:
        print("ℹ️ لم يتم إضافة مفاتيح جديدة")
    
    print()
    
    # عرض ملخص الإعداد
    print("📊 ملخص الإعداد:")
    print("-" * 30)
    
    total_keys = 0
    configured_keys = 0
    
    # فحص جميع المفاتيح في الملف النهائي
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            final_content = f.read()
        
        for key, info in models_info.items():
            total_keys += 1
            has_key = False
            
            for line in final_content.split('\n'):
                if line.startswith(f"{key}=") and len(line.split('=', 1)[1].strip()) > 0:
                    has_key = True
                    configured_keys += 1
                    break
            
            status = "✅ مُعد" if has_key else "❌ غير مُعد"
            print(f"   {info['name']}: {status}")
    
    print(f"\nإجمالي النماذج المُعدة: {configured_keys}/{total_keys}")
    
    if configured_keys > 0:
        print(f"\n🎉 تم إعداد {configured_keys} نموذج بنجاح!")
        print("🚀 يمكنك الآن تشغيل الاختبار:")
        print("   python test_ai_fallback_system.py")
    else:
        print("\n⚠️ لم يتم إعداد أي نماذج")
        print("💡 يمكنك إعادة تشغيل هذا السكريبت لاحقاً لإضافة المفاتيح")
    
    print("\n📚 للمزيد من المعلومات، راجع:")
    print("   AI_FALLBACK_SYSTEM_README.md")

def show_model_info():
    """عرض معلومات مفصلة عن النماذج"""
    print("📖 معلومات النماذج الاحتياطية")
    print("=" * 60)
    print()
    
    models = [
        {
            'name': 'Gemini 2.0 Flash',
            'priority': 1,
            'features': ['بحث على الويب', 'مجاني', '500 طلب/يوم'],
            'best_for': 'البحث العام مع معلومات حديثة',
            'url': 'https://ai.google.dev/'
        },
        {
            'name': 'DeepSeek R1',
            'priority': 2,
            'features': ['تفكير عميق', 'بحث إنترنت', 'تحليل متقدم'],
            'best_for': 'التحليل العميق والمعقد',
            'url': 'https://chat.deepseek.com/'
        },
        {
            'name': 'Groq API',
            'priority': 3,
            'features': ['سرعة عالية', 'معالجة سريعة', 'استجابة فورية'],
            'best_for': 'الحالات التي تتطلب سرعة',
            'url': 'https://console.groq.com/'
        },
        {
            'name': 'Gemini 1.5 Flash',
            'priority': 4,
            'features': ['مجاني', 'موثوق', 'احتياطي نهائي'],
            'best_for': 'الحل الاحتياطي الأخير',
            'url': 'https://ai.google.dev/'
        }
    ]
    
    for model in models:
        print(f"🤖 {model['name']} (الأولوية {model['priority']})")
        print(f"   🎯 الأفضل لـ: {model['best_for']}")
        print(f"   ✨ الميزات: {', '.join(model['features'])}")
        print(f"   🌐 الموقع: {model['url']}")
        print()

def main():
    """الوظيفة الرئيسية"""
    if len(sys.argv) > 1 and sys.argv[1] == '--info':
        show_model_info()
    else:
        setup_ai_fallback_keys()

if __name__ == "__main__":
    main()
