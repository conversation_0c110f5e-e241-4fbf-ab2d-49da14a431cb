#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لميزة عرض النص المستخرج من Whisper
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer

async def test_whisper_with_display():
    """اختبار Whisper مع عرض النص بوضوح"""
    print("🎤 اختبار Whisper مع عرض النص المحسن")
    print("=" * 60)
    
    try:
        analyzer = AdvancedYouTubeAnalyzer()
        
        # الفيديو المقترح
        video_url = "https://youtu.be/Dvtswxb51K4?si=NB-sO4SIlpIKRAvx"
        video_id = "Dvtswxb51K4"
        
        print(f"🎥 الفيديو المختبر: {video_url}")
        print(f"🆔 معرف الفيديو: {video_id}")
        
        # الحصول على تفاصيل الفيديو أولاً
        print("\n🔍 الحصول على تفاصيل الفيديو...")
        video_details = await analyzer._get_video_details(video_id)
        
        if video_details:
            print(f"✅ تفاصيل الفيديو:")
            print(f"   📹 العنوان: {video_details.get('title', 'غير محدد')}")
            print(f"   📺 القناة: {video_details.get('channel_title', 'غير محدد')}")
            print(f"   ⏱️ المدة: {video_details.get('duration', 'غير محدد')} ثانية")
        
        # اختبار استخراج النص مع العرض المحسن
        print("\n🎤 بدء استخراج النص من Whisper...")
        print("سيتم عرض تفاصيل النص المستخرج بوضوح أدناه:")
        print("-" * 60)
        
        transcript = await analyzer.extract_video_transcript_with_whisper(video_id)
        
        if transcript:
            print(f"\n🎉 تم استخراج النص بنجاح!")
            print(f"📊 طول النص النهائي: {len(transcript):,} حرف")
            print(f"📝 عدد الكلمات: {len(transcript.split()):,} كلمة")
            
            # اختبار تقييم الجودة
            quality_score = analyzer._evaluate_transcript_quality(transcript)
            print(f"⭐ نقاط الجودة: {quality_score:.1f}/100")
            
            return True
        else:
            print("\n❌ فشل في استخراج النص")
            
            # اختبار الطرق البديلة
            print("\n🔄 محاولة الطرق البديلة...")
            
            # الترجمة المدمجة
            print("📺 اختبار الترجمة المدمجة...")
            embedded_transcript = await analyzer._extract_embedded_captions(video_id)
            if embedded_transcript:
                print("✅ تم العثور على ترجمة مدمجة")
                return True
            
            # الوصف المفصل
            print("📝 اختبار الوصف المفصل...")
            detailed_description = await analyzer._get_detailed_video_description(video_id)
            if detailed_description:
                print("✅ تم العثور على وصف مفصل")
                return True
            
            print("❌ فشلت جميع الطرق")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

async def test_display_function():
    """اختبار دالة العرض مع نص تجريبي"""
    print("\n🧪 اختبار دالة العرض مع نص تجريبي")
    print("=" * 50)
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    # نص تجريبي عربي
    test_text_arabic = """
    مرحباً بكم في هذا الفيديو الرائع عن الألعاب الجديدة.
    سنتحدث اليوم عن أحدث الألعاب التي صدرت هذا العام.
    هناك العديد من الألعاب المثيرة التي يجب أن تجربوها.
    لا تنسوا الاشتراك في القناة وتفعيل الجرس للحصول على آخر الأخبار.
    """
    
    # نص تجريبي إنجليزي
    test_text_english = """
    Welcome to this amazing video about new games.
    Today we will talk about the latest games released this year.
    There are many exciting games that you should try.
    Don't forget to subscribe to the channel and hit the bell for the latest news.
    """
    
    # نص مختلط
    test_text_mixed = """
    مرحباً Welcome to this gaming channel! 
    سنتحدث عن latest games والألعاب الجديدة.
    This video will cover both Arabic and English content.
    لا تنسوا الاشتراك subscribe to our channel!
    """
    
    print("📝 اختبار النص العربي:")
    analyzer._display_transcript_details(test_text_arabic, "نص تجريبي عربي")
    
    print("📝 اختبار النص الإنجليزي:")
    analyzer._display_transcript_details(test_text_english, "نص تجريبي إنجليزي")
    
    print("📝 اختبار النص المختلط:")
    analyzer._display_transcript_details(test_text_mixed, "نص تجريبي مختلط")
    
    # اختبار نص فارغ
    print("📝 اختبار النص الفارغ:")
    analyzer._display_transcript_details("", "نص فارغ")
    
    # اختبار نص قصير
    print("📝 اختبار النص القصير:")
    analyzer._display_transcript_details("نص قصير", "نص قصير جداً")

async def main():
    """الدالة الرئيسية"""
    print("🎤 اختبار شامل لميزة عرض النص من Whisper")
    print("=" * 70)
    
    # اختبار 1: دالة العرض مع نصوص تجريبية
    await test_display_function()
    
    # اختبار 2: Whisper الحقيقي مع الفيديو المقترح
    print("\n" + "="*70)
    success = await test_whisper_with_display()
    
    print("\n" + "="*70)
    if success:
        print("🎉 اكتمل الاختبار بنجاح! ميزة العرض تعمل بشكل صحيح")
    else:
        print("⚠️ الاختبار لم يكتمل بالكامل، لكن ميزة العرض تعمل")
    
    print("✅ تم اختبار جميع الميزات الجديدة")

if __name__ == "__main__":
    asyncio.run(main())
