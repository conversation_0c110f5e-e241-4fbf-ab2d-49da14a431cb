#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام البحث العميق باستخدام Gemini 2.5 Pro
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.gemini_deep_search import gemini_deep_search, SearchDepth
from modules.logger import logger

async def test_gemini_deep_search():
    """اختبار شامل لنظام البحث العميق Gemini 2.5 Pro"""
    
    print("\n" + "="*60)
    print("🧪 اختبار نظام البحث العميق Gemini 2.5 Pro")
    print("="*60)
    
    # فحص حالة النظام
    print(f"\n📊 حالة النظام:")
    print(f"   مفعل: {'✅ نعم' if gemini_deep_search.enabled else '❌ لا'}")
    print(f"   النموذج: {gemini_deep_search.model_name}")
    
    if not gemini_deep_search.enabled:
        print("❌ النظام غير مفعل - تحقق من مفتاح API")
        return
    
    # اختبارات متدرجة
    test_cases = [
        {
            'name': 'اختبار أساسي',
            'query': 'latest gaming news',
            'depth': SearchDepth.BASIC,
            'max_results': 3
        },
        {
            'name': 'اختبار متقدم',
            'query': 'new video game releases 2025',
            'depth': SearchDepth.ADVANCED,
            'max_results': 5
        },
        {
            'name': 'اختبار شامل',
            'query': 'PlayStation 5 games updates',
            'depth': SearchDepth.COMPREHENSIVE,
            'max_results': 3
        }
    ]
    
    results_summary = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 {i}. {test_case['name']}")
        print("-" * 40)
        print(f"   الاستعلام: {test_case['query']}")
        print(f"   العمق: {test_case['depth'].value}")
        print(f"   النتائج المطلوبة: {test_case['max_results']}")
        
        try:
            start_time = datetime.now()
            
            # تنفيذ البحث
            results = await gemini_deep_search.deep_search(
                query=test_case['query'],
                search_depth=test_case['depth'],
                max_results=test_case['max_results']
            )
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # تحليل النتائج
            if results:
                print(f"   ✅ نجح: {len(results)} نتيجة في {execution_time:.2f} ثانية")
                
                # عرض معاينة النتائج
                for j, result in enumerate(results[:2], 1):
                    print(f"      {j}. {result.title[:60]}...")
                    print(f"         الصلة: {result.relevance_score:.2f}")
                    print(f"         بحث ويب: {'✅' if result.has_web_search else '❌'}")
                    print(f"         المحتوى: {len(result.content)} حرف")
                
                # إحصائيات
                avg_relevance = sum(r.relevance_score for r in results) / len(results)
                web_search_count = sum(1 for r in results if r.has_web_search)
                
                test_summary = {
                    'test_name': test_case['name'],
                    'query': test_case['query'],
                    'success': True,
                    'results_count': len(results),
                    'execution_time': execution_time,
                    'average_relevance': round(avg_relevance, 2),
                    'web_search_detected': web_search_count,
                    'web_search_rate': round(web_search_count / len(results) * 100, 1)
                }
                
                print(f"      📊 متوسط الصلة: {avg_relevance:.2f}")
                print(f"      🌐 بحث ويب: {web_search_count}/{len(results)} ({web_search_count/len(results)*100:.1f}%)")
                
            else:
                print(f"   ❌ فشل: لم يتم العثور على نتائج")
                test_summary = {
                    'test_name': test_case['name'],
                    'query': test_case['query'],
                    'success': False,
                    'execution_time': execution_time,
                    'error': 'لم يتم العثور على نتائج'
                }
            
            results_summary.append(test_summary)
            
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
            results_summary.append({
                'test_name': test_case['name'],
                'query': test_case['query'],
                'success': False,
                'error': str(e)
            })
    
    # عرض الملخص النهائي
    print(f"\n📈 ملخص الاختبارات:")
    print("=" * 50)
    
    successful_tests = [r for r in results_summary if r.get('success')]
    total_tests = len(results_summary)
    success_rate = len(successful_tests) / total_tests * 100 if total_tests > 0 else 0
    
    print(f"إجمالي الاختبارات: {total_tests}")
    print(f"الاختبارات الناجحة: {len(successful_tests)}")
    print(f"معدل النجاح: {success_rate:.1f}%")
    
    if successful_tests:
        avg_time = sum(r.get('execution_time', 0) for r in successful_tests) / len(successful_tests)
        total_results = sum(r.get('results_count', 0) for r in successful_tests)
        avg_relevance = sum(r.get('average_relevance', 0) for r in successful_tests if r.get('average_relevance')) / len([r for r in successful_tests if r.get('average_relevance')])
        
        print(f"متوسط وقت التنفيذ: {avg_time:.2f} ثانية")
        print(f"إجمالي النتائج: {total_results}")
        print(f"متوسط الصلة: {avg_relevance:.2f}")
    
    # عرض إحصائيات النظام
    print(f"\n📊 إحصائيات النظام:")
    stats = gemini_deep_search.get_usage_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")

    # حفظ النتائج مع تحويل التاريخ إلى نص
    stats_for_json = stats.copy()
    if 'last_reset' in stats_for_json:
        stats_for_json['last_reset'] = stats_for_json['last_reset'].isoformat()

    report = {
        'timestamp': datetime.now().isoformat(),
        'system_info': {
            'enabled': gemini_deep_search.enabled,
            'model': gemini_deep_search.model_name
        },
        'test_results': results_summary,
        'summary': {
            'total_tests': total_tests,
            'successful_tests': len(successful_tests),
            'success_rate': success_rate,
            'average_execution_time': avg_time if successful_tests else 0,
            'total_results_found': total_results if successful_tests else 0
        },
        'system_stats': stats_for_json
    }
    
    report_file = f"gemini_deep_search_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ التقرير في: {report_file}")

async def test_search_capability():
    """اختبار سريع لقدرة البحث"""
    print("\n🚀 اختبار سريع لقدرة البحث...")
    
    result = await gemini_deep_search.test_search_capability("latest PlayStation news")
    
    print(f"النتيجة: {'✅ نجح' if result.get('success') else '❌ فشل'}")
    if result.get('success'):
        print(f"وقت التنفيذ: {result.get('execution_time')} ثانية")
        print(f"عدد النتائج: {result.get('results_count')}")
        print(f"بحث ويب: {'✅' if result.get('web_search_detected') else '❌'}")
        
        if result.get('sample_result'):
            sample = result['sample_result']
            print(f"مثال: {sample['title'][:50]}...")
            print(f"الصلة: {sample['relevance_score']}")

async def main():
    """الدالة الرئيسية"""
    try:
        # اختبار سريع أولاً
        await test_search_capability()
        
        # اختبار شامل
        await test_gemini_deep_search()
        
        print("\n🎉 اكتملت جميع الاختبارات!")
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في التشغيل: {e}")

if __name__ == "__main__":
    asyncio.run(main())
