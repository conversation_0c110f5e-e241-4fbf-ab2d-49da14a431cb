#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تحليل صور الألعاب المحدث بدون تخزين مؤقت
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.game_image_analyzer import game_image_analyzer
from modules.logger import logger

async def test_fresh_analysis():
    """اختبار تحليل جديد بدون تخزين مؤقت"""
    
    print("\n" + "="*80)
    print("🎮 اختبار نظام تحليل صور الألعاب المحسن (بدون تخزين مؤقت)")
    print("="*80)
    
    # مسح التخزين المؤقت للاختبار
    game_image_analyzer.analysis_cache.clear()
    
    # مقالات اختبار جديدة
    test_articles = [
        {
            'title': 'Elden Ring: Shadow of the Erdtree - مراجعة التوسعة الجديدة',
            'content': 'تقدم FromSoftware توسعة رائعة للعبة Elden Ring تحمل اسم Shadow of the Erdtree. تتميز هذه التوسعة بعالم مظلم وغامض مليء بالوحوش والتحديات. الجرافيك مذهل مع إضاءة درامية وألوان داكنة مع لمسات ذهبية. تصميم الشخصيات معقد ومفصل مع دروع وأسلحة متنوعة.',
            'keywords': ['elden ring', 'fromsoftware', 'rpg', 'dark fantasy', 'souls', 'gaming']
        },
        {
            'title': 'Gran Turismo 7 - تحديث جديد يضيف سيارات كلاسيكية',
            'content': 'يحصل Gran Turismo 7 على تحديث جديد يضيف مجموعة من السيارات الكلاسيكية والحديثة. تتميز اللعبة بجرافيك واقعي للغاية مع تفاصيل دقيقة للسيارات والمضامير. الألوان زاهية ومشرقة مع تأثيرات الضوء والظل المتقدمة. تصميم المضامير واقعي مع بيئات متنوعة.',
            'keywords': ['gran turismo', 'racing', 'cars', 'simulation', 'playstation', 'gaming']
        },
        {
            'title': 'Hogwarts Legacy - عالم هاري بوتر في لعبة مفتوحة العالم',
            'content': 'تأخذنا Hogwarts Legacy إلى عالم هاري بوتر السحري في القرن التاسع عشر. تتميز اللعبة بأسلوب فني سحري مع ألوان دافئة وإضاءة ساحرة. القلعة مصممة بتفاصيل معمارية رائعة مع عناصر سحرية متوهجة. الشخصيات مصممة بأسلوب واقعي مع لمسات فانتازية.',
            'keywords': ['hogwarts legacy', 'harry potter', 'magic', 'rpg', 'open world', 'gaming']
        },
        {
            'title': 'Street Fighter 6 - عودة قوية لسلسلة القتال الأسطورية',
            'content': 'تعود سلسلة Street Fighter بجزء سادس مذهل يجمع بين الأسلوب الكلاسيكي والحداثة. تتميز اللعبة بأسلوب فني ملون وحيوي مع شخصيات مميزة وحركات قتالية متنوعة. الخلفيات مصممة بعناية مع ألوان زاهية وتأثيرات بصرية مثيرة.',
            'keywords': ['street fighter', 'fighting', 'capcom', 'arcade', 'combat', 'gaming']
        }
    ]
    
    print(f"🎮 تحليل {len(test_articles)} مقال جديد...")
    
    results = []
    
    for i, article in enumerate(test_articles, 1):
        print(f"\n🔍 {i}. تحليل مقال: {article['title'][:50]}...")
        print("-" * 60)
        
        try:
            analysis = await game_image_analyzer.analyze_game_for_image_generation(article)
            
            if analysis:
                results.append(analysis)
                
                print(f"✅ نجح التحليل - ثقة: {analysis['confidence_score']:.2f}")
                print(f"🎮 اللعبة: {analysis.get('game_name', 'Unknown')}")
                print(f"🎯 النوع: {analysis.get('visual_style', {}).get('genre', 'Unknown')}")
                print(f"🎨 النمط البصري: {analysis.get('visual_style', {}).get('visual_style', 'Unknown')[:100]}...")
                print(f"🌈 لوحة الألوان: {analysis.get('visual_style', {}).get('color_palette', 'Unknown')[:100]}...")
                print(f"📝 طريقة التحليل: {analysis.get('analysis_method', 'Unknown')}")
                
                if analysis.get('enhanced_prompt'):
                    print(f"💡 Prompt للإنشاء:")
                    print(f"   {analysis['enhanced_prompt'][:150]}...")
                
            else:
                print(f"❌ فشل التحليل")
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    # عرض الإحصائيات
    print(f"\n📊 ملخص النتائج:")
    print("=" * 60)
    
    successful = len(results)
    print(f"نجح: {successful}/{len(test_articles)}")
    
    if successful > 0:
        avg_confidence = sum(r['confidence_score'] for r in results) / successful
        print(f"متوسط الثقة: {avg_confidence:.2f}")
        
        # تحليل طرق التحليل المستخدمة
        methods = {}
        for r in results:
            method = r.get('analysis_method', 'unknown')
            methods[method] = methods.get(method, 0) + 1
        
        print(f"\n🔍 طرق التحليل المستخدمة:")
        for method, count in methods.items():
            print(f"   • {method}: {count} مرة")
        
        # تحليل الأنواع المكتشفة
        genres = {}
        for r in results:
            genre = r.get('visual_style', {}).get('genre', 'unknown')
            genres[genre] = genres.get(genre, 0) + 1
        
        print(f"\n🎯 أنواع الألعاب المكتشفة:")
        for genre, count in genres.items():
            print(f"   • {genre}: {count} مرة")
        
        # عرض أمثلة على الـ prompts المُنشأة
        print(f"\n💡 أمثلة على Prompts المُنشأة:")
        for i, r in enumerate(results[:2], 1):
            if r.get('enhanced_prompt'):
                print(f"   {i}. {r['game_name']}: {r['enhanced_prompt'][:100]}...")
    
    # عرض إحصائيات النظام
    print(f"\n📈 إحصائيات النظام:")
    stats = game_image_analyzer.get_usage_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # حفظ النتائج
    report = {
        'test_timestamp': datetime.now().isoformat(),
        'test_type': 'fresh_analysis_without_cache',
        'total_articles': len(test_articles),
        'successful_analyses': successful,
        'average_confidence': avg_confidence if successful > 0 else 0,
        'system_stats': stats,
        'detailed_results': [
            {
                'article_title': test_articles[i]['title'],
                'game_name': r.get('game_name', 'Unknown'),
                'confidence_score': r.get('confidence_score', 0),
                'analysis_method': r.get('analysis_method', 'Unknown'),
                'genre': r.get('visual_style', {}).get('genre', 'Unknown'),
                'visual_style': r.get('visual_style', {}).get('visual_style', 'Unknown')[:200],
                'color_palette': r.get('visual_style', {}).get('color_palette', 'Unknown')[:200],
                'enhanced_prompt': r.get('enhanced_prompt', '')[:300]
            }
            for i, r in enumerate(results)
        ]
    }
    
    report_file = f"fresh_game_analyzer_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ التقرير في: {report_file}")
    
    return results

async def demonstrate_style_extraction():
    """عرض قدرات استخراج الأنماط"""
    
    print(f"\n🎨 عرض قدرات استخراج الأنماط البصرية:")
    print("=" * 60)
    
    # مثال على مقال معقد
    complex_article = {
        'title': 'Baldur\'s Gate 3 - تحفة فنية في عالم الـ RPG',
        'content': '''
        تعتبر Baldur's Gate 3 من Larian Studios واحدة من أعظم ألعاب الـ RPG في التاريخ الحديث. 
        تتميز اللعبة بأسلوب فني خيالي مذهل يجمع بين الواقعية والفانتازيا بطريقة مبهرة.
        
        الألوان في اللعبة غنية ومتنوعة، مع استخدام ذكي للإضاءة الدرامية التي تخلق أجواء ملحمية.
        تصميم الشخصيات معقد ومفصل، مع تنوع كبير في الأعراق والفئات والأزياء.
        
        البيئات في اللعبة متنوعة من الغابات الخضراء إلى الأبراج المحصنة والمدن القديمة.
        كل منطقة لها طابعها البصري الخاص مع اهتمام بالتفاصيل الصغيرة.
        
        تأثيرات السحر والقدرات الخاصة مصممة بعناية مع ألوان متوهجة وحركات سلسة.
        واجهة المستخدم نظيفة وأنيقة مع عناصر تصميم تتماشى مع الطابع الفانتازي للعبة.
        ''',
        'keywords': ['baldurs gate 3', 'larian studios', 'rpg', 'fantasy', 'dungeons dragons', 'turn based', 'gaming']
    }
    
    print(f"📖 تحليل مقال معقد: {complex_article['title']}")
    
    analysis = await game_image_analyzer.analyze_game_for_image_generation(complex_article)
    
    if analysis:
        print(f"\n✅ تحليل مفصل:")
        print(f"🎮 اللعبة: {analysis.get('game_name', 'Unknown')}")
        print(f"🎯 النوع: {analysis.get('visual_style', {}).get('genre', 'Unknown')}")
        print(f"😊 المزاج: {analysis.get('visual_style', {}).get('mood', 'Unknown')}")
        print(f"🎨 الاتجاه الفني: {analysis.get('visual_style', {}).get('art_direction', 'Unknown')}")
        print(f"📝 طريقة التحليل: {analysis.get('analysis_method', 'Unknown')}")
        print(f"🎯 مستوى الثقة: {analysis.get('confidence_score', 0):.2f}")
        
        print(f"\n🎨 التفاصيل البصرية:")
        visual_style = analysis.get('visual_style', {})
        print(f"   النمط: {visual_style.get('visual_style', 'Unknown')}")
        print(f"   الألوان: {visual_style.get('color_palette', 'Unknown')}")
        print(f"   التكوين: {visual_style.get('composition', 'Unknown')}")
        print(f"   الشخصيات: {visual_style.get('characters', 'Unknown')}")
        
        if analysis.get('enhanced_prompt'):
            print(f"\n💡 Prompt محسن للإنشاء:")
            print(f"   {analysis['enhanced_prompt']}")
    else:
        print(f"❌ فشل في التحليل المعقد")

async def main():
    """الدالة الرئيسية"""
    try:
        results = await test_fresh_analysis()
        await demonstrate_style_extraction()
        
        print("\n🎉 اكتمل اختبار النظام المحسن!")
        
        if results:
            print(f"\n🏆 النتائج الإجمالية:")
            print(f"   • تم تحليل {len(results)} مقال بنجاح")
            print(f"   • متوسط الثقة: {sum(r['confidence_score'] for r in results) / len(results):.2f}")
            print(f"   • النظام يعمل بكفاءة عالية!")
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في التشغيل: {e}")

if __name__ == "__main__":
    asyncio.run(main())
