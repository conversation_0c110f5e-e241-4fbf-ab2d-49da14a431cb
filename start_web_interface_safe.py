#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل آمن لواجهة الويب بدون مشاكل
Safe Web Interface Launcher without issues
"""

import sys
import os
import time
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger


def start_web_interface_safe():
    """تشغيل واجهة الويب بطريقة آمنة"""
    try:
        logger.info("🚀 بدء تشغيل واجهة الويب الآمنة...")
        
        # استيراد Flask وواجهة الويب
        from flask import Flask
        import whisper_web_interface
        
        # تعطيل جميع ميزات التطوير المشكلة
        whisper_web_interface.app.config['DEBUG'] = False
        whisper_web_interface.app.config['TESTING'] = False
        
        logger.info("🌐 الواجهة متاحة على:")
        logger.info("   📱 http://localhost:5001")
        logger.info("   📱 http://127.0.0.1:5001")
        
        # تشغيل الخادم بدون debug mode
        whisper_web_interface.app.run(
            host='0.0.0.0',
            port=5001,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل واجهة الويب: {e}")
        return False
    
    return True


def test_web_interface():
    """اختبار واجهة الويب"""
    import time
    import requests
    
    logger.info("🧪 اختبار واجهة الويب...")
    
    # انتظار بدء الخادم
    max_attempts = 10
    for attempt in range(max_attempts):
        try:
            response = requests.get("http://localhost:5001", timeout=5)
            if response.status_code == 200:
                logger.info("✅ واجهة الويب تعمل بنجاح")
                return True
        except Exception:
            pass
        
        time.sleep(2)
        logger.info(f"⏳ انتظار بدء الخادم... ({attempt + 1}/{max_attempts})")
    
    logger.error("❌ فشل في الاتصال بواجهة الويب")
    return False


def display_interface_info():
    """عرض معلومات الواجهة"""
    logger.info("\n" + "🌐" * 50)
    logger.info("🌐 واجهة ويب تحويل الصوت إلى نص")
    logger.info("🌐" * 50)
    
    logger.info("📋 الميزات المتاحة:")
    logger.info("   📊 عرض جميع التحويلات")
    logger.info("   📈 إحصائيات مفصلة")
    logger.info("   🔍 فلترة وبحث")
    logger.info("   📤 تصدير البيانات")
    logger.info("   🧪 اختبار الاتصال")
    
    logger.info("\n🌐 الروابط:")
    logger.info("   📱 الواجهة الرئيسية: http://localhost:5001")
    logger.info("   🔧 API: http://localhost:5001/api/")
    logger.info("   📊 الإحصائيات: http://localhost:5001/api/stats")
    
    logger.info("\n📚 كيفية الاستخدام:")
    logger.info("   1. افتح المتصفح")
    logger.info("   2. انتقل إلى http://localhost:5001")
    logger.info("   3. راقب نتائج التحويل")
    logger.info("   4. صدّر البيانات عند الحاجة")
    
    logger.info("\n🌐" * 50)


def main():
    """الدالة الرئيسية"""
    logger.info("🌐 بدء تشغيل واجهة الويب الآمنة...")
    logger.info(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)
    
    try:
        # عرض معلومات الواجهة
        display_interface_info()
        
        # تشغيل واجهة الويب
        logger.info("\n🚀 تشغيل الخادم...")
        success = start_web_interface_safe()
        
        if success:
            logger.info("✅ تم تشغيل واجهة الويب بنجاح")
        else:
            logger.error("❌ فشل في تشغيل واجهة الويب")
        
        return success
        
    except KeyboardInterrupt:
        logger.info("\n🛑 تم إيقاف واجهة الويب بواسطة المستخدم")
        return True
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
