#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة إرسال النص المستخرج للمدير
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_test_header(test_name):
    """طباعة رأس الاختبار"""
    print(f"\n{'='*60}")
    print(f"🧪 اختبار: {test_name}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """طباعة نتيجة الاختبار"""
    status = "✅ نجح" if success else "❌ فشل"
    print(f"{status} {test_name}")
    if details:
        print(f"   📝 {details}")

async def test_text_formatting():
    """اختبار تنسيق النص المستخرج"""
    print_test_header("تنسيق النص المستخرج")
    
    try:
        from modules.video_approval_system import VideoApprovalSystem
        
        approval_system = VideoApprovalSystem()
        
        # بيانات فيديو اختبار
        test_video = {
            'id': 'test_video_123',
            'title': 'Star Citizen (Pyro) and It Took Forever!',
            'channel_info': {
                'name': 'LevelCap Gaming'
            }
        }
        
        # نص مستخرج اختبار
        test_text = """
        Welcome to another gaming video! Today we're exploring the new Pyro system in Star Citizen.
        This update brings massive improvements to the game including new planets, better graphics,
        and enhanced gameplay mechanics. The development team has been working hard on this update
        for over a year and it shows in the quality of the content.
        """
        
        # تنسيق النص
        formatted_text = approval_system._format_extracted_text(test_text, test_video)
        
        # فحص التنسيق
        has_title = test_video['title'] in formatted_text
        has_channel = test_video['channel_info']['name'] in formatted_text
        has_text_content = test_text.strip() in formatted_text
        has_html_tags = '<b>' in formatted_text and '<code>' in formatted_text
        
        print_result("تضمين عنوان الفيديو", has_title)
        print_result("تضمين اسم القناة", has_channel)
        print_result("تضمين النص المستخرج", has_text_content)
        print_result("تنسيق HTML", has_html_tags)
        
        # عرض عينة من النص المنسق
        print(f"\n📄 عينة من النص المنسق:")
        print(formatted_text[:300] + "..." if len(formatted_text) > 300 else formatted_text)
        
        return has_title and has_channel and has_text_content and has_html_tags
        
    except Exception as e:
        print_result("تنسيق النص المستخرج", False, f"خطأ: {str(e)}")
        return False

async def test_text_splitting():
    """اختبار تقسيم النص الطويل"""
    print_test_header("تقسيم النص الطويل")
    
    try:
        from modules.video_approval_system import VideoApprovalSystem
        
        approval_system = VideoApprovalSystem()
        
        # إنشاء نص طويل للاختبار
        long_text = "هذا نص طويل جداً. " * 500  # حوالي 5000 حرف
        
        # تقسيم النص
        parts = approval_system._split_text_into_parts(long_text, 1000)
        
        # فحص التقسيم
        has_multiple_parts = len(parts) > 1
        all_parts_within_limit = all(len(part) <= 1000 for part in parts)
        total_length_preserved = sum(len(part) for part in parts) >= len(long_text) * 0.9  # 90% على الأقل
        
        print_result("تقسيم النص الطويل", has_multiple_parts,
                    f"عدد الأجزاء: {len(parts)}")
        print_result("جميع الأجزاء ضمن الحد المسموح", all_parts_within_limit)
        print_result("الحفاظ على طول النص", total_length_preserved,
                    f"الطول الأصلي: {len(long_text)}, مجموع الأجزاء: {sum(len(part) for part in parts)}")
        
        # عرض معلومات التقسيم
        for i, part in enumerate(parts[:3]):  # أول 3 أجزاء فقط
            print(f"   الجزء {i+1}: {len(part)} حرف")
        
        return has_multiple_parts and all_parts_within_limit
        
    except Exception as e:
        print_result("تقسيم النص الطويل", False, f"خطأ: {str(e)}")
        return False

async def test_approval_with_text():
    """اختبار طلب الموافقة مع النص المستخرج"""
    print_test_header("طلب الموافقة مع النص")
    
    try:
        from modules.video_approval_system import VideoApprovalSystem
        
        approval_system = VideoApprovalSystem()
        
        # بيانات فيديو اختبار
        test_video = {
            'id': 'test_video_456',
            'title': 'Gaming News Update - January 2025',
            'duration': 480,
            'published_at': '2025-01-20T12:00:00Z',
            'channel_info': {
                'name': 'Gaming News Channel',
                'language': 'en'
            }
        }
        
        # نص مستخرج اختبار
        extracted_text = """
        Hello gamers! In today's video we're covering the latest gaming news.
        First, Microsoft announced a new update for Xbox Game Pass with 15 new games.
        Sony revealed the PlayStation 6 development roadmap for 2025.
        Nintendo teased a new Mario game coming this summer.
        """
        
        # متغير لتتبع نتيجة الموافقة
        approval_result = {'approved': None, 'reason': None}
        
        async def test_callback(approved: bool, reason: str):
            """callback اختبار"""
            approval_result['approved'] = approved
            approval_result['reason'] = reason
            print(f"📞 تم استدعاء callback: موافقة={approved}, السبب={reason}")
        
        # طلب الموافقة مع النص
        approval_id = await approval_system.request_video_approval(
            test_video, test_callback, extracted_text
        )
        
        # فحص النتائج
        has_approval_id = bool(approval_id)
        callback_called = approval_result['approved'] is not None
        
        print_result("إنشاء معرف الموافقة", has_approval_id,
                    f"المعرف: {approval_id}")
        print_result("استدعاء callback", callback_called,
                    f"النتيجة: {approval_result}")
        
        # فحص إذا تم حفظ النص مع الفيديو
        if approval_system.approval_enabled and approval_id in approval_system.pending_videos:
            saved_data = approval_system.pending_videos[approval_id]
            has_extracted_text = 'extracted_text' in saved_data
            text_matches = saved_data.get('extracted_text') == extracted_text
            
            print_result("حفظ النص المستخرج", has_extracted_text)
            print_result("تطابق النص المحفوظ", text_matches)
        else:
            print_result("الموافقة التلقائية", True, "النظام معطل - موافقة تلقائية")
        
        return has_approval_id and callback_called
        
    except Exception as e:
        print_result("طلب الموافقة مع النص", False, f"خطأ: {str(e)}")
        return False

async def test_main_integration():
    """اختبار التكامل مع main.py"""
    print_test_header("التكامل مع main.py")
    
    try:
        from main import GamingNewsBot
        
        bot = GamingNewsBot()
        
        # بيانات فيديو اختبار
        test_video = {
            'id': 'test_video_789',
            'title': 'Test Gaming Video',
            'duration': 600,
            'channel_info': {
                'name': 'Test Channel'
            }
        }
        
        test_text = "This is a test transcript from a gaming video."
        
        # اختبار دالة طلب الموافقة المحدثة
        approval_result = await bot._request_video_approval(test_video, test_text)
        
        # فحص النتائج
        has_result = isinstance(approval_result, dict)
        has_approved_key = 'approved' in approval_result if has_result else False
        has_reason_key = 'reason' in approval_result if has_result else False
        
        print_result("إرجاع نتيجة الموافقة", has_result)
        print_result("وجود مفتاح 'approved'", has_approved_key)
        print_result("وجود مفتاح 'reason'", has_reason_key)
        
        if has_result:
            print_result("نتيجة الموافقة", True,
                        f"موافقة: {approval_result.get('approved')}, السبب: {approval_result.get('reason')}")
        
        return has_result and has_approved_key and has_reason_key
        
    except Exception as e:
        print_result("التكامل مع main.py", False, f"خطأ: {str(e)}")
        return False

async def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار ميزة إرسال النص المستخرج...")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("تنسيق النص المستخرج", test_text_formatting),
        ("تقسيم النص الطويل", test_text_splitting),
        ("طلب الموافقة مع النص", test_approval_with_text),
        ("التكامل مع main.py", test_main_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print_result(test_name, False, f"خطأ غير متوقع: {str(e)}")
            results.append((test_name, False))
    
    # تقرير النتائج النهائية
    print_test_header("تقرير النتائج النهائية")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        print_result(test_name, result)
    
    print(f"\n📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 ميزة إرسال النص المستخرج تعمل بشكل مثالي!")
        print("📱 المدير @Yaasssssin سيحصل على:")
        print("   📄 النص المستخرج من الفيديو")
        print("   🎥 معلومات الفيديو")
        print("   ✅❌ أزرار الموافقة/الرفض")
    elif passed >= 3:
        print("✅ ميزة إرسال النص تعمل بشكل جيد مع بعض التحسينات")
    else:
        print("⚠️ الميزة تحتاج إصلاحات")
    
    return passed >= 3

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ حرج: {e}")
        sys.exit(1)
