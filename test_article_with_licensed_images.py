#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إنشاء مقال كامل مع الصور المرخصة
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.smart_image_manager import smart_image_manager

async def test_article_with_images(game_name: str):
    """اختبار إنشاء مقال مع صور مرخصة"""
    
    print(f"📰 اختبار إنشاء مقال عن: {game_name}")
    print("=" * 60)
    
    # إنشاء مقال تجريبي
    test_article = {
        'title': f'مراجعة شاملة للعبة {game_name} - تجربة استثنائية',
        'content': f'''
        تُعد لعبة {game_name} من أبرز الإصدارات الحديثة في عالم الألعاب، حيث تقدم 
        تجربة لعب فريدة تجمع بين الإثارة والتشويق والإبداع. اللعبة تتميز بجرافيك 
        مذهل وقصة مشوقة تأسر اللاعبين من اللحظة الأولى.
        
        من ناحية اللعب، تقدم {game_name} آليات لعب مبتكرة ومتنوعة تناسب جميع 
        أنواع اللاعبين. سواء كنت من محبي الألعاب الاستراتيجية أو ألعاب الحركة، 
        ستجد في هذه اللعبة ما يناسب ذوقك.
        
        الرسوميات في اللعبة على مستوى عالمي، مع تفاصيل دقيقة في البيئات والشخصيات 
        والتأثيرات البصرية. كما أن الموسيقى التصويرية والمؤثرات الصوتية تضيف 
        بُعداً إضافياً للتجربة الإجمالية.
        
        بشكل عام، {game_name} لعبة تستحق التجربة وتقدم ساعات طويلة من المتعة 
        والتسلية. ننصح بشدة بتجربتها لكل محبي الألعاب.
        ''',
        'keywords': [game_name, 'مراجعة', 'ألعاب', 'gaming', 'review', 'تقييم'],
        'content_type': 'مراجعة_لعبة',
        'published_date': datetime.now()
    }
    
    print(f"📝 تفاصيل المقال:")
    print(f"   • العنوان: {test_article['title']}")
    print(f"   • عدد الكلمات: {len(test_article['content'].split())}")
    print(f"   • الكلمات المفتاحية: {', '.join(test_article['keywords'])}")
    
    try:
        # اختبار Smart Image Manager
        print(f"\n🎨 اختبار إنشاء صورة ذكية...")
        
        # الحصول على إحصائيات قبل الاختبار
        stats_before = smart_image_manager.get_daily_stats()
        print(f"📊 الإحصائيات قبل الاختبار:")
        print(f"   • الصور المولدة اليوم: {stats_before['images_generated']}")
        print(f"   • الحصة المتبقية: {stats_before['remaining_quota']}")
        
        if 'licensed_images_rate' in stats_before:
            print(f"   • معدل الصور المرخصة: {stats_before['licensed_images_rate']:.1f}%")
        
        # محاولة إنشاء صورة
        print(f"\n🔄 جاري إنشاء صورة للمقال...")
        image_result = await smart_image_manager.generate_smart_image_for_article(test_article)
        
        if image_result:
            print(f"\n✅ تم إنشاء صورة بنجاح!")
            
            # تحليل نوع الصورة
            generation_method = image_result.get('generation_method', 'غير محدد')
            source = image_result.get('source', 'غير محدد')
            api_used = image_result.get('api_used', 'غير محدد')
            
            print(f"📸 تفاصيل الصورة:")
            print(f"   • المصدر: {source}")
            print(f"   • طريقة الإنشاء: {generation_method}")
            print(f"   • API المستخدم: {api_used}")
            print(f"   • آمنة لأدسنس: {image_result.get('safe_for_adsense', 'غير محدد')}")
            print(f"   • مجانية الاستخدام: {image_result.get('copyright_free', 'غير محدد')}")
            
            if 'attribution' in image_result:
                print(f"   • الإشارة المطلوبة: {image_result['attribution']}")
            
            print(f"   • الرابط: {image_result.get('url', 'غير متوفر')[:80]}...")
            
            # تحديد نوع الصورة المستخدمة
            if generation_method == 'licensed_official':
                print(f"\n🎯 ممتاز! تم استخدام صورة مرخصة رسمية من {source}")
                print(f"   ✅ هذا يعني أن الصورة آمنة قانونياً 100%")
                
                if 'game_name' in image_result:
                    print(f"   🎮 اللعبة المحددة: {image_result['game_name']}")
                if 'image_type' in image_result:
                    print(f"   📷 نوع الصورة: {image_result['image_type']}")
                    
            elif 'pollinations' in generation_method.lower():
                print(f"\n🤖 تم استخدام الذكاء الاصطناعي (Pollinations)")
                print(f"   ℹ️ لم يتم العثور على صور مرخصة، تم اللجوء للذكاء الاصطناعي")
                
            elif 'manual' in generation_method.lower():
                print(f"\n✋ تم استخدام النظام اليدوي")
                print(f"   ℹ️ فشل الذكاء الاصطناعي، تم إنشاء صورة يدوية")
                
            else:
                print(f"\n🔄 تم استخدام طريقة أخرى: {generation_method}")
            
        else:
            print(f"\n❌ فشل في إنشاء صورة للمقال")
            print(f"   ℹ️ قد يكون السبب:")
            print(f"      • تجاوز الحد اليومي للصور")
            print(f"      • جودة المقال منخفضة")
            print(f"      • مشاكل في الاتصال بـ APIs")
        
        # الحصول على إحصائيات بعد الاختبار
        stats_after = smart_image_manager.get_daily_stats()
        print(f"\n📊 الإحصائيات بعد الاختبار:")
        print(f"   • الصور المولدة اليوم: {stats_after['images_generated']}")
        print(f"   • الحصة المتبقية: {stats_after['remaining_quota']}")
        
        if 'licensed_images_rate' in stats_after:
            print(f"   • معدل الصور المرخصة: {stats_after['licensed_images_rate']:.1f}%")
        
        # عرض مصادر الصور إذا كانت متوفرة
        if 'image_sources' in stats_after:
            sources = stats_after['image_sources']
            print(f"\n📈 توزيع مصادر الصور اليوم:")
            print(f"   • صور مرخصة رسمية: {sources.get('licensed_official', 0)}")
            print(f"   • ذكاء اصطناعي: {sources.get('ai_generated', 0)}")
            print(f"   • يدوية احتياطية: {sources.get('manual_fallback', 0)}")
            
            total_images = sum(sources.values())
            if total_images > 0:
                licensed_percentage = (sources.get('licensed_official', 0) / total_images) * 100
                print(f"   • نسبة الصور المرخصة: {licensed_percentage:.1f}%")
        
        # معدل نجاح التخزين المؤقت
        if 'cache_hit_rate' in stats_after:
            print(f"   • معدل التخزين المؤقت: {stats_after['cache_hit_rate']:.1f}%")
        
    except Exception as e:
        print(f"\n❌ خطأ في اختبار الصورة: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """الدالة الرئيسية"""
    print("🎮 أداة اختبار المقالات مع الصور المرخصة")
    print("=" * 60)
    
    # ألعاب مختلفة للاختبار
    test_games = [
        "The Witcher 3: Wild Hunt",
        "Cyberpunk 2077",
        "League of Legends", 
        "Valorant",
        "Assassin's Creed Valhalla",
        "Call of Duty: Modern Warfare",
        "FIFA 2024",
        "Minecraft",
        "Fortnite",
        "Overwatch 2"
    ]
    
    print("\n🎯 اختر لعبة للاختبار:")
    for i, game in enumerate(test_games, 1):
        print(f"   {i:2d}. {game}")
    
    print(f"\n💡 أو اكتب اسم لعبة مخصص")
    
    try:
        user_input = input("\nأدخل رقم اللعبة أو اسم اللعبة: ").strip()
        
        if user_input.isdigit():
            choice = int(user_input)
            if 1 <= choice <= len(test_games):
                selected_game = test_games[choice - 1]
            else:
                print("❌ رقم غير صحيح")
                return
        else:
            selected_game = user_input
            if not selected_game:
                print("❌ لم يتم إدخال اسم اللعبة")
                return
        
        print(f"\n🚀 بدء اختبار المقال...")
        await test_article_with_images(selected_game)
        
        print(f"\n🎉 اكتمل اختبار المقال للعبة: {selected_game}")
        print("=" * 60)
        
        # نصائح للمستخدم
        print(f"\n💡 نصائح:")
        print(f"   • إذا تم استخدام صورة مرخصة: ممتاز! النظام يعمل بشكل مثالي")
        print(f"   • إذا تم استخدام الذكاء الاصطناعي: طبيعي، لم يتم العثور على صور مرخصة")
        print(f"   • تأكد من إعداد مفاتيح APIs للحصول على أفضل النتائج")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبار: {e}")

if __name__ == "__main__":
    asyncio.run(main())
