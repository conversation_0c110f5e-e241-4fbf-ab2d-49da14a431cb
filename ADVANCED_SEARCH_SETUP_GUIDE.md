# 🚀 دليل إعداد نظام البحث المتقدم مع البدائل المتعددة

## 📋 نظرة عامة

تم تطوير نظام بحث متقدم جديد يدعم **7 محركات بحث مختلفة** مع نظام **البدائل التلقائي** عند فشل أي محرك. النظام مصمم ليكون **مجاني 100%** ولا يتطلب أي دفع.

## 🔧 المحركات المدعومة (مرتبة حسب الأولوية)

### 1. 🥇 Tavily (الأولوية الأولى)
- **الوصف**: بحث عميق مع الذكاء الاصطناعي
- **الحد المجاني**: 1000 طلب/شهر
- **المميزات**: بحث عميق، ملخصات ذكية، جودة عالية
- **الموقع**: [tavily.com](https://tavily.com)

### 2. 🥈 SerpAPI (الأولوية الثانية)
- **الوصف**: API بحث Google مخصص
- **الحد المجاني**: 5000 طلب/شهر
- **المميزات**: نتائج فورية، دعم محركات متعددة
- **الموقع**: [serpapi.com](https://serpapi.com)

### 3. 🥉 ScraperAPI (الأولوية الثالثة)
- **الوصف**: استخراج صفحات الويب مع proxies تلقائية
- **الحد المجاني**: 1000 طلب/شهر
- **المميزات**: تجاوز البوتات، proxies متعددة
- **الموقع**: [scraperapi.com](https://scraperapi.com)

### 4. 🏅 Zyte (الأولوية الرابعة)
- **الوصف**: منصة استخراج بيانات ويب متكاملة
- **الحد المجاني**: 1000 طلب/شهر
- **المميزات**: استخراج موزع، ذكاء اصطناعي
- **الموقع**: [zyte.com](https://zyte.com)

### 5. 🏅 ContextualWeb Search API (الأولوية الخامسة)
- **الوصف**: API بحث عام للأخبار والصور
- **الحد المجاني**: 1000 طلب/يوم
- **المميزات**: بحث أخبار، صور، فيديو
- **الموقع**: [contextualweb.io](https://contextualweb.io)

### 6. 🏅 Serper.dev (الأولوية السادسة)
- **الوصف**: API بحث Google مخصص للذكاء الاصطناعي
- **الحد المجاني**: 100 طلب/شهر
- **المميزات**: مصمم للـ AI، نتائج دقيقة
- **الموقع**: [serper.dev](https://serper.dev)

### 7. 🏅 Google Custom Search JSON API (احتياطي أخير)
- **الوصف**: خدمة بحث Google مخصصة
- **الحد المجاني**: 100 طلب/يوم
- **المميزات**: بحث Google رسمي، موثوق
- **الموقع**: [developers.google.com/custom-search](https://developers.google.com/custom-search/v1/overview)

## 🔑 إعداد المفاتيح

### 1. إنشاء ملف `.env` أو تحديث الموجود:

```bash
# مفاتيح Tavily (الأولوية الأولى)
TAVILY_API_KEY_1=your_tavily_key_1
TAVILY_API_KEY_2=your_tavily_key_2

# مفاتيح SerpAPI
SERPAPI_KEY_1=your_serpapi_key_1
SERPAPI_KEY_2=your_serpapi_key_2
SERPAPI_KEY_3=your_serpapi_key_3

# مفاتيح ScraperAPI
SCRAPERAPI_KEY_1=your_scraperapi_key_1
SCRAPERAPI_KEY_2=your_scraperapi_key_2

# مفاتيح Zyte
ZYTE_API_KEY_1=your_zyte_key_1
ZYTE_API_KEY_2=your_zyte_key_2

# مفاتيح ContextualWeb
CONTEXTUALWEB_KEY_1=your_contextualweb_key_1
CONTEXTUALWEB_KEY_2=your_contextualweb_key_2

# مفاتيح Serper.dev
SERPER_DEV_KEY_1=your_serper_dev_key_1
SERPER_DEV_KEY_2=your_serper_dev_key_2

# مفاتيح Google Custom Search
GOOGLE_CUSTOM_SEARCH_KEY_1=your_google_custom_key_1
GOOGLE_CUSTOM_SEARCH_KEY_2=your_google_custom_key_2
GOOGLE_CUSTOM_SEARCH_ENGINE_ID=your_search_engine_id
```

## 📝 خطوات الحصول على المفاتيح

### 1. Tavily (مجاني - 1000 طلب/شهر)
1. اذهب إلى [tavily.com](https://tavily.com)
2. أنشئ حساب مجاني
3. اذهب إلى Dashboard
4. انسخ API Key

### 2. SerpAPI (مجاني - 5000 طلب/شهر)
1. اذهب إلى [serpapi.com](https://serpapi.com)
2. أنشئ حساب مجاني
3. اذهب إلى Dashboard
4. انسخ API Key

### 3. ScraperAPI (مجاني - 1000 طلب/شهر)
1. اذهب إلى [scraperapi.com](https://scraperapi.com)
2. أنشئ حساب مجاني
3. اذهب إلى Dashboard
4. انسخ API Key

### 4. Zyte (مجاني - 1000 طلب/شهر)
1. اذهب إلى [zyte.com](https://zyte.com)
2. أنشئ حساب مجاني
3. اذهب إلى Scrapy Cloud
4. انسخ API Key

### 5. ContextualWeb (مجاني - 1000 طلب/يوم)
1. اذهب إلى [RapidAPI](https://rapidapi.com)
2. ابحث عن "ContextualWeb Search"
3. اشترك في الخطة المجانية
4. انسخ API Key

### 6. Serper.dev (مجاني - 100 طلب/شهر)
1. اذهب إلى [serper.dev](https://serper.dev)
2. أنشئ حساب مجاني
3. اذهب إلى Dashboard
4. انسخ API Key

### 7. Google Custom Search (مجاني - 100 طلب/يوم)
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com)
2. فعل Custom Search JSON API
3. أنشئ API Key
4. أنشئ Custom Search Engine في [cse.google.com](https://cse.google.com)

## 🚀 كيفية عمل النظام

### نظام البدائل التلقائي:
1. **البحث الأول**: يبدأ بـ Tavily (الأفضل)
2. **عند الفشل**: ينتقل تلقائياً لـ SerpAPI
3. **عند الفشل**: ينتقل لـ ScraperAPI
4. **وهكذا**: حتى العثور على نتائج أو انتهاء جميع المحركات

### مميزات النظام:
- ✅ **تبديل تلقائي** عند فشل أي محرك
- ✅ **تخزين مؤقت ذكي** لتوفير الطلبات
- ✅ **مراقبة الحدود** لكل محرك
- ✅ **إحصائيات مفصلة** لكل محرك
- ✅ **جودة عالية** للنتائج
- ✅ **مجاني 100%** بدون تكاليف

## 🧪 اختبار النظام

```bash
# اختبار النظام الجديد
python test_advanced_search_system.py
```

## 📊 مراقبة الأداء

النظام يوفر إحصائيات مفصلة:
- عدد الطلبات لكل محرك
- معدل النجاح
- وقت الاستجابة
- استخدام التخزين المؤقت
- حالة كل محرك

## 🔧 استكشاف الأخطاء

### إذا لم يعمل محرك معين:
1. تحقق من صحة API Key
2. تحقق من الحدود المتاحة
3. تحقق من اتصال الإنترنت
4. راجع logs للتفاصيل

### إذا فشلت جميع المحركات:
1. تحقق من ملف `.env`
2. تأكد من وجود مفتاح واحد على الأقل
3. اختبر الاتصال بالإنترنت
4. راجع إعدادات الـ firewall

## 🎯 نصائح للاستخدام الأمثل

1. **احصل على مفاتيح متعددة** لكل خدمة
2. **راقب الحدود اليومية** لكل محرك
3. **استخدم التخزين المؤقت** لتوفير الطلبات
4. **اختبر النظام بانتظام** للتأكد من العمل
5. **احتفظ بنسخة احتياطية** من المفاتيح

## 🆘 الدعم

إذا واجهت أي مشاكل:
1. راجع ملف `logs/bot.log`
2. شغل اختبار النظام
3. تحقق من إعدادات المفاتيح
4. راجع هذا الدليل

---

**ملاحظة**: جميع الخدمات المذكورة تقدم خطط مجانية كافية للاستخدام العادي. النظام مصمم للعمل بدون أي تكاليف.
