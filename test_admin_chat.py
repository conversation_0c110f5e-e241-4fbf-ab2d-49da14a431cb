#!/usr/bin/env python3
"""
اختبار محسن لإرسال رسالة للمدير
"""

import asyncio
from telegram import Bot
from config.settings import BotConfig

async def test_admin_chat():
    """اختبار إرسال رسالة للمدير مع تحسينات"""
    try:
        # إنشاء البوت
        bot = Bot(token=BotConfig.TELEGRAM_BOT_TOKEN)

        admin_id = BotConfig.TELEGRAM_ADMIN_ID
        print(f"🧪 اختبار إرسال رسالة للمدير: {admin_id}")

        # التحقق من نوع المعرف
        if admin_id.startswith('@'):
            print("⚠️ المعرف يبدأ بـ @ (اسم مستخدم)")
            print("💡 يُفضل استخدام المعرف الرقمي بدلاً من اسم المستخدم")
            print("🔧 شغل: python get_admin_id.py للحصول على المعرف الرقمي")
            return

        # محاولة إرسال رسالة اختبار
        test_message = """🧪 <b>رسالة اختبار من وكيل أخبار الألعاب</b>

✅ إذا وصلتك هذه الرسالة، فإن نظام الإشعارات يعمل بشكل صحيح!

🤖 <i>هذا اختبار تلقائي للتأكد من عمل النظام</i>"""

        await bot.send_message(
            chat_id=admin_id,
            text=test_message,
            parse_mode='HTML'
        )

        print("✅ تم إرسال رسالة الاختبار بنجاح!")
        print("📱 تحقق من تيليجرام لرؤية الرسالة")

        # اختبار إضافي: إرسال إجراء الكتابة
        await bot.send_chat_action(chat_id=admin_id, action="typing")
        print("✅ تم اختبار إرسال إجراء الكتابة بنجاح!")

    except Exception as e:
        error_msg = str(e)
        print(f"❌ فشل في إرسال رسالة الاختبار: {error_msg}")

        if "Chat not found" in error_msg:
            print("\n💡 لحل مشكلة 'Chat not found':")
            print("1. تأكد من أن المدير أرسل رسالة للبوت أولاً")
            print("2. شغل: python get_admin_id.py للحصول على المعرف الصحيح")
            print("3. حدث TELEGRAM_ADMIN_ID في config/settings.py بالمعرف الرقمي")
            print(f"4. رابط البوت: https://t.me/{BotConfig.TELEGRAM_BOT_USERNAME.replace('@', '')}")
        elif "Unauthorized" in error_msg:
            print("\n💡 مشكلة في توكن البوت:")
            print("1. تحقق من صحة TELEGRAM_BOT_TOKEN في config/settings.py")
            print("2. تأكد من أن البوت مفعل في BotFather")
        elif "Forbidden" in error_msg:
            print("\n💡 البوت محظور من إرسال رسائل:")
            print("1. تأكد من أن المدير لم يحظر البوت")
            print("2. أرسل رسالة للبوت من حساب المدير أولاً")
        else:
            print(f"\n💡 خطأ غير متوقع: {error_msg}")

if __name__ == "__main__":
    asyncio.run(test_admin_chat())
