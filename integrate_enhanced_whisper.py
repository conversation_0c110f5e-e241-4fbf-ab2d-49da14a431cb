#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تكامل نظام Whisper المحسن مع الوكيل الحالي
Integration of Enhanced Whisper System with Current Agent
"""

import asyncio
import sys
import os
from typing import Optional, Dict

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.enhanced_whisper_manager import enhanced_whisper_manager
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.logger import logger
from config.settings import BotConfig


class WhisperIntegrationManager:
    """مدير تكامل نظام Whisper المحسن"""
    
    def __init__(self):
        self.enhanced_whisper = enhanced_whisper_manager
        self.youtube_analyzer = AdvancedYouTubeAnalyzer()
        self.config = BotConfig()
        
    async def integrate_with_youtube_analyzer(self):
        """تكامل النظام المحسن مع محلل YouTube"""
        logger.info("🔗 تكامل نظام Whisper المحسن مع محلل YouTube...")
        
        try:
            # استبدال دالة تحويل الصوت في محلل YouTube
            original_method = self.youtube_analyzer.extract_video_transcript_with_whisper
            
            async def enhanced_whisper_method(video_id: str, video_url: str = None, video_data: Dict = None) -> Optional[str]:
                """دالة محسنة لتحويل الصوت إلى نص"""
                try:
                    logger.info(f"🎤 استخدام النظام المحسن لتحويل الصوت - الفيديو: {video_id}")
                    
                    # الحصول على بيانات الفيديو إذا لم تكن متوفرة
                    if not video_data:
                        video_data = await self.youtube_analyzer._get_video_basic_info(video_id)
                    
                    video_title = video_data.get('title', '') if video_data else ''
                    
                    # تحميل الصوت
                    if not video_url:
                        video_url = f"https://www.youtube.com/watch?v={video_id}"
                    
                    import aiohttp
                    async with aiohttp.ClientSession() as session:
                        audio_data = await self.youtube_analyzer._download_audio_from_video(video_url, session)
                    
                    if not audio_data:
                        logger.error("❌ فشل في تحميل الصوت")
                        return None
                    
                    # تحديد اللغة
                    detected_language = 'en'  # افتراضي
                    if video_data:
                        detected_language = await self.youtube_analyzer._detect_video_language(video_data)
                    
                    # تحويل الصوت إلى نص باستخدام النظام المحسن
                    result = await self.enhanced_whisper.transcribe_audio(
                        audio_data=audio_data,
                        video_id=video_id,
                        video_title=video_title,
                        detected_language=detected_language
                    )
                    
                    if result and result.get('success'):
                        logger.info(f"✅ نجح التحويل المحسن - {result.get('word_count', 0)} كلمة")
                        return result.get('text', '')
                    else:
                        logger.warning("⚠️ فشل النظام المحسن، العودة للطريقة الأصلية...")
                        return await original_method(video_id, video_url, video_data)
                        
                except Exception as e:
                    logger.error(f"❌ خطأ في النظام المحسن: {e}")
                    logger.info("🔄 العودة للطريقة الأصلية...")
                    return await original_method(video_id, video_url, video_data)
            
            # استبدال الدالة
            self.youtube_analyzer.extract_video_transcript_with_whisper = enhanced_whisper_method
            
            logger.info("✅ تم تكامل النظام المحسن بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في التكامل: {e}")
            return False
    
    async def test_integration(self, test_video_id: str = "bS5yMnHP3w0") -> bool:
        """اختبار التكامل مع فيديو تجريبي"""
        logger.info(f"🧪 اختبار التكامل مع الفيديو: {test_video_id}")
        
        try:
            # اختبار النظام المحسن مباشرة
            logger.info("1️⃣ اختبار النظام المحسن مباشرة...")
            
            video_url = f"https://www.youtube.com/watch?v={test_video_id}"
            
            # تحميل الصوت
            import aiohttp
            async with aiohttp.ClientSession() as session:
                audio_data = await self.youtube_analyzer._download_audio_from_video(video_url, session)
            
            if audio_data:
                logger.info(f"✅ تم تحميل الصوت - {len(audio_data) / 1024 / 1024:.2f}MB")
                
                # تحويل الصوت
                result = await self.enhanced_whisper.transcribe_audio(
                    audio_data=audio_data,
                    video_id=test_video_id,
                    video_title="فيديو اختبار التكامل"
                )
                
                if result and result.get('success'):
                    logger.info("✅ نجح اختبار النظام المحسن")
                    
                    # اختبار التكامل مع محلل YouTube
                    logger.info("2️⃣ اختبار التكامل مع محلل YouTube...")
                    
                    transcript = await self.youtube_analyzer.extract_video_transcript_with_whisper(test_video_id)
                    
                    if transcript and len(transcript.strip()) > 10:
                        logger.info("✅ نجح اختبار التكامل الكامل")
                        return True
                    else:
                        logger.warning("⚠️ فشل اختبار التكامل")
                        return False
                else:
                    logger.error("❌ فشل اختبار النظام المحسن")
                    return False
            else:
                logger.error("❌ فشل في تحميل الصوت")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار التكامل: {e}")
            return False
    
    def get_integration_status(self) -> Dict:
        """الحصول على حالة التكامل"""
        try:
            # فحص النظام المحسن
            enhanced_status = hasattr(self.enhanced_whisper, 'transcribe_audio')
            
            # فحص التكامل مع محلل YouTube
            integration_status = hasattr(self.youtube_analyzer, 'extract_video_transcript_with_whisper')
            
            # فحص الإعدادات
            config_status = bool(self.config.WHISPER_API_URL and self.config.WHISPER_API_KEY)
            
            return {
                'enhanced_whisper_available': enhanced_status,
                'youtube_integration_active': integration_status,
                'configuration_valid': config_status,
                'whisper_api_url': self.config.WHISPER_API_URL,
                'transcription_history_count': len(self.enhanced_whisper.get_transcription_history()),
                'last_stats': self.enhanced_whisper.get_transcription_stats()
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص حالة التكامل: {e}")
            return {
                'error': str(e),
                'enhanced_whisper_available': False,
                'youtube_integration_active': False,
                'configuration_valid': False
            }
    
    async def run_full_integration_test(self) -> Dict:
        """تشغيل اختبار تكامل شامل"""
        logger.info("🧪 بدء اختبار التكامل الشامل...")
        
        results = {
            'integration_setup': False,
            'connection_test': False,
            'transcription_test': False,
            'youtube_integration_test': False,
            'overall_success': False
        }
        
        try:
            # 1. إعداد التكامل
            logger.info("1️⃣ إعداد التكامل...")
            results['integration_setup'] = await self.integrate_with_youtube_analyzer()
            
            # 2. اختبار الاتصال
            logger.info("2️⃣ اختبار الاتصال...")
            connection_result = await self.enhanced_whisper.test_whisper_connection()
            results['connection_test'] = connection_result.get('success', False)
            
            # 3. اختبار التحويل
            logger.info("3️⃣ اختبار التحويل...")
            results['transcription_test'] = await self.test_integration()
            
            # 4. اختبار التكامل مع YouTube
            logger.info("4️⃣ اختبار التكامل مع YouTube...")
            results['youtube_integration_test'] = results['transcription_test']  # نفس الاختبار
            
            # النتيجة الإجمالية
            results['overall_success'] = all([
                results['integration_setup'],
                results['connection_test'],
                results['transcription_test']
            ])
            
            if results['overall_success']:
                logger.info("🎉 نجح اختبار التكامل الشامل!")
            else:
                logger.warning("⚠️ فشل في بعض اختبارات التكامل")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار التكامل الشامل: {e}")
            results['error'] = str(e)
            return results


async def main():
    """الدالة الرئيسية للتكامل"""
    logger.info("🚀 بدء تكامل نظام Whisper المحسن...")
    
    # إنشاء مدير التكامل
    integration_manager = WhisperIntegrationManager()
    
    # فحص الحالة الحالية
    logger.info("📊 فحص حالة النظام...")
    status = integration_manager.get_integration_status()
    
    logger.info("📋 حالة النظام:")
    for key, value in status.items():
        if key != 'last_stats':
            logger.info(f"   {key}: {value}")
    
    # تشغيل اختبار التكامل الشامل
    logger.info("\n🧪 تشغيل اختبار التكامل الشامل...")
    test_results = await integration_manager.run_full_integration_test()
    
    # عرض النتائج
    logger.info("\n📊 نتائج اختبار التكامل:")
    for test_name, result in test_results.items():
        if test_name != 'error':
            status_icon = "✅" if result else "❌"
            logger.info(f"   {status_icon} {test_name}: {result}")
    
    if test_results.get('error'):
        logger.error(f"❌ خطأ: {test_results['error']}")
    
    # النتيجة النهائية
    if test_results.get('overall_success'):
        logger.info("\n🎉 تم تكامل نظام Whisper المحسن بنجاح!")
        logger.info("✅ النظام جاهز للاستخدام")
        return True
    else:
        logger.warning("\n⚠️ فشل في تكامل النظام بالكامل")
        logger.info("🔧 يرجى مراجعة الأخطاء وإعادة المحاولة")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
