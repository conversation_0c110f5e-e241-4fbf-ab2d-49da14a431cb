#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للصور المرخصة للألعاب
"""

import asyncio
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.licensed_image_manager import licensed_image_manager

async def quick_test(game_name: str):
    """اختبار سريع للعبة"""
    print(f"🎮 اختبار سريع للعبة: {game_name}")
    print("=" * 50)
    
    try:
        # البحث عن صور مرخصة
        print("🔍 البحث عن صور مرخصة...")
        images = await licensed_image_manager.get_licensed_images_for_game(game_name, 3)
        
        if images:
            print(f"✅ تم العثور على {len(images)} صورة مرخصة!")
            print("\n📸 تفاصيل الصور:")
            
            for i, img in enumerate(images, 1):
                print(f"\n   صورة {i}:")
                print(f"   • المصدر: {img.source}")
                print(f"   • النوع: {img.image_type}")
                print(f"   • الترخيص: {img.license_type}")
                print(f"   • آمنة لأدسنس: {'✅ نعم' if img.safe_for_adsense else '❌ لا'}")
                print(f"   • مجانية: {'✅ نعم' if img.copyright_free else '⚠️ تتطلب إشارة'}")
                print(f"   • الإشارة: {img.attribution}")
                print(f"   • الرابط: {img.url[:60]}...")
            
            # إحصائيات
            sources = {}
            for img in images:
                sources[img.source] = sources.get(img.source, 0) + 1
            
            print(f"\n📊 إحصائيات المصادر:")
            for source, count in sources.items():
                print(f"   • {source}: {count} صورة")
                
        else:
            print("❌ لم يتم العثور على صور مرخصة")
            print("💡 سيتم استخدام الذكاء الاصطناعي كبديل")
        
        # اختبار الإحصائيات
        stats = licensed_image_manager.get_usage_stats()
        print(f"\n📈 إحصائيات الاستخدام:")
        print(f"   • إجمالي الطلبات: {stats['total_requests']}")
        print(f"   • الطلبات الناجحة: {stats['successful_requests']}")
        print(f"   • معدل النجاح: {stats['success_rate']:.1f}%")
        
        if stats['provider_usage']:
            print(f"   • استخدام الموفرين:")
            for provider, usage in stats['provider_usage'].items():
                print(f"     - {provider}: {usage} صورة")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """الدالة الرئيسية"""
    print("🎯 أداة الاختبار السريع للصور المرخصة")
    print("=" * 50)
    
    # ألعاب شائعة للاختبار السريع
    popular_games = [
        "The Witcher 3",
        "Cyberpunk 2077",
        "League of Legends", 
        "Valorant",
        "Call of Duty",
        "FIFA 2024",
        "Minecraft",
        "Fortnite"
    ]
    
    print("\n🎮 ألعاب شائعة للاختبار السريع:")
    for i, game in enumerate(popular_games, 1):
        print(f"   {i}. {game}")
    
    print(f"\n💡 أو اكتب اسم لعبة مخصص")
    
    try:
        user_input = input("\nأدخل رقم اللعبة أو اسم اللعبة: ").strip()
        
        if user_input.isdigit():
            choice = int(user_input)
            if 1 <= choice <= len(popular_games):
                game_name = popular_games[choice - 1]
            else:
                print("❌ رقم غير صحيح")
                return
        else:
            game_name = user_input
            if not game_name:
                print("❌ لم يتم إدخال اسم اللعبة")
                return
        
        print(f"\n🚀 بدء الاختبار...")
        await quick_test(game_name)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")

if __name__ == "__main__":
    asyncio.run(main())
