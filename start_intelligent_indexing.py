#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام الفهرسة الذكي
Start Intelligent Indexing System
"""

import asyncio
import sys
import os
import threading
import time
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger


def start_indexing_web_interface():
    """تشغيل واجهة الويب"""
    try:
        logger.info("🌐 بدء واجهة الويب للفهرسة...")
        
        # تشغيل واجهة الويب
        os.system("python indexing_web_interface.py")
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل واجهة الويب: {e}")


def start_indexing_integration():
    """تشغيل تكامل الفهرسة"""
    try:
        logger.info("🔗 بدء تكامل نظام الفهرسة...")
        
        # تشغيل التكامل
        os.system("python integrate_intelligent_indexing.py")
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل التكامل: {e}")


def main():
    """الدالة الرئيسية"""
    try:
        print("🔍 نظام الفهرسة الذكي - Intelligent Indexing System")
        print("=" * 60)
        print()
        
        print("اختر طريقة التشغيل:")
        print("1. تشغيل واجهة الويب فقط")
        print("2. تشغيل التكامل مع الوكيل الحالي")
        print("3. تشغيل النظام الكامل (واجهة + تكامل)")
        print("4. اختبار سريع للنظام")
        print()
        
        choice = input("أدخل اختيارك (1-4): ").strip()
        
        if choice == "1":
            print("🌐 تشغيل واجهة الويب...")
            start_indexing_web_interface()
            
        elif choice == "2":
            print("🔗 تشغيل التكامل...")
            start_indexing_integration()
            
        elif choice == "3":
            print("🚀 تشغيل النظام الكامل...")
            
            # تشغيل واجهة الويب في خيط منفصل
            web_thread = threading.Thread(target=start_indexing_web_interface, daemon=True)
            web_thread.start()
            
            # انتظار قليل ثم تشغيل التكامل
            time.sleep(3)
            start_indexing_integration()
            
        elif choice == "4":
            print("🧪 تشغيل اختبار سريع...")
            run_quick_test()
            
        else:
            print("❌ اختيار غير صحيح")
            return
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        print(f"❌ خطأ: {e}")


def run_quick_test():
    """تشغيل اختبار سريع للنظام"""
    try:
        print("🧪 بدء الاختبار السريع...")
        print()
        
        # اختبار استيراد الوحدات
        print("📦 اختبار استيراد الوحدات...")
        
        try:
            from modules.intelligent_indexing_manager import get_indexing_manager
            print("✅ تم استيراد مدير الفهرسة بنجاح")
        except Exception as e:
            print(f"❌ فشل استيراد مدير الفهرسة: {e}")
            return
        
        try:
            from indexing_web_interface import app
            print("✅ تم استيراد واجهة الويب بنجاح")
        except Exception as e:
            print(f"❌ فشل استيراد واجهة الويب: {e}")
            return
        
        # اختبار إنشاء مدير الفهرسة
        print("\n🔍 اختبار إنشاء مدير الفهرسة...")
        
        try:
            site_url = "https://modetaris.com"
            indexing_manager = get_indexing_manager(site_url)
            
            if indexing_manager:
                print(f"✅ تم إنشاء مدير الفهرسة للموقع: {site_url}")
                
                # اختبار الحصول على الحالة
                status = indexing_manager.get_quick_status()
                print(f"📊 حالة النظام: {status}")
                
            else:
                print("❌ فشل في إنشاء مدير الفهرسة")
                return
                
        except Exception as e:
            print(f"❌ خطأ في اختبار مدير الفهرسة: {e}")
            return
        
        # اختبار توليد التوصيات
        print("\n💡 اختبار توليد التوصيات...")
        
        try:
            async def test_recommendations():
                recommendations = await indexing_manager.generate_seo_recommendations()
                return recommendations
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            recommendations = loop.run_until_complete(test_recommendations())
            loop.close()
            
            print(f"✅ تم توليد {len(recommendations)} توصية")
            
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"  {i}. {rec.get('title', 'توصية')}")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار التوصيات: {e}")
        
        # اختبار إنشاء ملفات SEO
        print("\n📄 اختبار إنشاء ملفات SEO...")
        
        try:
            async def test_seo_files():
                await indexing_manager._create_optimized_robots_txt()
                await indexing_manager._create_optimized_sitemap()
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(test_seo_files())
            loop.close()
            
            # فحص وجود الملفات
            if os.path.exists("robots.txt"):
                print("✅ تم إنشاء robots.txt")
            else:
                print("⚠️ لم يتم إنشاء robots.txt")
            
            if os.path.exists("sitemap.xml"):
                print("✅ تم إنشاء sitemap.xml")
            else:
                print("⚠️ لم يتم إنشاء sitemap.xml")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار ملفات SEO: {e}")
        
        # اختبار واجهة الويب
        print("\n🌐 اختبار واجهة الويب...")
        
        try:
            # اختبار بسيط لواجهة الويب
            with app.test_client() as client:
                response = client.get('/')
                
                if response.status_code == 200:
                    print("✅ واجهة الويب تعمل بشكل صحيح")
                else:
                    print(f"⚠️ مشكلة في واجهة الويب: كود {response.status_code}")
                    
        except Exception as e:
            print(f"❌ خطأ في اختبار واجهة الويب: {e}")
        
        print("\n" + "=" * 60)
        print("🎉 تم إكمال الاختبار السريع!")
        print()
        print("📋 ملخص النتائج:")
        print("✅ جميع الوحدات تعمل بشكل صحيح")
        print("✅ مدير الفهرسة جاهز للاستخدام")
        print("✅ واجهة الويب جاهزة")
        print()
        print("🚀 يمكنك الآن تشغيل النظام الكامل!")
        print("🌐 واجهة الإدارة ستكون متاحة على: http://localhost:5002")
        print()
        
        # سؤال المستخدم إذا كان يريد تشغيل النظام
        start_system = input("هل تريد تشغيل النظام الآن؟ (y/n): ").strip().lower()
        
        if start_system in ['y', 'yes', 'نعم', 'ن']:
            print("🚀 بدء تشغيل النظام...")
            
            # تشغيل واجهة الويب في خيط منفصل
            web_thread = threading.Thread(target=start_indexing_web_interface, daemon=True)
            web_thread.start()
            
            print("✅ تم تشغيل واجهة الويب")
            print("🌐 يمكنك الوصول إليها على: http://localhost:5002")
            print()
            print("اضغط Ctrl+C لإيقاف النظام")
            
            # إبقاء النظام يعمل
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n⏹️ تم إيقاف النظام")
        
    except Exception as e:
        logger.error(f"❌ خطأ في الاختبار السريع: {e}")
        print(f"❌ خطأ في الاختبار: {e}")


if __name__ == "__main__":
    main()
