#!/usr/bin/env python3
"""
اختبار سريع للتحسينات المطبقة على رفع الملفات الصوتية
"""

import asyncio
import sys
from pathlib import Path

# إضافة المسار الجذر
sys.path.append(str(Path(__file__).parent))

from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.logger import logger
from config.audio_settings import AudioDiagnostics
import time

class AudioUploadTester:
    """فئة اختبار تحسينات رفع الصوت"""
    
    def __init__(self):
        self.analyzer = AdvancedYouTubeAnalyzer()
        self.test_results = []
    
    async def test_video_processing(self, video_id: str) -> dict:
        """اختبار معالجة فيديو كامل"""
        logger.info(f"🧪 بدء اختبار الفيديو: {video_id}")
        start_time = time.time()
        
        test_result = {
            'video_id': video_id,
            'success': False,
            'duration': 0,
            'file_size_mb': 0,
            'transcript_length': 0,
            'method_used': None,
            'errors': []
        }
        
        try:
            # 1. استخراج النص من الفيديو
            transcript = await self.analyzer.extract_video_transcript_with_whisper(video_id)
            
            if transcript and len(transcript.strip()) > 50:
                test_result['success'] = True
                test_result['transcript_length'] = len(transcript)
                logger.info(f"✅ نجح الاختبار - طول النص: {len(transcript)} حرف")
            else:
                test_result['errors'].append("النص قصير جداً أو فارغ")
                logger.warning("⚠️ النص المستخرج قصير جداً")
            
        except Exception as e:
            test_result['errors'].append(str(e))
            logger.error(f"❌ فشل الاختبار: {e}")
        
        test_result['duration'] = time.time() - start_time
        self.test_results.append(test_result)
        
        return test_result
    
    async def test_compression_methods(self) -> dict:
        """اختبار طرق الضغط المختلفة"""
        logger.info("🧪 اختبار طرق الضغط...")
        
        # إنشاء بيانات اختبار بأحجام مختلفة
        test_sizes = [
            (5 * 1024 * 1024, "5MB"),    # صغير
            (15 * 1024 * 1024, "15MB"),  # متوسط
            (25 * 1024 * 1024, "25MB"),  # كبير
            (35 * 1024 * 1024, "35MB"),  # كبير جداً
        ]
        
        compression_results = []
        
        for size_bytes, size_label in test_sizes:
            # إنشاء بيانات وهمية
            test_data = b"test_audio_data" * (size_bytes // 15)
            test_data = test_data[:size_bytes]  # ضبط الحجم بدقة
            
            # تحليل الملف
            analysis = AudioDiagnostics.analyze_file_size(test_data)
            strategy = AudioDiagnostics.suggest_upload_strategy(analysis['size_mb'])
            
            # اختبار الضغط
            if analysis['size_mb'] > 10:
                compressed_data = await self.analyzer._compress_audio_data(test_data)
                compression_ratio = len(compressed_data) / len(test_data)
            else:
                compressed_data = test_data
                compression_ratio = 1.0
            
            result = {
                'original_size': size_label,
                'original_size_mb': analysis['size_mb'],
                'compressed_size_mb': len(compressed_data) / 1024 / 1024,
                'compression_ratio': compression_ratio,
                'strategy': strategy['method'],
                'recommended_model': strategy['model']
            }
            
            compression_results.append(result)
            logger.info(f"📦 {size_label}: {strategy['method']} - نسبة الضغط: {compression_ratio:.2f}")
        
        return {
            'compression_tests': compression_results,
            'total_tests': len(compression_results)
        }
    
    async def test_alternative_methods(self) -> dict:
        """اختبار الطرق البديلة"""
        logger.info("🧪 اختبار الطرق البديلة...")
        
        # محاكاة بيانات صوتية
        test_audio = b"mock_audio_data" * (2 * 1024 * 1024)  # 2MB تقريباً
        
        # اختبار الطرق البديلة (محاكاة)
        methods = [
            {'model': 'whisper-small', 'format': 'text'},
            {'model': 'whisper-base', 'format': 'json'},
            {'model': 'whisper-tiny', 'format': 'text'},
        ]
        
        method_results = []
        for method in methods:
            # محاكاة نتيجة الاختبار
            simulated_success = method['model'] in ['whisper-small', 'whisper-base']
            
            result = {
                'model': method['model'],
                'format': method['format'],
                'simulated_success': simulated_success,
                'estimated_speed': 'fast' if 'small' in method['model'] or 'tiny' in method['model'] else 'medium'
            }
            
            method_results.append(result)
            status = "✅" if simulated_success else "❌"
            logger.info(f"{status} {method['model']} ({method['format']})")
        
        return {
            'method_tests': method_results,
            'successful_methods': [r for r in method_results if r['simulated_success']]
        }
    
    def print_test_summary(self):
        """طباعة ملخص الاختبارات"""
        print("\n" + "="*60)
        print("🧪 ملخص اختبارات تحسينات رفع الصوت")
        print("="*60)
        
        if self.test_results:
            successful_tests = [r for r in self.test_results if r['success']]
            success_rate = len(successful_tests) / len(self.test_results) * 100
            
            print(f"\n📊 النتائج العامة:")
            print(f"   إجمالي الاختبارات: {len(self.test_results)}")
            print(f"   الاختبارات الناجحة: {len(successful_tests)}")
            print(f"   معدل النجاح: {success_rate:.1f}%")
            
            if successful_tests:
                avg_duration = sum(r['duration'] for r in successful_tests) / len(successful_tests)
                avg_transcript_length = sum(r['transcript_length'] for r in successful_tests) / len(successful_tests)
                
                print(f"   متوسط وقت المعالجة: {avg_duration:.1f} ثانية")
                print(f"   متوسط طول النص: {avg_transcript_length:.0f} حرف")
            
            print(f"\n📋 تفاصيل الاختبارات:")
            for i, result in enumerate(self.test_results, 1):
                status = "✅" if result['success'] else "❌"
                print(f"   {i}. {result['video_id']}: {status} ({result['duration']:.1f}s)")
                if result['errors']:
                    for error in result['errors']:
                        print(f"      خطأ: {error}")
        
        print("\n" + "="*60)

async def main():
    """الدالة الرئيسية للاختبار"""
    tester = AudioUploadTester()
    
    print("🚀 بدء اختبار تحسينات رفع الملفات الصوتية...")
    
    # 1. اختبار طرق الضغط
    print("\n📦 اختبار طرق الضغط...")
    compression_results = await tester.test_compression_methods()
    
    print(f"✅ تم اختبار {compression_results['total_tests']} طريقة ضغط")
    
    # 2. اختبار الطرق البديلة
    print("\n🔄 اختبار الطرق البديلة...")
    alternative_results = await tester.test_alternative_methods()
    
    successful_methods = len(alternative_results['successful_methods'])
    total_methods = len(alternative_results['method_tests'])
    print(f"✅ {successful_methods}/{total_methods} طريقة بديلة ناجحة")
    
    # 3. اختبار فيديو حقيقي (إذا تم توفير معرف)
    if len(sys.argv) > 1:
        video_id = sys.argv[1]
        print(f"\n🎥 اختبار فيديو حقيقي: {video_id}")
        video_result = await tester.test_video_processing(video_id)
        
        if video_result['success']:
            print(f"✅ نجح اختبار الفيديو - {video_result['transcript_length']} حرف")
        else:
            print(f"❌ فشل اختبار الفيديو: {video_result['errors']}")
    
    # طباعة الملخص
    tester.print_test_summary()
    
    print("\n🎉 اكتمل الاختبار!")
    print("💡 لاختبار فيديو محدد: python test_audio_upload_fix.py VIDEO_ID")

if __name__ == "__main__":
    asyncio.run(main())
