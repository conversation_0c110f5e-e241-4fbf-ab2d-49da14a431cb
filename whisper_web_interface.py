#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة ويب لعرض نتائج تحويل الصوت إلى نص
Web Interface for Audio-to-Text Transcription Results
"""

from flask import Flask, render_template_string, jsonify, request, send_file
import json
import os
from datetime import datetime
from typing import Dict, List
import tempfile
import csv

from modules.enhanced_whisper_manager import enhanced_whisper_manager
from modules.logger import logger

app = Flask(__name__)

# HTML Template للواجهة الرئيسية
HTML_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 نتائج تحويل الصوت إلى نص</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(45deg, #f8f9ff, #e8f4fd);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #667eea;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            color: #666;
        }
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .transcriptions-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }
        .transcription-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .transcription-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        .video-info {
            flex: 1;
        }
        .video-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .video-id {
            color: #666;
            font-size: 0.9em;
        }
        .transcription-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
            font-size: 0.9em;
        }
        .meta-item {
            background: #f0f0f0;
            padding: 8px;
            border-radius: 5px;
            text-align: center;
        }
        .meta-label {
            font-weight: bold;
            color: #667eea;
        }
        .transcription-text {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            line-height: 1.6;
            border: 1px solid #e9ecef;
            max-height: 200px;
            overflow-y: auto;
        }
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
        .loading {
            text-align: center;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .filter-controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        input, select {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        input:focus, select:focus {
            border-color: #667eea;
            outline: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 نتائج تحويل الصوت إلى نص</h1>
        
        <!-- إحصائيات عامة -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-number" id="totalTranscriptions">0</div>
                <div class="stat-label">إجمالي التحويلات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successfulTranscriptions">0</div>
                <div class="stat-label">تحويلات ناجحة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">معدل النجاح</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalWords">0</div>
                <div class="stat-label">إجمالي الكلمات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgProcessingTime">0s</div>
                <div class="stat-label">متوسط وقت المعالجة</div>
            </div>
        </div>

        <!-- أدوات التحكم -->
        <div class="controls">
            <button onclick="refreshData()">🔄 تحديث البيانات</button>
            <button onclick="exportToCSV()">📊 تصدير CSV</button>
            <button onclick="exportToJSON()">📄 تصدير JSON</button>
            <button onclick="clearHistory()">🗑️ مسح التاريخ</button>
            <button onclick="testConnection()">🧪 اختبار الاتصال</button>
        </div>

        <!-- فلاتر البحث -->
        <div class="filter-controls">
            <div class="filter-row">
                <label>🔍 البحث:</label>
                <input type="text" id="searchInput" placeholder="ابحث في النصوص..." onkeyup="filterTranscriptions()">
                
                <label>🌍 اللغة:</label>
                <select id="languageFilter" onchange="filterTranscriptions()">
                    <option value="">جميع اللغات</option>
                </select>
                
                <label>📅 التاريخ:</label>
                <input type="date" id="dateFilter" onchange="filterTranscriptions()">
            </div>
        </div>

        <!-- رسائل النظام -->
        <div id="systemMessage"></div>

        <!-- قائمة التحويلات -->
        <div class="transcriptions-list" id="transcriptionsList">
            <div class="loading">⏳ جاري تحميل البيانات...</div>
        </div>
    </div>

    <script>
        let allTranscriptions = [];
        let filteredTranscriptions = [];

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
        });

        async function refreshData() {
            try {
                showMessage('⏳ جاري تحميل البيانات...', 'loading');
                
                const response = await fetch('/api/transcriptions');
                const data = await response.json();
                
                if (data.success) {
                    allTranscriptions = data.transcriptions;
                    filteredTranscriptions = [...allTranscriptions];
                    
                    updateStats(data.stats);
                    updateLanguageFilter();
                    displayTranscriptions();
                    
                    showMessage('✅ تم تحديث البيانات بنجاح', 'success');
                } else {
                    showMessage('❌ خطأ في تحميل البيانات: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        function updateStats(stats) {
            document.getElementById('totalTranscriptions').textContent = stats.total_transcriptions;
            document.getElementById('successfulTranscriptions').textContent = stats.successful_transcriptions;
            document.getElementById('successRate').textContent = stats.success_rate.toFixed(1) + '%';
            document.getElementById('totalWords').textContent = stats.total_words.toLocaleString();
            document.getElementById('avgProcessingTime').textContent = stats.average_processing_time.toFixed(1) + 's';
        }

        function updateLanguageFilter() {
            const languageFilter = document.getElementById('languageFilter');
            const languages = [...new Set(allTranscriptions.map(t => t.language).filter(l => l))];
            
            // مسح الخيارات الحالية (عدا الخيار الأول)
            while (languageFilter.children.length > 1) {
                languageFilter.removeChild(languageFilter.lastChild);
            }
            
            // إضافة اللغات الجديدة
            languages.forEach(lang => {
                const option = document.createElement('option');
                option.value = lang;
                option.textContent = lang;
                languageFilter.appendChild(option);
            });
        }

        function displayTranscriptions() {
            const container = document.getElementById('transcriptionsList');
            
            if (filteredTranscriptions.length === 0) {
                container.innerHTML = '<div class="no-data">📭 لا توجد تحويلات متاحة</div>';
                return;
            }
            
            const html = filteredTranscriptions.map(transcription => `
                <div class="transcription-item">
                    <div class="transcription-header">
                        <div class="video-info">
                            <div class="video-title">${transcription.video_title || 'عنوان غير محدد'}</div>
                            <div class="video-id">🆔 ${transcription.video_id}</div>
                        </div>
                        <div class="timestamp">${new Date(transcription.timestamp).toLocaleString('ar-SA')}</div>
                    </div>
                    
                    <div class="transcription-meta">
                        <div class="meta-item">
                            <div class="meta-label">🌍 اللغة</div>
                            <div>${transcription.language || 'غير محدد'}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">📝 كلمات</div>
                            <div>${transcription.word_count || 0}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">🔤 أحرف</div>
                            <div>${transcription.char_count || 0}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">⏱️ وقت المعالجة</div>
                            <div>${(transcription.processing_time || 0).toFixed(1)}s</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">📁 حجم الملف</div>
                            <div>${(transcription.file_size_mb || 0).toFixed(1)}MB</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">🔧 الطريقة</div>
                            <div>${transcription.method_used || 'غير محدد'}</div>
                        </div>
                    </div>
                    
                    <div class="transcription-text">${transcription.text || 'لا يوجد نص متاح'}</div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        function filterTranscriptions() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const languageFilter = document.getElementById('languageFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;
            
            filteredTranscriptions = allTranscriptions.filter(transcription => {
                // فلتر النص
                const textMatch = !searchTerm || 
                    (transcription.text && transcription.text.toLowerCase().includes(searchTerm)) ||
                    (transcription.video_title && transcription.video_title.toLowerCase().includes(searchTerm)) ||
                    (transcription.video_id && transcription.video_id.toLowerCase().includes(searchTerm));
                
                // فلتر اللغة
                const languageMatch = !languageFilter || transcription.language === languageFilter;
                
                // فلتر التاريخ
                const dateMatch = !dateFilter || 
                    new Date(transcription.timestamp).toDateString() === new Date(dateFilter).toDateString();
                
                return textMatch && languageMatch && dateMatch;
            });
            
            displayTranscriptions();
        }

        async function exportToCSV() {
            try {
                const response = await fetch('/api/export/csv');
                const blob = await response.blob();
                
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `transcriptions_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showMessage('✅ تم تصدير ملف CSV بنجاح', 'success');
            } catch (error) {
                showMessage('❌ خطأ في تصدير CSV: ' + error.message, 'error');
            }
        }

        async function exportToJSON() {
            try {
                const response = await fetch('/api/export/json');
                const blob = await response.blob();
                
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `transcriptions_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showMessage('✅ تم تصدير ملف JSON بنجاح', 'success');
            } catch (error) {
                showMessage('❌ خطأ في تصدير JSON: ' + error.message, 'error');
            }
        }

        async function clearHistory() {
            if (!confirm('هل أنت متأكد من مسح جميع التحويلات؟')) {
                return;
            }
            
            try {
                const response = await fetch('/api/clear-history', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('✅ تم مسح التاريخ بنجاح', 'success');
                    refreshData();
                } else {
                    showMessage('❌ خطأ في مسح التاريخ: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function testConnection() {
            try {
                showMessage('🧪 جاري اختبار الاتصال...', 'loading');
                
                const response = await fetch('/api/test-connection');
                const data = await response.json();
                
                if (data.success) {
                    showMessage('✅ الاتصال مع Whisper API ناجح', 'success');
                } else {
                    showMessage('❌ فشل الاتصال: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في اختبار الاتصال: ' + error.message, 'error');
            }
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('systemMessage');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
            
            // إخفاء الرسالة بعد 5 ثوان (عدا رسائل التحميل)
            if (type !== 'loading') {
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 5000);
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/transcriptions')
def get_transcriptions():
    """الحصول على جميع التحويلات مع الإحصائيات"""
    try:
        transcriptions = enhanced_whisper_manager.get_transcription_history()
        stats = enhanced_whisper_manager.get_transcription_stats()

        return jsonify({
            'success': True,
            'transcriptions': transcriptions,
            'stats': stats,
            'total_count': len(transcriptions)
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على التحويلات: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/transcription/<video_id>')
def get_transcription(video_id):
    """الحصول على تحويل محدد"""
    try:
        transcriptions = enhanced_whisper_manager.get_transcription_history()
        transcription = next((t for t in transcriptions if t.get('video_id') == video_id), None)

        if transcription:
            return jsonify({
                'success': True,
                'transcription': transcription
            })
        else:
            return jsonify({
                'success': False,
                'error': 'التحويل غير موجود'
            }), 404

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على التحويل: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/export/csv')
def export_csv():
    """تصدير التحويلات كملف CSV"""
    try:
        transcriptions = enhanced_whisper_manager.get_transcription_history()

        # إنشاء ملف CSV مؤقت
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8-sig') as tmp_file:
            writer = csv.writer(tmp_file)

            # كتابة العناوين
            headers = [
                'معرف الفيديو', 'عنوان الفيديو', 'النص', 'اللغة',
                'عدد الكلمات', 'عدد الأحرف', 'وقت المعالجة',
                'حجم الملف (MB)', 'الطريقة المستخدمة', 'التاريخ'
            ]
            writer.writerow(headers)

            # كتابة البيانات
            for t in transcriptions:
                if t.get('success'):
                    row = [
                        t.get('video_id', ''),
                        t.get('video_title', ''),
                        t.get('text', ''),
                        t.get('language', ''),
                        t.get('word_count', 0),
                        t.get('char_count', 0),
                        t.get('processing_time', 0),
                        t.get('file_size_mb', 0),
                        t.get('method_used', ''),
                        t.get('timestamp', '')
                    ]
                    writer.writerow(row)

            tmp_file.flush()

            return send_file(
                tmp_file.name,
                as_attachment=True,
                download_name=f'transcriptions_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv',
                mimetype='text/csv'
            )

    except Exception as e:
        logger.error(f"❌ خطأ في تصدير CSV: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/export/json')
def export_json():
    """تصدير التحويلات كملف JSON"""
    try:
        transcriptions = enhanced_whisper_manager.get_transcription_history()
        stats = enhanced_whisper_manager.get_transcription_stats()

        export_data = {
            'export_date': datetime.now().isoformat(),
            'stats': stats,
            'transcriptions': transcriptions
        }

        # إنشاء ملف JSON مؤقت
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json', encoding='utf-8') as tmp_file:
            json.dump(export_data, tmp_file, ensure_ascii=False, indent=2)
            tmp_file.flush()

            return send_file(
                tmp_file.name,
                as_attachment=True,
                download_name=f'transcriptions_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json',
                mimetype='application/json'
            )

    except Exception as e:
        logger.error(f"❌ خطأ في تصدير JSON: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/clear-history', methods=['POST'])
def clear_history():
    """مسح تاريخ التحويلات"""
    try:
        enhanced_whisper_manager.transcription_history.clear()
        logger.info("✅ تم مسح تاريخ التحويلات")

        return jsonify({
            'success': True,
            'message': 'تم مسح التاريخ بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في مسح التاريخ: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-connection')
def test_connection():
    """اختبار الاتصال مع Whisper API"""
    try:
        import asyncio

        # تشغيل الاختبار غير المتزامن
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(enhanced_whisper_manager.test_whisper_connection())
        loop.close()

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الاتصال: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stats')
def get_stats():
    """الحصول على الإحصائيات فقط"""
    try:
        stats = enhanced_whisper_manager.get_transcription_stats()
        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على الإحصائيات: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("🚀 بدء واجهة ويب تحويل الصوت إلى نص...")
    logger.info("🌐 الواجهة متاحة على: http://localhost:5001")

    # تعطيل debug mode لتجنب مشاكل watchdog
    app.run(host='0.0.0.0', port=5001, debug=False, use_reloader=False)
