#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل للنظام بعد الإصلاحات
"""

import os
import sys
import sqlite3
import traceback
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database():
    """اختبار قاعدة البيانات"""
    print("💾 اختبار قاعدة البيانات...")
    
    try:
        db_path = "data/articles.db"
        
        if not os.path.exists(db_path):
            print("❌ قاعدة البيانات غير موجودة")
            return False
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # فحص الجداول المطلوبة
            required_tables = [
                'published_articles',
                'monitored_sources',
                'error_log',
                'performance_stats',
                'content_analytics',
                'whisper_quality_logs'
            ]
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            missing_tables = [table for table in required_tables if table not in existing_tables]
            
            if missing_tables:
                print(f"❌ جداول مفقودة: {missing_tables}")
                return False
            else:
                print(f"✅ جميع الجداول موجودة ({len(existing_tables)} جدول)")
            
            # فحص الفهارس
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index';")
            indexes = [row[0] for row in cursor.fetchall()]
            print(f"✅ الفهارس: {len(indexes)} فهرس")
            
            # اختبار العمليات الأساسية
            cursor.execute("SELECT COUNT(*) FROM published_articles")
            article_count = cursor.fetchone()[0]
            print(f"📰 عدد المقالات: {article_count}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_modules_import():
    """اختبار استيراد الوحدات"""
    print("📦 اختبار استيراد الوحدات...")
    
    modules_to_test = [
        ("modules.database", "قاعدة البيانات"),
        ("modules.logger", "نظام السجلات"),
        ("modules.content_scraper", "جامع المحتوى"),
        ("modules.content_generator", "مولد المحتوى"),
        ("modules.publisher", "ناشر المحتوى"),
        ("modules.analytics", "التحليلات"),
        ("modules.error_handler", "معالج الأخطاء"),
        ("config.settings", "الإعدادات")
    ]
    
    success_count = 0
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {description} ({module_name})")
            success_count += 1
        except ImportError as e:
            print(f"❌ {description} ({module_name}): {e}")
        except Exception as e:
            print(f"⚠️ {description} ({module_name}): {e}")
    
    print(f"📊 نجح استيراد {success_count}/{len(modules_to_test)} وحدة")
    return success_count == len(modules_to_test)

def test_configuration():
    """اختبار الإعدادات"""
    print("⚙️ اختبار الإعدادات...")
    
    try:
        from config.settings import BotConfig
        
        # فحص الإعدادات المهمة
        required_configs = [
            'GEMINI_API_KEY',
            'BLOGGER_CLIENT_ID',
            'BLOGGER_CLIENT_SECRET',
            'BLOGGER_BLOG_ID'
        ]
        
        missing_configs = []
        for config in required_configs:
            if not hasattr(BotConfig, config) or not getattr(BotConfig, config):
                missing_configs.append(config)
        
        if missing_configs:
            print(f"⚠️ إعدادات مفقودة: {missing_configs}")
            print("💡 تأكد من وجود ملف .env مع جميع المتغيرات المطلوبة")
            return False
        else:
            print("✅ جميع الإعدادات المطلوبة موجودة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        return False

def test_ai_libraries():
    """اختبار مكتبات الذكاء الاصطناعي"""
    print("🧠 اختبار مكتبات الذكاء الاصطناعي...")
    
    ai_libraries = [
        ("google.generativeai", "Gemini AI"),
        ("requests", "HTTP Requests"),
        ("json", "JSON Processing"),
        ("hashlib", "Hashing"),
        ("datetime", "Date/Time")
    ]
    
    success_count = 0
    
    for lib_name, description in ai_libraries:
        try:
            __import__(lib_name)
            print(f"✅ {description}")
            success_count += 1
        except ImportError:
            print(f"❌ {description} مفقود")
    
    print(f"📊 متوفر {success_count}/{len(ai_libraries)} مكتبة")
    return success_count >= len(ai_libraries) - 1  # السماح بمكتبة واحدة مفقودة

def test_file_structure():
    """اختبار بنية الملفات"""
    print("📁 اختبار بنية الملفات...")
    
    required_files = [
        "main.py",
        "config/settings.py",
        "modules/database.py",
        "modules/logger.py",
        "modules/content_scraper.py",
        "modules/content_generator.py"
    ]
    
    required_dirs = [
        "data",
        "config",
        "modules",
        "logs"
    ]
    
    # فحص الملفات
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    # فحص المجلدات
    missing_dirs = []
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
        else:
            print(f"✅ {dir_path}/")
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {missing_files}")
    
    if missing_dirs:
        print(f"❌ مجلدات مفقودة: {missing_dirs}")
    
    return len(missing_files) == 0 and len(missing_dirs) == 0

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("🔧 اختبار الوظائف الأساسية...")
    
    try:
        # اختبار قاعدة البيانات
        from modules.database import db
        
        # اختبار إنشاء hash
        test_content = "محتوى تجريبي للاختبار"
        content_hash = db.generate_content_hash(test_content)
        print(f"✅ إنشاء hash: {content_hash[:16]}...")
        
        # اختبار فحص التكرار
        is_duplicate, reason = db.is_duplicate_content(test_content, "عنوان تجريبي", ["اختبار"])
        print(f"✅ فحص التكرار: {is_duplicate} - {reason}")
        
        # اختبار السجلات
        from modules.logger import logger
        logger.info("اختبار نظام السجلات")
        print("✅ نظام السجلات يعمل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف الأساسية: {e}")
        traceback.print_exc()
        return False

def generate_test_report():
    """إنشاء تقرير الاختبار"""
    print("\n" + "="*60)
    print("📋 تقرير الاختبار الشامل")
    print("="*60)
    
    tests = [
        (test_file_structure, "بنية الملفات"),
        (test_database, "قاعدة البيانات"),
        (test_modules_import, "استيراد الوحدات"),
        (test_configuration, "الإعدادات"),
        (test_ai_libraries, "مكتبات الذكاء الاصطناعي"),
        (test_basic_functionality, "الوظائف الأساسية")
    ]
    
    results = []
    
    for test_func, test_name in tests:
        print(f"\n🧪 {test_name}:")
        print("-" * 40)
        
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # ملخص النتائج
    print("\n" + "="*60)
    print("📊 ملخص النتائج")
    print("="*60)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ نجح" if success else "❌ فشل"
        print(f"{status} {test_name}")
        if success:
            success_count += 1
    
    success_rate = (success_count / len(results)) * 100
    print(f"\n📈 معدل النجاح: {success_count}/{len(results)} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 النظام جاهز للتشغيل!")
        print("✅ يمكنك تشغيل: python main.py")
    elif success_rate >= 60:
        print("⚠️ النظام يعمل مع بعض المشاكل")
        print("🔧 يُنصح بإصلاح المشاكل المتبقية")
    else:
        print("❌ النظام يحتاج إصلاحات إضافية")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print(f"\n⏰ تم الاختبار في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    generate_test_report()
