@echo off
chcp 65001 >nul
title نظام الفهرسة الذكي - Intelligent Indexing System

echo.
echo ========================================
echo 🔍 نظام الفهرسة الذكي
echo Intelligent Indexing System
echo ========================================
echo.

echo 🚀 بدء تشغيل نظام الفهرسة الذكي...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير متاح في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من وجود الملفات المطلوبة
if not exist "modules\intelligent_indexing_manager.py" (
    echo ❌ ملف مدير الفهرسة غير موجود
    echo تأكد من وجود جميع الملفات المطلوبة
    pause
    exit /b 1
)

if not exist "indexing_web_interface.py" (
    echo ❌ ملف واجهة الويب غير موجود
    echo تأكد من وجود جميع الملفات المطلوبة
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة

REM تثبيت المتطلبات إذا لزم الأمر
echo.
echo 📦 التحقق من المتطلبات...

python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 📥 تثبيت Flask...
    pip install flask
)

python -c "import aiohttp" >nul 2>&1
if errorlevel 1 (
    echo 📥 تثبيت aiohttp...
    pip install aiohttp
)

echo ✅ جميع المتطلبات متوفرة

echo.
echo 🎯 اختر طريقة التشغيل:
echo.
echo 1. تشغيل واجهة الويب فقط
echo 2. تشغيل اختبار سريع
echo 3. تشغيل النظام الكامل
echo 4. تشغيل مع التكامل
echo.

set /p choice="أدخل اختيارك (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🌐 تشغيل واجهة الويب...
    echo ستكون متاحة على: http://localhost:5002
    echo.
    echo اضغط Ctrl+C لإيقاف النظام
    python indexing_web_interface.py
    
) else if "%choice%"=="2" (
    echo.
    echo 🧪 تشغيل اختبار سريع...
    python start_intelligent_indexing.py
    
) else if "%choice%"=="3" (
    echo.
    echo 🚀 تشغيل النظام الكامل...
    echo ستكون واجهة الإدارة متاحة على: http://localhost:5002
    echo.
    echo اضغط Ctrl+C لإيقاف النظام
    start /b python indexing_web_interface.py
    timeout /t 3 /nobreak >nul
    python integrate_intelligent_indexing.py
    
) else if "%choice%"=="4" (
    echo.
    echo 🔗 تشغيل مع التكامل...
    python integrate_intelligent_indexing.py
    
) else (
    echo.
    echo ❌ اختيار غير صحيح
    echo تشغيل الاختبار السريع بدلاً من ذلك...
    python start_intelligent_indexing.py
)

echo.
echo 🎉 انتهى التشغيل
pause
