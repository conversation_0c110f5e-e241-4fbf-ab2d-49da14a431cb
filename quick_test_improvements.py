#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع للتحسينات الجديدة
"""

import sys
import os
import time

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.workflow_settings import WorkflowConfig, AdvancedWorkflowConfig

def test_workflow_config():
    """اختبار إعدادات تدفق العمل"""
    print("🧪 اختبار إعدادات تدفق العمل...")
    print("=" * 50)
    
    # اختبار الإعدادات الأساسية
    print(f"⚡ وقت جمع المحتوى السريع: {WorkflowConfig.FAST_CONTENT_COLLECTION_TIMEOUT} ثانية")
    print(f"📰 عدد المصادر السريعة: {WorkflowConfig.QUICK_SOURCES_COUNT}")
    print(f"📝 مقالات لكل مصدر: {WorkflowConfig.ARTICLES_PER_QUICK_SOURCE}")
    print(f"⏰ الحد الأدنى للانتظار: {WorkflowConfig.MIN_CYCLE_WAIT_TIME / 3600:.1f} ساعة")
    print(f"⏰ الحد الأقصى للانتظار: {WorkflowConfig.MAX_CYCLE_WAIT_TIME / 3600:.1f} ساعة")
    
    # اختبار المهام الخلفية
    print("\n🔧 المهام الخلفية المفعلة:")
    for task, enabled in WorkflowConfig.BACKGROUND_TASKS_ENABLED.items():
        status = "✅" if enabled else "❌"
        interval = WorkflowConfig.get_background_task_interval(task) / 60
        print(f"  {status} {task}: كل {interval:.0f} دقيقة")
    
    # اختبار التحقق من صحة الإعدادات
    print(f"\n✅ صحة الإعدادات: {'نجح' if WorkflowConfig.validate_config() else 'فشل'}")
    
    return True

def test_optimal_wait_time():
    """اختبار حساب وقت الانتظار الأمثل"""
    print("\n🕐 اختبار حساب وقت الانتظار الأمثل...")
    print("=" * 50)
    
    test_cases = [
        (1, 85),  # نشر ناجح، جودة عالية
        (0, 60),  # لا نشر، جودة متوسطة
        (2, 95),  # نشر متعدد، جودة ممتازة
        (0, 40),  # لا نشر، جودة منخفضة
    ]
    
    for published_count, quality in test_cases:
        wait_time = WorkflowConfig.get_optimal_wait_time(published_count, quality)
        wait_hours = wait_time / 3600
        print(f"📊 نشر: {published_count}, جودة: {quality}% → انتظار: {wait_hours:.1f} ساعة")
    
    return True

def test_background_task_scheduling():
    """اختبار جدولة المهام الخلفية"""
    print("\n⏰ اختبار جدولة المهام الخلفية...")
    print("=" * 50)
    
    current_time = time.time()
    
    # محاكاة أوقات تشغيل مختلفة
    test_scenarios = [
        ("article_analysis", 0),      # لم يتم تشغيلها من قبل
        ("seo_analysis", current_time - 300),   # تم تشغيلها قبل 5 دقائق
        ("maintenance", current_time - 1200),   # تم تشغيلها قبل 20 دقيقة
        ("backup_creation", current_time - 3600), # تم تشغيلها قبل ساعة
    ]
    
    for task_name, last_run in test_scenarios:
        should_run = WorkflowConfig.should_run_background_task(task_name, last_run, current_time)
        status = "✅ يجب التشغيل" if should_run else "⏳ انتظار"
        interval = WorkflowConfig.get_background_task_interval(task_name) / 60
        print(f"🔧 {task_name} (كل {interval:.0f} دقيقة): {status}")
    
    return True

def test_advanced_config():
    """اختبار الإعدادات المتقدمة"""
    print("\n🚀 اختبار الإعدادات المتقدمة...")
    print("=" * 50)
    
    print(f"🧠 التحسين التكيفي: {'مفعل' if AdvancedWorkflowConfig.ENABLE_ADAPTIVE_OPTIMIZATION else 'معطل'}")
    
    print("\n📊 عوامل التحسين التكيفي:")
    for factor, weight in AdvancedWorkflowConfig.ADAPTIVE_FACTORS.items():
        print(f"  • {factor}: {weight * 100:.0f}%")
    
    print("\n⚙️ حدود التحسين التلقائي:")
    limits = AdvancedWorkflowConfig.AUTO_OPTIMIZATION_LIMITS
    print(f"  • أقل تقليل للانتظار: {limits['min_wait_reduction'] * 100:.0f}%")
    print(f"  • أقصى زيادة للانتظار: {limits['max_wait_increase'] * 100:.0f}%")
    print(f"  • أقل فاصل للمهام: {limits['min_task_interval'] / 60:.0f} دقيقة")
    print(f"  • أقصى فاصل للمهام: {limits['max_task_interval'] / 60:.0f} دقيقة")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار سريع لتحسينات تدفق العمل")
    print("=" * 60)
    
    tests = [
        ("إعدادات تدفق العمل", test_workflow_config),
        ("وقت الانتظار الأمثل", test_optimal_wait_time),
        ("جدولة المهام الخلفية", test_background_task_scheduling),
        ("الإعدادات المتقدمة", test_advanced_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 تشغيل اختبار: {test_name}")
            if test_func():
                print(f"✅ نجح اختبار: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التحسينات جاهزة للاستخدام.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الإعدادات.")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
