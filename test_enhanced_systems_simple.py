#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للأنظمة المحسنة
"""

import asyncio
import json
import time
import os
from datetime import datetime

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        from modules.logger import logger
        print("✅ تم استيراد logger بنجاح")
    except ImportError as e:
        print(f"❌ فشل في استيراد logger: {e}")
        return False
    
    try:
        from modules.advanced_rag_system import advanced_rag_system
        print("✅ تم استيراد نظام RAG بنجاح")
        print(f"   - حالة النظام: {'مفعل' if advanced_rag_system.enabled else 'معطل'}")
    except ImportError as e:
        print(f"❌ فشل في استيراد نظام RAG: {e}")
        return False
    
    try:
        from modules.multimodal_analyzer import multimodal_analyzer
        print("✅ تم استيراد نظام التحليل متعدد الوسائط بنجاح")
        print(f"   - حالة النظام: {'مفعل' if multimodal_analyzer.enabled else 'معطل'}")
    except ImportError as e:
        print(f"❌ فشل في استيراد نظام التحليل متعدد الوسائط: {e}")
        return False
    
    try:
        from modules.memory_system import memory_system
        print("✅ تم استيراد نظام الذاكرة بنجاح")
    except ImportError as e:
        print(f"❌ فشل في استيراد نظام الذاكرة: {e}")
        return False
    
    try:
        from modules.enhanced_agent_integration import enhanced_agent
        print("✅ تم استيراد نظام التكامل المحسن بنجاح")
    except ImportError as e:
        print(f"❌ فشل في استيراد نظام التكامل المحسن: {e}")
        return False
    
    return True

async def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n🧪 اختبار الوظائف الأساسية...")
    
    try:
        from modules.enhanced_agent_integration import enhanced_agent
        
        # اختبار التحليل المحسن
        print("🔍 اختبار التحليل المحسن...")
        result = await enhanced_agent.enhance_content_analysis(
            content="اختبار بسيط لنظام التحليل المحسن - Minecraft لعبة شائعة",
            content_type="article"
        )
        
        print(f"   - المحتوى الأصلي: اختبار بسيط...")
        print(f"   - مستوى الثقة: {result.confidence:.2f}")
        print(f"   - وقت المعالجة: {result.processing_time:.2f}s")
        print(f"   - مستوى التحسين: {result.enhancement_level.value}")
        
        # اختبار تحسين الاستعلام
        print("\n🔍 اختبار تحسين الاستعلام...")
        enhanced_query = await enhanced_agent.enhance_search_query("ألعاب جديدة")
        print(f"   - الاستعلام الأصلي: ألعاب جديدة")
        print(f"   - الاستعلام المحسن: {enhanced_query}")
        
        # اختبار الإحصائيات
        print("\n📊 اختبار الإحصائيات...")
        stats = await enhanced_agent.get_enhancement_stats()
        print(f"   - إجمالي التحليلات: {stats.get('integration_stats', {}).get('total_enhanced_analyses', 0)}")
        print(f"   - الميزات المفعلة: {stats.get('integration_stats', {}).get('enabled_features', {})}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف الأساسية: {e}")
        return False

def test_file_structure():
    """اختبار بنية الملفات"""
    print("\n📁 اختبار بنية الملفات...")
    
    required_files = [
        "modules/advanced_rag_system.py",
        "modules/multimodal_analyzer.py", 
        "modules/memory_system.py",
        "modules/enhanced_agent_integration.py",
        "ENHANCED_SYSTEMS_GUIDE.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - مفقود")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

async def test_integration_with_main():
    """اختبار التكامل مع الملف الرئيسي"""
    print("\n🔗 اختبار التكامل مع الملف الرئيسي...")
    
    try:
        # فحص إذا كان الملف الرئيسي يحتوي على التحسينات
        with open("main.py", "r", encoding="utf-8") as f:
            main_content = f.read()
        
        integration_checks = [
            ("enhanced_agent_integration", "استيراد نظام التكامل"),
            ("ENHANCED_SYSTEMS_AVAILABLE", "متغير حالة الأنظمة المحسنة"),
            ("enhanced_systems_enabled", "تفعيل الأنظمة المحسنة"),
            ("enhance_content_analysis", "استخدام التحليل المحسن")
        ]
        
        for check, description in integration_checks:
            if check in main_content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description} - غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص التكامل: {e}")
        return False

def generate_simple_report(results):
    """إنشاء تقرير مبسط"""
    print("\n" + "="*60)
    print("📊 تقرير اختبار الأنظمة المحسنة - النسخة المبسطة")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n🎯 النتائج:")
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} - {test_name}")
    
    print(f"\n📈 الإحصائيات:")
    print(f"   📊 إجمالي الاختبارات: {total_tests}")
    print(f"   ✅ نجح: {passed_tests}")
    print(f"   ❌ فشل: {failed_tests}")
    print(f"   📈 معدل النجاح: {success_rate:.1f}%")
    
    # تقييم الأداء
    if success_rate >= 80:
        print("\n🎉 ممتاز! الأنظمة المحسنة جاهزة للاستخدام")
        recommendation = "يمكنك الآن استخدام جميع الميزات المحسنة"
    elif success_rate >= 60:
        print("\n👍 جيد! معظم الأنظمة تعمل بشكل صحيح")
        recommendation = "بعض الميزات قد تحتاج مكتبات إضافية"
    else:
        print("\n⚠️ يحتاج تحسين! هناك مشاكل في التثبيت")
        recommendation = "يرجى مراجعة دليل التثبيت وتثبيت المكتبات المطلوبة"
    
    print(f"\n💡 التوصية: {recommendation}")
    
    # حفظ التقرير
    report_data = {
        "timestamp": datetime.now().isoformat(),
        "test_results": results,
        "statistics": {
            "total_tests": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": success_rate
        },
        "recommendation": recommendation
    }
    
    report_file = f"enhanced_systems_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ التقرير في: {report_file}")

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار الأنظمة المحسنة - النسخة المبسطة")
    print("="*60)
    
    start_time = datetime.now()
    
    # تشغيل الاختبارات
    results = {}
    
    # اختبار الاستيراد
    results["استيراد الوحدات"] = test_imports()
    
    # اختبار بنية الملفات
    results["بنية الملفات"] = test_file_structure()
    
    # اختبار التكامل
    results["التكامل مع الملف الرئيسي"] = await test_integration_with_main()
    
    # اختبار الوظائف الأساسية
    if results["استيراد الوحدات"]:
        results["الوظائف الأساسية"] = await test_basic_functionality()
    else:
        results["الوظائف الأساسية"] = False
        print("⏭️ تم تخطي اختبار الوظائف الأساسية بسبب فشل الاستيراد")
    
    end_time = datetime.now()
    execution_time = (end_time - start_time).total_seconds()
    
    print(f"\n⏱️ وقت التنفيذ: {execution_time:.2f} ثانية")
    
    # إنشاء التقرير
    generate_simple_report(results)

if __name__ == "__main__":
    asyncio.run(main())
