#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مشاكل التوافق مع Python 3.13
"""

import subprocess
import sys
import os

def fix_httpx_version_conflict():
    """إصلاح تعارض إصدارات httpx"""
    print("🔧 إصلاح تعارض إصدارات httpx...")
    
    try:
        # إلغاء تثبيت googletrans المشكل
        print("📦 إلغاء تثبيت googletrans...")
        subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "googletrans", "-y"])
        
        # إلغاء تثبيت httpx القديم
        print("📦 إلغاء تثبيت httpx القديم...")
        subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "httpx", "-y"])
        
        # تثبيت httpx الحديث
        print("📦 تثبيت httpx الحديث...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "httpx>=0.25.0"])
        
        # تثبيت googletrans بديل متوافق
        print("📦 تثبيت googletrans متوافق...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "deep-translator"])
        
        print("✅ تم إصلاح تعارض httpx")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إصلاح httpx: {e}")
        return False

def install_cgi_replacement():
    """تثبيت بديل لمكتبة cgi"""
    print("🔧 تثبيت بديل لمكتبة cgi...")
    
    try:
        # تثبيت cgi-compat للتوافق مع Python 3.13
        subprocess.check_call([sys.executable, "-m", "pip", "install", "cgi-compat"])
        print("✅ تم تثبيت بديل cgi")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت بديل cgi: {e}")
        return False

def fix_telegram_compatibility():
    """إصلاح مشاكل التوافق مع telegram"""
    print("🔧 إصلاح مشاكل telegram...")
    
    try:
        # تحديث python-telegram-bot للإصدار المتوافق
        print("📦 تحديث python-telegram-bot...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "python-telegram-bot>=20.7"])
        
        print("✅ تم تحديث telegram")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تحديث telegram: {e}")
        return False

def create_cgi_compatibility_module():
    """إنشاء وحدة توافق لـ cgi"""
    print("📝 إنشاء وحدة توافق cgi...")
    
    cgi_compat_code = '''
"""
وحدة توافق cgi لـ Python 3.13
"""

import urllib.parse
import io
import sys
from typing import Dict, List, Any, Optional, Union

# إعادة تعريف الدوال المهمة من cgi
def parse_qs(qs: str, keep_blank_values: bool = False, strict_parsing: bool = False) -> Dict[str, List[str]]:
    """تحليل query string"""
    return urllib.parse.parse_qs(qs, keep_blank_values, strict_parsing)

def parse_qsl(qs: str, keep_blank_values: bool = False, strict_parsing: bool = False) -> List[tuple]:
    """تحليل query string إلى قائمة من tuples"""
    return urllib.parse.parse_qsl(qs, keep_blank_values, strict_parsing)

def escape(s: str, quote: bool = True) -> str:
    """تشفير HTML"""
    import html
    return html.escape(s, quote)

def unescape(s: str) -> str:
    """إلغاء تشفير HTML"""
    import html
    return html.unescape(s)

class FieldStorage:
    """بديل مبسط لـ FieldStorage"""
    
    def __init__(self, fp=None, headers=None, outerboundary=None, 
                 environ=None, keep_blank_values=0, strict_parsing=0):
        self.list = []
        self.file = None
        self.filename = None
        self.name = None
        self.value = None
        
    def getvalue(self, key: str, default: Any = None) -> Any:
        """الحصول على قيمة"""
        return default
        
    def getlist(self, key: str) -> List[Any]:
        """الحصول على قائمة قيم"""
        return []

# متغيرات مهمة
maxlen = 0

# دوال إضافية للتوافق
def print_exception(type=None, value=None, tb=None, limit=None, file=None):
    """طباعة استثناء"""
    import traceback
    traceback.print_exception(type, value, tb, limit, file)

def print_environ(environ=None):
    """طباعة متغيرات البيئة"""
    if environ is None:
        environ = os.environ
    for key, value in environ.items():
        print(f"{key}={value}")

def print_form(form):
    """طباعة نموذج"""
    print("Form data:")
    if hasattr(form, 'list'):
        for item in form.list:
            print(f"  {item.name}: {item.value}")

def print_directory():
    """طباعة دليل"""
    print("CGI Directory listing not available")

def print_arguments():
    """طباعة المعاملات"""
    print("Arguments:", sys.argv)

def print_environ_usage():
    """طباعة استخدام متغيرات البيئة"""
    print("Environment variables usage information not available")
'''
    
    try:
        # إنشاء مجلد للوحدات المساعدة
        compat_dir = "compat_modules"
        os.makedirs(compat_dir, exist_ok=True)
        
        # كتابة وحدة cgi
        with open(os.path.join(compat_dir, "cgi.py"), 'w', encoding='utf-8') as f:
            f.write(cgi_compat_code)
        
        # إنشاء __init__.py
        with open(os.path.join(compat_dir, "__init__.py"), 'w', encoding='utf-8') as f:
            f.write("# وحدات التوافق\n")
        
        # إضافة المسار إلى sys.path
        sys.path.insert(0, compat_dir)
        
        print("✅ تم إنشاء وحدة توافق cgi")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء وحدة cgi: {e}")
        return False

def patch_publisher_module():
    """تصحيح وحدة الناشر لتجنب telegram"""
    print("🔧 تصحيح وحدة الناشر...")
    
    try:
        # قراءة ملف الناشر
        publisher_path = "modules/publisher.py"
        
        if not os.path.exists(publisher_path):
            print("⚠️ ملف الناشر غير موجود")
            return False
        
        with open(publisher_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استبدال استيراد telegram بـ try/except
        old_import = "import telegram"
        new_import = """try:
    import telegram
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False
    telegram = None"""
        
        if old_import in content:
            content = content.replace(old_import, new_import)
            
            # إضافة فحص توفر telegram في الكلاس
            old_init = "def __init__(self, blogger_config, telegram_config):"
            new_init = """def __init__(self, blogger_config, telegram_config):
        # فحص توفر telegram
        if telegram_config and not TELEGRAM_AVAILABLE:
            print("⚠️ Telegram غير متوفر - سيتم تعطيله")
            telegram_config = None"""
            
            content = content.replace(old_init, new_init)
            
            # كتابة الملف المحدث
            with open(publisher_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم تصحيح وحدة الناشر")
            return True
        else:
            print("ℹ️ وحدة الناشر لا تحتاج تصحيح")
            return True
            
    except Exception as e:
        print(f"❌ فشل في تصحيح وحدة الناشر: {e}")
        return False

def create_python313_startup_script():
    """إنشاء سكريبت بدء تشغيل متوافق مع Python 3.13"""
    print("📝 إنشاء سكريبت بدء تشغيل متوافق...")
    
    startup_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت بدء تشغيل متوافق مع Python 3.13
"""

import os
import sys
import warnings

# إخفاء جميع التحذيرات
warnings.filterwarnings("ignore")

# إضافة وحدات التوافق
compat_dir = os.path.join(os.path.dirname(__file__), "compat_modules")
if os.path.exists(compat_dir):
    sys.path.insert(0, compat_dir)

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# تطبيق إصلاحات التوافق
try:
    # إنشاء وحدة cgi مؤقتة إذا لم تكن موجودة
    try:
        import cgi
    except ImportError:
        # إنشاء وحدة cgi بديلة
        import types
        cgi_module = types.ModuleType('cgi')
        
        # إضافة الدوال الأساسية
        import urllib.parse
        import html
        
        cgi_module.parse_qs = urllib.parse.parse_qs
        cgi_module.parse_qsl = urllib.parse.parse_qsl
        cgi_module.escape = html.escape
        cgi_module.unescape = html.unescape
        
        # إضافة كلاس FieldStorage بديل
        class FieldStorage:
            def __init__(self, *args, **kwargs):
                self.list = []
                self.file = None
                self.filename = None
                self.name = None
                self.value = None
            
            def getvalue(self, key, default=None):
                return default
                
            def getlist(self, key):
                return []
        
        cgi_module.FieldStorage = FieldStorage
        cgi_module.maxlen = 0
        
        # تسجيل الوحدة
        sys.modules['cgi'] = cgi_module
        print("✅ تم إنشاء وحدة cgi بديلة")

except Exception as e:
    print(f"⚠️ تحذير في إصلاحات التوافق: {e}")

# تشغيل البرنامج الرئيسي
if __name__ == "__main__":
    try:
        # استيراد وتشغيل main
        import main
        
        # إنشاء حلقة أحداث جديدة
        import asyncio
        
        # تشغيل البوت
        bot = main.GamingNewsBot()
        asyncio.run(bot.run())
        
    except KeyboardInterrupt:
        print("\\n🛑 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        import traceback
        traceback.print_exc()
'''
    
    try:
        with open("start_python313.py", 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        print("✅ تم إنشاء سكريبت البدء المتوافق: start_python313.py")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء سكريبت البدء: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح مشاكل التوافق مع Python 3.13...")
    
    fixes = [
        (create_cgi_compatibility_module, "إنشاء وحدة توافق cgi"),
        (fix_httpx_version_conflict, "إصلاح تعارض httpx"),
        (fix_telegram_compatibility, "إصلاح مشاكل telegram"),
        (patch_publisher_module, "تصحيح وحدة الناشر"),
        (create_python313_startup_script, "إنشاء سكريبت بدء متوافق")
    ]
    
    success_count = 0
    
    for fix_func, description in fixes:
        print(f"\n🔧 {description}...")
        try:
            if fix_func():
                success_count += 1
        except Exception as e:
            print(f"❌ خطأ في {description}: {e}")
    
    print(f"\n📊 النتائج: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count >= len(fixes) - 1:
        print("✅ تم إصلاح مشاكل التوافق!")
        print("🎯 يمكنك الآن تشغيل البرنامج:")
        print("   python start_python313.py")
    else:
        print("⚠️ تم إصلاح معظم مشاكل التوافق")

if __name__ == "__main__":
    main()
