#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام الكامل لتحويل الصوت إلى نص
Complete Audio-to-Text System Launcher
"""

import sys
import os
import threading
import time
import subprocess
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger


class CompleteWhisperSystem:
    """نظام Whisper الكامل"""
    
    def __init__(self):
        self.whisper_server_process = None
        self.web_interface_thread = None
        self.system_ready = False
        
    def check_requirements(self):
        """فحص المتطلبات"""
        logger.info("🔍 فحص متطلبات النظام...")
        
        requirements = {
            'whisper_model': os.path.exists('huggingface_upload/models/small.pt'),
            'whisper_app': os.path.exists('huggingface_upload/app.py'),
            'enhanced_manager': os.path.exists('modules/enhanced_whisper_manager.py'),
            'web_interface': os.path.exists('whisper_web_interface.py'),
            'integration_script': os.path.exists('integrate_enhanced_whisper.py')
        }
        
        logger.info("📋 حالة المتطلبات:")
        for req, status in requirements.items():
            status_icon = "✅" if status else "❌"
            logger.info(f"   {status_icon} {req}: {status}")
        
        missing = [req for req, status in requirements.items() if not status]
        if missing:
            logger.error(f"❌ متطلبات مفقودة: {', '.join(missing)}")
            return False
        
        logger.info("✅ جميع المتطلبات متوفرة")
        return True
    
    def start_whisper_server(self):
        """تشغيل خادم Whisper"""
        try:
            logger.info("🚀 بدء تشغيل خادم Whisper...")
            
            # تحديث الإعدادات أولاً
            self._update_config()
            
            # تشغيل خادم Whisper
            cmd = [sys.executable, "start_local_whisper_server.py"]
            
            self.whisper_server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # انتظار بدء الخادم
            time.sleep(10)
            
            # اختبار الخادم
            if self._test_whisper_server():
                logger.info("✅ خادم Whisper يعمل بنجاح")
                return True
            else:
                logger.error("❌ فشل في تشغيل خادم Whisper")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل خادم Whisper: {e}")
            return False
    
    def start_web_interface(self):
        """تشغيل واجهة الويب"""
        try:
            logger.info("🌐 بدء تشغيل واجهة الويب...")
            
            def run_web_interface():
                try:
                    import whisper_web_interface
                    whisper_web_interface.app.run(
                        host='0.0.0.0',
                        port=5001,
                        debug=False,
                        use_reloader=False
                    )
                except Exception as e:
                    logger.error(f"❌ خطأ في واجهة الويب: {e}")
            
            self.web_interface_thread = threading.Thread(
                target=run_web_interface,
                daemon=True
            )
            self.web_interface_thread.start()
            
            # انتظار بدء واجهة الويب
            time.sleep(5)
            
            # اختبار واجهة الويب
            if self._test_web_interface():
                logger.info("✅ واجهة الويب تعمل بنجاح")
                return True
            else:
                logger.error("❌ فشل في تشغيل واجهة الويب")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل واجهة الويب: {e}")
            return False
    
    def integrate_with_agent(self):
        """تكامل مع الوكيل الحالي"""
        try:
            logger.info("🔗 تكامل مع الوكيل الحالي...")
            
            # تشغيل سكريبت التكامل
            cmd = [sys.executable, "integrate_enhanced_whisper.py"]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                logger.info("✅ تم التكامل بنجاح")
                return True
            else:
                logger.warning("⚠️ مشاكل في التكامل، لكن النظام سيعمل")
                logger.warning(f"تفاصيل: {result.stderr}")
                return True  # نعتبره نجاح جزئي
                
        except Exception as e:
            logger.warning(f"⚠️ خطأ في التكامل: {e}")
            return True  # نعتبره نجاح جزئي
    
    def _update_config(self):
        """تحديث إعدادات النظام"""
        try:
            config_path = "config/settings.py"
            
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحديث رابط API
            old_url = '"https://nanami34-ai55.hf.space/api/transcribe"'
            new_url = '"http://localhost:7860/api/transcribe"'
            
            if old_url in content:
                content = content.replace(old_url, new_url)
                
                with open(config_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info("✅ تم تحديث إعدادات API")
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحديث الإعدادات: {e}")
    
    def _test_whisper_server(self):
        """اختبار خادم Whisper"""
        try:
            import requests
            response = requests.get("http://localhost:7860/health", timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def _test_web_interface(self):
        """اختبار واجهة الويب"""
        try:
            import requests
            response = requests.get("http://localhost:5001", timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def run_system_test(self):
        """تشغيل اختبار شامل للنظام"""
        logger.info("🧪 تشغيل اختبار شامل للنظام...")
        
        try:
            # اختبار النظام المحسن
            cmd = [sys.executable, "quick_test_enhanced_whisper.py"]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                logger.info("✅ اختبار النظام نجح")
                return True
            else:
                logger.warning("⚠️ مشاكل في اختبار النظام")
                logger.warning(f"تفاصيل: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار النظام: {e}")
            return False
    
    def display_system_status(self):
        """عرض حالة النظام"""
        logger.info("\n" + "🎤" * 60)
        logger.info("🎤 حالة نظام Whisper الكامل")
        logger.info("🎤" * 60)
        
        logger.info("🌐 الخدمات المتاحة:")
        logger.info("   📱 واجهة الويب: http://localhost:5001")
        logger.info("   🎤 خادم Whisper: http://localhost:7860")
        logger.info("   🔧 API للتطوير: http://localhost:5001/api/")
        
        logger.info("\n📚 الميزات:")
        logger.info("   ✅ تحويل صوت إلى نص محسن")
        logger.info("   ✅ طرق رفع متعددة")
        logger.info("   ✅ واجهة ويب تفاعلية")
        logger.info("   ✅ إحصائيات مفصلة")
        logger.info("   ✅ تصدير النتائج")
        logger.info("   ✅ تكامل مع الوكيل")
        
        logger.info("\n🎤" * 60)
    
    def run(self):
        """تشغيل النظام الكامل"""
        logger.info("🚀 بدء تشغيل نظام Whisper الكامل...")
        logger.info(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)
        
        try:
            # 1. فحص المتطلبات
            if not self.check_requirements():
                return False
            
            # 2. تشغيل خادم Whisper
            if not self.start_whisper_server():
                logger.error("❌ فشل في تشغيل خادم Whisper")
                return False
            
            # 3. تشغيل واجهة الويب
            if not self.start_web_interface():
                logger.error("❌ فشل في تشغيل واجهة الويب")
                return False
            
            # 4. تكامل مع الوكيل
            self.integrate_with_agent()
            
            # 5. اختبار النظام
            self.run_system_test()
            
            # 6. عرض حالة النظام
            self.display_system_status()
            
            self.system_ready = True
            
            # 7. إبقاء النظام يعمل
            logger.info("\n🎉 النظام جاهز للاستخدام!")
            logger.info("⏳ النظام يعمل... اضغط Ctrl+C للإيقاف")
            
            try:
                while True:
                    time.sleep(60)
                    
                    # فحص دوري للخدمات
                    if not self._test_whisper_server():
                        logger.warning("⚠️ مشكلة في خادم Whisper")
                    
                    if not self._test_web_interface():
                        logger.warning("⚠️ مشكلة في واجهة الويب")
                        
            except KeyboardInterrupt:
                logger.info("\n🛑 تم إيقاف النظام بواسطة المستخدم")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل النظام: {e}")
            return False
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """تنظيف الموارد"""
        logger.info("🧹 تنظيف الموارد...")
        
        if self.whisper_server_process:
            try:
                self.whisper_server_process.terminate()
                logger.info("✅ تم إيقاف خادم Whisper")
            except:
                pass
        
        logger.info("👋 تم إنهاء النظام")


def main():
    """الدالة الرئيسية"""
    system = CompleteWhisperSystem()
    success = system.run()
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
