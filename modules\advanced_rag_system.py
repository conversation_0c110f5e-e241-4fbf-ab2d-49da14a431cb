# نظام RAG متقدم مع Multi-modal RAG + Knowledge Graph
import asyncio
import json
import time
import hashlib
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
import sqlite3
import pickle
import base64
from pathlib import Path

# مكتبات الذكاء الاصطناعي
try:
    import openai
    from sentence_transformers import SentenceTransformer
    import faiss
    import networkx as nx
    from transformers import pipeline, AutoTokenizer, AutoModel
    import torch
    from PIL import Image
    import cv2
    import pytesseract
    HAS_AI_LIBS = True
except ImportError as e:
    HAS_AI_LIBS = False
    # إنشاء بدائل وهمية للمكتبات المفقودة
    class DummyGraph:
        def __init__(self):
            pass
        def add_node(self, *args, **kwargs):
            pass
        def add_edge(self, *args, **kwargs):
            pass
        def has_node(self, *args, **kwargs):
            return False
        def has_edge(self, *args, **kwargs):
            return False
        def number_of_nodes(self):
            return 0
        def number_of_edges(self):
            return 0

    class DummyNX:
        Graph = DummyGraph
        NetworkXNoPath = Exception
        def shortest_path_length(self, *args, **kwargs):
            raise self.NetworkXNoPath()

    nx = DummyNX()

from .logger import logger
from config.settings import BotConfig

class RAGMode(Enum):
    """أنماط RAG المختلفة"""
    TEXT_ONLY = "text_only"
    MULTIMODAL = "multimodal"
    KNOWLEDGE_GRAPH = "knowledge_graph"
    HYBRID = "hybrid"

class ContentType(Enum):
    """أنواع المحتوى"""
    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    MIXED = "mixed"

@dataclass
class RAGDocument:
    """وثيقة RAG"""
    id: str
    content: str
    content_type: ContentType
    metadata: Dict[str, Any]
    embeddings: Optional[List[float]] = None
    knowledge_entities: Optional[List[str]] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class RAGQuery:
    """استعلام RAG"""
    query: str
    mode: RAGMode
    content_types: List[ContentType]
    max_results: int = 10
    similarity_threshold: float = 0.7
    include_metadata: bool = True

@dataclass
class RAGResult:
    """نتيجة RAG"""
    document: RAGDocument
    similarity_score: float
    relevance_score: float
    context_score: float
    final_score: float

class AdvancedRAGSystem:
    """نظام RAG متقدم مع دعم Multi-modal + Knowledge Graph"""
    
    def __init__(self):
        self.enabled = HAS_AI_LIBS
        self.documents = {}
        self.embeddings_cache = {}
        self.knowledge_graph = nx.Graph()
        self.entity_embeddings = {}
        
        # إحصائيات
        self.stats = {
            'total_documents': 0,
            'total_queries': 0,
            'cache_hits': 0,
            'avg_query_time': 0,
            'last_update': datetime.now()
        }
        
        # إعدادات
        self.config = {
            'embedding_model': 'all-MiniLM-L6-v2',
            'max_documents': 10000,
            'cache_size': 1000,
            'similarity_threshold': 0.7,
            'knowledge_graph_enabled': True,
            'multimodal_enabled': True
        }
        
        if self.enabled:
            self._initialize_models()
        else:
            logger.warning("⚠️ مكتبات الذكاء الاصطناعي غير متوفرة - RAG معطل")
    
    def _initialize_models(self):
        """تهيئة النماذج"""
        try:
            logger.info("🤖 تهيئة نماذج RAG...")
            
            # نموذج التضمين النصي
            self.text_encoder = SentenceTransformer(self.config['embedding_model'])
            
            # نموذج تحليل الصور
            if self.config['multimodal_enabled']:
                try:
                    self.image_processor = pipeline("image-to-text", 
                                                   model="Salesforce/blip-image-captioning-base")
                    self.ocr_enabled = True
                except:
                    self.image_processor = None
                    self.ocr_enabled = False
                    logger.warning("⚠️ فشل في تحميل معالج الصور")
            
            # فهرس FAISS للبحث السريع
            self.faiss_index = None
            self.document_ids = []
            
            logger.info("✅ تم تهيئة نماذج RAG بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة نماذج RAG: {e}")
            self.enabled = False
    
    async def add_document(self, document: RAGDocument) -> bool:
        """إضافة وثيقة جديدة"""
        if not self.enabled:
            return False
            
        try:
            logger.info(f"📄 إضافة وثيقة جديدة: {document.id}")
            
            # توليد التضمينات
            embeddings = await self._generate_embeddings(document)
            document.embeddings = embeddings
            
            # استخراج الكيانات للـ Knowledge Graph
            if self.config['knowledge_graph_enabled']:
                entities = await self._extract_entities(document)
                document.knowledge_entities = entities
                await self._update_knowledge_graph(document, entities)
            
            # إضافة للفهرس
            self.documents[document.id] = document
            await self._update_faiss_index(document)
            
            self.stats['total_documents'] += 1
            logger.info(f"✅ تم إضافة الوثيقة بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة الوثيقة: {e}")
            return False
    
    async def search(self, query: RAGQuery) -> List[RAGResult]:
        """البحث في الوثائق"""
        if not self.enabled:
            return []
            
        try:
            start_time = time.time()
            self.stats['total_queries'] += 1
            
            logger.info(f"🔍 بحث RAG: {query.query} (نمط: {query.mode.value})")
            
            # توليد تضمينات الاستعلام
            query_embeddings = await self._generate_query_embeddings(query.query)
            
            # البحث حسب النمط
            if query.mode == RAGMode.TEXT_ONLY:
                results = await self._text_search(query, query_embeddings)
            elif query.mode == RAGMode.MULTIMODAL:
                results = await self._multimodal_search(query, query_embeddings)
            elif query.mode == RAGMode.KNOWLEDGE_GRAPH:
                results = await self._knowledge_graph_search(query, query_embeddings)
            else:  # HYBRID
                results = await self._hybrid_search(query, query_embeddings)
            
            # ترتيب النتائج
            results = sorted(results, key=lambda x: x.final_score, reverse=True)
            results = results[:query.max_results]
            
            # تحديث الإحصائيات
            query_time = time.time() - start_time
            self.stats['avg_query_time'] = (self.stats['avg_query_time'] + query_time) / 2
            
            logger.info(f"✅ تم العثور على {len(results)} نتيجة في {query_time:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث: {e}")
            return []
    
    async def _generate_embeddings(self, document: RAGDocument) -> List[float]:
        """توليد التضمينات للوثيقة"""
        try:
            if document.content_type == ContentType.TEXT:
                return self.text_encoder.encode(document.content).tolist()
            elif document.content_type == ContentType.IMAGE:
                return await self._generate_image_embeddings(document)
            elif document.content_type == ContentType.MIXED:
                return await self._generate_mixed_embeddings(document)
            else:
                return self.text_encoder.encode(document.content).tolist()
                
        except Exception as e:
            logger.error(f"❌ خطأ في توليد التضمينات: {e}")
            return []
    
    async def _generate_image_embeddings(self, document: RAGDocument) -> List[float]:
        """توليد تضمينات للصور"""
        try:
            if not self.image_processor:
                return []
            
            # تحليل الصورة لاستخراج النص الوصفي
            image_description = self.image_processor(document.content)
            if isinstance(image_description, list) and len(image_description) > 0:
                description_text = image_description[0].get('generated_text', '')
            else:
                description_text = str(image_description)
            
            # OCR لاستخراج النص من الصورة
            if self.ocr_enabled:
                try:
                    ocr_text = pytesseract.image_to_string(Image.open(document.content))
                    description_text += " " + ocr_text
                except:
                    pass
            
            # توليد التضمينات للنص المستخرج
            return self.text_encoder.encode(description_text).tolist()
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل الصورة: {e}")
            return []

    async def _extract_entities(self, document: RAGDocument) -> List[str]:
        """استخراج الكيانات من الوثيقة"""
        try:
            # استخراج الكيانات البسيط (يمكن تحسينه بـ NER)
            content = document.content.lower()

            # كيانات الألعاب الشائعة
            gaming_entities = [
                'minecraft', 'fortnite', 'call of duty', 'fifa', 'gta',
                'league of legends', 'valorant', 'apex legends', 'overwatch',
                'world of warcraft', 'cyberpunk', 'witcher', 'assassins creed',
                'playstation', 'xbox', 'nintendo', 'steam', 'epic games'
            ]

            found_entities = []
            for entity in gaming_entities:
                if entity in content:
                    found_entities.append(entity)

            # إضافة كيانات من الميتاداتا
            if 'tags' in document.metadata:
                found_entities.extend(document.metadata['tags'])

            return list(set(found_entities))

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج الكيانات: {e}")
            return []

    async def _update_knowledge_graph(self, document: RAGDocument, entities: List[str]):
        """تحديث الرسم البياني للمعرفة"""
        try:
            # إضافة الوثيقة كعقدة
            self.knowledge_graph.add_node(document.id,
                                        type='document',
                                        content_type=document.content_type.value,
                                        created_at=document.created_at)

            # إضافة الكيانات وربطها بالوثيقة
            for entity in entities:
                if not self.knowledge_graph.has_node(entity):
                    self.knowledge_graph.add_node(entity, type='entity')

                self.knowledge_graph.add_edge(document.id, entity,
                                            relation='contains')

            # ربط الكيانات المترابطة
            for i, entity1 in enumerate(entities):
                for entity2 in entities[i+1:]:
                    if self.knowledge_graph.has_edge(entity1, entity2):
                        # زيادة قوة الرابط
                        self.knowledge_graph[entity1][entity2]['weight'] = \
                            self.knowledge_graph[entity1][entity2].get('weight', 0) + 1
                    else:
                        self.knowledge_graph.add_edge(entity1, entity2,
                                                    relation='co_occurs', weight=1)

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث الرسم البياني: {e}")

    async def _update_faiss_index(self, document: RAGDocument):
        """تحديث فهرس FAISS"""
        try:
            if not document.embeddings:
                return

            embeddings_array = np.array([document.embeddings], dtype=np.float32)

            if self.faiss_index is None:
                # إنشاء فهرس جديد
                dimension = len(document.embeddings)
                self.faiss_index = faiss.IndexFlatIP(dimension)

            self.faiss_index.add(embeddings_array)
            self.document_ids.append(document.id)

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث فهرس FAISS: {e}")

    async def _generate_query_embeddings(self, query: str) -> List[float]:
        """توليد تضمينات للاستعلام"""
        try:
            return self.text_encoder.encode(query).tolist()
        except Exception as e:
            logger.error(f"❌ خطأ في توليد تضمينات الاستعلام: {e}")
            return []

    async def _text_search(self, query: RAGQuery, query_embeddings: List[float]) -> List[RAGResult]:
        """البحث النصي"""
        try:
            if not self.faiss_index or not query_embeddings:
                return []

            # البحث في FAISS
            query_array = np.array([query_embeddings], dtype=np.float32)
            scores, indices = self.faiss_index.search(query_array, query.max_results * 2)

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= len(self.document_ids):
                    continue

                doc_id = self.document_ids[idx]
                if doc_id not in self.documents:
                    continue

                document = self.documents[doc_id]

                # فلترة حسب نوع المحتوى
                if document.content_type not in query.content_types:
                    continue

                # حساب النقاط
                similarity_score = float(score)
                if similarity_score < query.similarity_threshold:
                    continue

                relevance_score = await self._calculate_relevance(query.query, document)
                context_score = await self._calculate_context_score(document)
                final_score = (similarity_score * 0.5 + relevance_score * 0.3 + context_score * 0.2)

                results.append(RAGResult(
                    document=document,
                    similarity_score=similarity_score,
                    relevance_score=relevance_score,
                    context_score=context_score,
                    final_score=final_score
                ))

            return results

        except Exception as e:
            logger.error(f"❌ خطأ في البحث النصي: {e}")
            return []

    async def _multimodal_search(self, query: RAGQuery, query_embeddings: List[float]) -> List[RAGResult]:
        """البحث متعدد الوسائط"""
        try:
            # البحث النصي أولاً
            text_results = await self._text_search(query, query_embeddings)

            # تحسين النتائج للمحتوى متعدد الوسائط
            enhanced_results = []
            for result in text_results:
                if result.document.content_type in [ContentType.IMAGE, ContentType.VIDEO, ContentType.MIXED]:
                    # زيادة النقاط للمحتوى متعدد الوسائط
                    result.final_score *= 1.2

                enhanced_results.append(result)

            return enhanced_results

        except Exception as e:
            logger.error(f"❌ خطأ في البحث متعدد الوسائط: {e}")
            return []

    async def _knowledge_graph_search(self, query: RAGQuery, query_embeddings: List[float]) -> List[RAGResult]:
        """البحث باستخدام الرسم البياني للمعرفة"""
        try:
            # البحث النصي أولاً
            text_results = await self._text_search(query, query_embeddings)

            # تحسين النتائج باستخدام الرسم البياني
            enhanced_results = []
            query_entities = await self._extract_query_entities(query.query)

            for result in text_results:
                graph_score = await self._calculate_graph_score(result.document, query_entities)
                result.context_score = (result.context_score + graph_score) / 2
                result.final_score = (result.similarity_score * 0.4 +
                                    result.relevance_score * 0.3 +
                                    result.context_score * 0.3)
                enhanced_results.append(result)

            return enhanced_results

        except Exception as e:
            logger.error(f"❌ خطأ في البحث بالرسم البياني: {e}")
            return []

    async def _hybrid_search(self, query: RAGQuery, query_embeddings: List[float]) -> List[RAGResult]:
        """البحث المختلط"""
        try:
            # دمج جميع أنواع البحث
            text_results = await self._text_search(query, query_embeddings)
            multimodal_results = await self._multimodal_search(query, query_embeddings)
            graph_results = await self._knowledge_graph_search(query, query_embeddings)

            # دمج النتائج وإزالة التكرار
            all_results = {}

            for results in [text_results, multimodal_results, graph_results]:
                for result in results:
                    doc_id = result.document.id
                    if doc_id in all_results:
                        # متوسط النقاط
                        existing = all_results[doc_id]
                        existing.final_score = (existing.final_score + result.final_score) / 2
                    else:
                        all_results[doc_id] = result

            return list(all_results.values())

        except Exception as e:
            logger.error(f"❌ خطأ في البحث المختلط: {e}")
            return []

    async def _calculate_relevance(self, query: str, document: RAGDocument) -> float:
        """حساب مدى الصلة"""
        try:
            query_words = set(query.lower().split())
            content_words = set(document.content.lower().split())

            # تقاطع الكلمات
            intersection = query_words.intersection(content_words)
            union = query_words.union(content_words)

            if len(union) == 0:
                return 0.0

            jaccard_similarity = len(intersection) / len(union)

            # تحسين بناءً على الميتاداتا
            metadata_boost = 0.0
            if 'tags' in document.metadata:
                for tag in document.metadata['tags']:
                    if tag.lower() in query.lower():
                        metadata_boost += 0.1

            return min(1.0, jaccard_similarity + metadata_boost)

        except Exception as e:
            logger.error(f"❌ خطأ في حساب الصلة: {e}")
            return 0.0

    async def _calculate_context_score(self, document: RAGDocument) -> float:
        """حساب نقاط السياق"""
        try:
            score = 0.5  # نقاط أساسية

            # تحسين بناءً على حداثة المحتوى
            age_days = (datetime.now() - document.created_at).days
            if age_days <= 1:
                score += 0.3
            elif age_days <= 7:
                score += 0.2
            elif age_days <= 30:
                score += 0.1

            # تحسين بناءً على نوع المحتوى
            if document.content_type == ContentType.MIXED:
                score += 0.1
            elif document.content_type == ContentType.IMAGE:
                score += 0.05

            # تحسين بناءً على طول المحتوى
            content_length = len(document.content)
            if 100 <= content_length <= 1000:
                score += 0.1
            elif content_length > 1000:
                score += 0.05

            return min(1.0, score)

        except Exception as e:
            logger.error(f"❌ خطأ في حساب نقاط السياق: {e}")
            return 0.5

    async def _extract_query_entities(self, query: str) -> List[str]:
        """استخراج الكيانات من الاستعلام"""
        try:
            query_lower = query.lower()
            entities = []

            # كيانات الألعاب
            gaming_entities = [
                'minecraft', 'fortnite', 'call of duty', 'fifa', 'gta',
                'league of legends', 'valorant', 'apex legends', 'overwatch'
            ]

            for entity in gaming_entities:
                if entity in query_lower:
                    entities.append(entity)

            return entities

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج كيانات الاستعلام: {e}")
            return []

    async def _calculate_graph_score(self, document: RAGDocument, query_entities: List[str]) -> float:
        """حساب نقاط الرسم البياني"""
        try:
            if not query_entities or not self.knowledge_graph.has_node(document.id):
                return 0.5

            score = 0.0
            total_entities = len(query_entities)

            for entity in query_entities:
                if self.knowledge_graph.has_node(entity):
                    # فحص الاتصال المباشر
                    if self.knowledge_graph.has_edge(document.id, entity):
                        score += 1.0
                    else:
                        # فحص الاتصال غير المباشر
                        try:
                            path_length = nx.shortest_path_length(
                                self.knowledge_graph, document.id, entity)
                            if path_length <= 3:
                                score += 1.0 / path_length
                        except nx.NetworkXNoPath:
                            pass

            return min(1.0, score / max(1, total_entities))

        except Exception as e:
            logger.error(f"❌ خطأ في حساب نقاط الرسم البياني: {e}")
            return 0.5

    async def get_stats(self) -> Dict[str, Any]:
        """الحصول على الإحصائيات"""
        return {
            **self.stats,
            'documents_count': len(self.documents),
            'knowledge_graph_nodes': self.knowledge_graph.number_of_nodes(),
            'knowledge_graph_edges': self.knowledge_graph.number_of_edges(),
            'enabled': self.enabled
        }

    async def clear_cache(self):
        """مسح التخزين المؤقت"""
        try:
            self.embeddings_cache.clear()
            logger.info("🧹 تم مسح تخزين RAG المؤقت")
        except Exception as e:
            logger.error(f"❌ خطأ في مسح التخزين المؤقت: {e}")

    async def save_knowledge_graph(self, filepath: str):
        """حفظ الرسم البياني للمعرفة"""
        try:
            import pickle
            with open(filepath, 'wb') as f:
                pickle.dump(self.knowledge_graph, f)
            logger.info(f"💾 تم حفظ الرسم البياني: {filepath}")
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الرسم البياني: {e}")

    async def load_knowledge_graph(self, filepath: str):
        """تحميل الرسم البياني للمعرفة"""
        try:
            import pickle
            with open(filepath, 'rb') as f:
                self.knowledge_graph = pickle.load(f)
            logger.info(f"📂 تم تحميل الرسم البياني: {filepath}")
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل الرسم البياني: {e}")

# إنشاء مثيل عام
advanced_rag_system = AdvancedRAGSystem()
