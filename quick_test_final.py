#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.smart_image_manager import smart_image_manager

async def quick_test():
    article = {
        'title': 'Mortal Kombat 1 يحصل على تحديث جديد',
        'content': 'لعبة Mortal Kombat 1 الشهيرة تحصل على تحديث كبير يتضمن شخصيات جديدة.',
        'keywords': ['mortal kombat', 'fighting', 'update']
    }
    
    print("🧪 اختبار سريع للنظام المحسن...")
    result = await smart_image_manager.generate_smart_image_for_article(article)
    
    if result:
        print('✅ اختبار سريع نجح - تم إنشاء صورة')
        print(f'المصدر: {result.get("source", "غير محدد")}')
        print(f'الطريقة: {result.get("generation_method", "غير محدد")}')
        
        # عرض الإحصائيات
        stats = smart_image_manager.get_daily_stats()
        print(f'📊 صور منشأة: {stats["images_generated"]}')
        print(f'📦 معدل التخزين المؤقت: {stats["cache_hit_rate"]:.1f}%')
    else:
        print('❌ اختبار سريع فشل')

if __name__ == "__main__":
    asyncio.run(quick_test())
