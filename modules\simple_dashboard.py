#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة مراقبة بسيطة للوكيل
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List

class SimpleDashboard:
    """لوحة مراقبة بسيطة"""
    
    def __init__(self):
        self.db_path = "data/articles.db"
    
    def get_system_status(self) -> Dict:
        """الحصول على حالة النظام"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # إحصائيات عامة
                cursor.execute("SELECT COUNT(*) FROM published_articles")
                total_articles = cursor.fetchone()[0]
                
                cursor.execute("""
                    SELECT COUNT(*) FROM published_articles 
                    WHERE published_at >= datetime('now', '-24 hours')
                """)
                articles_today = cursor.fetchone()[0]
                
                cursor.execute("""
                    SELECT AVG(engagement_score) FROM published_articles 
                    WHERE published_at >= datetime('now', '-7 days')
                """)
                avg_engagement = cursor.fetchone()[0] or 0
                
                return {
                    'total_articles': total_articles,
                    'articles_today': articles_today,
                    'avg_engagement_week': round(avg_engagement, 2),
                    'status': 'healthy' if articles_today > 0 else 'warning',
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                'error': str(e),
                'status': 'error',
                'last_updated': datetime.now().isoformat()
            }
    
    def get_performance_metrics(self) -> Dict:
        """الحصول على مقاييس الأداء"""
        try:
            from modules.performance_optimizer import performance_optimizer
            return performance_optimizer.get_stats()
        except:
            return {'error': 'Performance optimizer not available'}
    
    def generate_simple_report(self) -> str:
        """إنشاء تقرير بسيط"""
        status = self.get_system_status()
        performance = self.get_performance_metrics()
        
        report = f"""
🤖 تقرير حالة الوكيل - {datetime.now().strftime('%Y-%m-%d %H:%M')}
{'='*50}

📊 الإحصائيات العامة:
   📰 إجمالي المقالات: {status.get('total_articles', 0)}
   📅 مقالات اليوم: {status.get('articles_today', 0)}
   📈 متوسط التفاعل (أسبوع): {status.get('avg_engagement_week', 0)}
   🟢 الحالة: {status.get('status', 'unknown')}

⚡ الأداء:
   🎯 معدل إصابة الكاش: {performance.get('cache_hit_rate', 0)}%
   💾 حجم الكاش: {performance.get('cache_size', 0)}
   🔍 استعلامات قاعدة البيانات: {performance.get('db_queries', 0)}
   🌐 استدعاءات API: {performance.get('api_calls', 0)}

{'='*50}
        """
        
        return report

# إنشاء مثيل عام للوحة المراقبة
simple_dashboard = SimpleDashboard()
