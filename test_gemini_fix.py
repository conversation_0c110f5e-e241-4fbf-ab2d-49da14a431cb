#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات Gemini 2.5 Pro
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.fallback_ai_manager import fallback_ai_manager, SearchRequest

async def test_gemini_fixes():
    """اختبار إصلاحات Gemini 2.5 Pro"""
    
    logger.info("🧪 بدء اختبار إصلاحات Gemini 2.5 Pro...")
    
    # إنشاء طلب بحث تجريبي
    test_request = SearchRequest(
        query="gaming news today",
        max_results=3,
        search_depth="standard",
        include_web_search=True
    )
    
    try:
        # اختبار البحث مع النماذج الاحتياطية
        logger.info("🔍 اختبار البحث مع النماذج الاحتياطية...")
        
        result = await fallback_ai_manager.search_with_fallback(test_request)
        
        if result:
            logger.info("✅ نجح الاختبار!")
            logger.info(f"📊 المصدر: {result.get('source', 'غير محدد')}")
            logger.info(f"📝 نوع النموذج: {result.get('model_type', 'غير محدد')}")
            logger.info(f"📄 طول المحتوى: {len(result.get('content', ''))}")
            logger.info(f"🌐 يدعم البحث على الويب: {result.get('supports_web_search', False)}")
            
            # عرض جزء من المحتوى
            content = result.get('content', '')
            if content:
                preview = content[:200] + "..." if len(content) > 200 else content
                logger.info(f"📖 معاينة المحتوى: {preview}")
            else:
                logger.warning("⚠️ المحتوى فارغ")
        else:
            logger.error("❌ فشل الاختبار - لم يتم الحصول على نتيجة")
            
    except Exception as e:
        logger.error(f"❌ خطأ في الاختبار: {e}")
        import traceback
        logger.error(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")

async def test_specific_gemini_models():
    """اختبار نماذج Gemini المحددة"""
    
    logger.info("🧪 اختبار نماذج Gemini المحددة...")
    
    test_request = SearchRequest(
        query="latest video game news",
        max_results=2,
        search_depth="basic"
    )
    
    # الحصول على النماذج المتاحة
    available_models = fallback_ai_manager._get_available_models_by_priority()
    
    logger.info(f"📋 النماذج المتاحة: {len(available_models)}")
    
    for model_config in available_models:
        if 'gemini' in model_config.value.lower():
            logger.info(f"🧪 اختبار {model_config.value}...")

            try:
                # الحصول على تكوين النموذج
                config = fallback_ai_manager.models[model_config]

                # اختبار النموذج المحدد
                if model_config.value == 'gemini_2_5_pro':
                    result = await fallback_ai_manager._search_with_gemini_2_5_pro(config, test_request)
                elif model_config.value == 'gemini_2_5_pro_backup':
                    result = await fallback_ai_manager._search_with_gemini_2_5_pro_backup(config, test_request)
                else:
                    continue

                if result:
                    logger.info(f"✅ {model_config.value} نجح")
                    logger.info(f"📄 طول المحتوى: {len(result.get('content', ''))}")
                else:
                    logger.warning(f"⚠️ {model_config.value} لم يعطِ نتيجة")

            except Exception as e:
                logger.error(f"❌ خطأ في {model_config.value}: {e}")

async def test_response_parsing():
    """اختبار تحليل الاستجابات المختلفة"""
    
    logger.info("🧪 اختبار تحليل الاستجابات...")
    
    # اختبار بيانات مختلفة
    test_responses = [
        # استجابة عادية
        {
            "candidates": [
                {
                    "content": {
                        "parts": [
                            {"text": "هذا محتوى تجريبي للاختبار"}
                        ]
                    }
                }
            ]
        },
        # استجابة بنص مباشر
        {
            "candidates": [
                {
                    "text": "محتوى تجريبي مباشر"
                }
            ]
        },
        # استجابة فارغة
        {
            "candidates": []
        },
        # استجابة بخطأ
        {
            "error": {
                "message": "خطأ تجريبي"
            }
        }
    ]
    
    for i, test_data in enumerate(test_responses):
        logger.info(f"🧪 اختبار الاستجابة {i+1}...")
        
        is_valid = fallback_ai_manager._validate_response_data(test_data, f"Test Model {i+1}")
        
        if is_valid:
            logger.info(f"✅ الاستجابة {i+1} صحيحة")
        else:
            logger.info(f"❌ الاستجابة {i+1} غير صحيحة (متوقع)")

async def main():
    """الدالة الرئيسية للاختبار"""
    
    logger.info("🚀 بدء اختبارات إصلاحات Gemini 2.5 Pro...")
    
    try:
        # اختبار 1: البحث العام
        await test_gemini_fixes()
        
        print("\n" + "="*50 + "\n")
        
        # اختبار 2: النماذج المحددة
        await test_specific_gemini_models()
        
        print("\n" + "="*50 + "\n")
        
        # اختبار 3: تحليل الاستجابات
        await test_response_parsing()
        
        logger.info("✅ اكتملت جميع الاختبارات")
        
    except Exception as e:
        logger.error(f"❌ خطأ في الاختبارات: {e}")
        import traceback
        logger.error(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(main())
