#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل خادم Whisper محلي باستخدام النموذج المرفوع
Start Local Whisper Server using Uploaded Model
"""

import sys
import os
import threading
import time
import subprocess
from pathlib import Path

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger


def check_whisper_model():
    """فحص توفر نموذج Whisper"""
    model_path = Path("huggingface_upload/models/small.pt")
    app_path = Path("huggingface_upload/app.py")
    
    logger.info("🔍 فحص ملفات نموذج Whisper...")
    
    if model_path.exists():
        size_mb = model_path.stat().st_size / (1024 * 1024)
        logger.info(f"✅ نموذج Whisper متوفر: {size_mb:.1f}MB")
    else:
        logger.warning("⚠️ نموذج Whisper غير متوفر")
    
    if app_path.exists():
        logger.info("✅ تطبيق Whisper متوفر")
    else:
        logger.error("❌ تطبيق Whisper غير متوفر")
        return False
    
    return model_path.exists() and app_path.exists()


def start_whisper_server():
    """تشغيل خادم Whisper"""
    try:
        logger.info("🚀 بدء تشغيل خادم Whisper المحلي...")
        
        # الانتقال إلى مجلد huggingface_upload
        os.chdir("huggingface_upload")
        
        # تشغيل التطبيق
        cmd = [sys.executable, "app.py"]
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        logger.info("✅ تم تشغيل خادم Whisper")
        logger.info("🌐 الخادم متاح على: http://localhost:7860")
        
        # مراقبة الخادم
        while True:
            output = process.stdout.readline()
            if output:
                print(f"[Whisper Server] {output.strip()}")
            
            if process.poll() is not None:
                break
                
        # في حالة توقف الخادم
        stderr = process.stderr.read()
        if stderr:
            logger.error(f"❌ خطأ في خادم Whisper: {stderr}")
        
        return_code = process.returncode
        if return_code != 0:
            logger.error(f"❌ توقف خادم Whisper بخطأ: {return_code}")
        else:
            logger.info("✅ توقف خادم Whisper بنجاح")
            
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل خادم Whisper: {e}")
    finally:
        # العودة إلى المجلد الأصلي
        os.chdir("..")


def test_whisper_server():
    """اختبار خادم Whisper"""
    import requests
    import time
    
    logger.info("🧪 اختبار خادم Whisper...")
    
    # انتظار بدء الخادم
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            response = requests.get("http://localhost:7860/health", timeout=5)
            if response.status_code == 200:
                logger.info("✅ خادم Whisper يعمل بنجاح")
                result = response.json()
                logger.info(f"📊 حالة الخادم: {result}")
                return True
        except Exception:
            pass
        
        time.sleep(2)
        logger.info(f"⏳ انتظار بدء الخادم... ({attempt + 1}/{max_attempts})")
    
    logger.error("❌ فشل في الاتصال بخادم Whisper")
    return False


def update_config_for_local_server():
    """تحديث الإعدادات لاستخدام الخادم المحلي"""
    try:
        logger.info("⚙️ تحديث إعدادات النظام...")
        
        # قراءة ملف الإعدادات
        config_path = "config/settings.py"
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحديث رابط API
        old_url = 'WHISPER_API_URL = os.getenv("WHISPER_API_URL", "https://nanami34-ai55.hf.space/api/transcribe")'
        new_url = 'WHISPER_API_URL = os.getenv("WHISPER_API_URL", "http://localhost:7860/api/transcribe")'
        
        if old_url in content:
            content = content.replace(old_url, new_url)
            
            # حفظ الملف المحدث
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("✅ تم تحديث إعدادات API بنجاح")
            logger.info("🔗 رابط API الجديد: http://localhost:7860/api/transcribe")
            return True
        else:
            logger.warning("⚠️ لم يتم العثور على الإعداد القديم")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في تحديث الإعدادات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    logger.info("🎤 إعداد خادم Whisper المحلي...")
    logger.info("=" * 50)
    
    try:
        # فحص ملفات النموذج
        if not check_whisper_model():
            logger.error("❌ ملفات النموذج غير متوفرة")
            return False
        
        # تحديث الإعدادات
        if not update_config_for_local_server():
            logger.warning("⚠️ فشل في تحديث الإعدادات")
        
        # تشغيل خادم Whisper في thread منفصل
        logger.info("🚀 تشغيل خادم Whisper...")
        server_thread = threading.Thread(target=start_whisper_server, daemon=True)
        server_thread.start()
        
        # انتظار قليل ثم اختبار الخادم
        time.sleep(5)
        
        if test_whisper_server():
            logger.info("🎉 خادم Whisper جاهز للاستخدام!")
            logger.info("🌐 يمكنك الآن استخدام النظام المحسن")
            
            # إبقاء الخادم يعمل
            logger.info("⏳ الخادم يعمل... اضغط Ctrl+C للإيقاف")
            try:
                while True:
                    time.sleep(60)
                    # فحص دوري لحالة الخادم
                    if not server_thread.is_alive():
                        logger.warning("⚠️ توقف خادم Whisper")
                        break
            except KeyboardInterrupt:
                logger.info("🛑 تم إيقاف الخادم بواسطة المستخدم")
            
            return True
        else:
            logger.error("❌ فشل في تشغيل خادم Whisper")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
