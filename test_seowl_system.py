#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام SEOwl
Comprehensive SEOwl System Test
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime, timedelta

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger


class SEOwlSystemTester:
    """فئة اختبار نظام SEOwl الشامل"""
    
    def __init__(self):
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': [],
            'start_time': datetime.now().isoformat(),
            'end_time': None
        }
        
    def run_test(self, test_name: str, test_func):
        """تشغيل اختبار واحد"""
        try:
            self.test_results['total_tests'] += 1
            
            print(f"🧪 اختبار: {test_name}")
            
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            duration = end_time - start_time
            
            if result:
                print(f"✅ نجح الاختبار: {test_name} ({duration:.2f}s)")
                self.test_results['passed_tests'] += 1
                status = 'PASSED'
            else:
                print(f"❌ فشل الاختبار: {test_name} ({duration:.2f}s)")
                self.test_results['failed_tests'] += 1
                status = 'FAILED'
            
            self.test_results['test_details'].append({
                'name': test_name,
                'status': status,
                'duration': duration,
                'timestamp': datetime.now().isoformat()
            })
            
            return result
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار {test_name}: {e}")
            self.test_results['failed_tests'] += 1
            self.test_results['test_details'].append({
                'name': test_name,
                'status': 'ERROR',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False
    
    async def run_async_test(self, test_name: str, test_func):
        """تشغيل اختبار غير متزامن"""
        try:
            self.test_results['total_tests'] += 1
            
            print(f"🧪 اختبار غير متزامن: {test_name}")
            
            start_time = time.time()
            result = await test_func()
            end_time = time.time()
            
            duration = end_time - start_time
            
            if result:
                print(f"✅ نجح الاختبار: {test_name} ({duration:.2f}s)")
                self.test_results['passed_tests'] += 1
                status = 'PASSED'
            else:
                print(f"❌ فشل الاختبار: {test_name} ({duration:.2f}s)")
                self.test_results['failed_tests'] += 1
                status = 'FAILED'
            
            self.test_results['test_details'].append({
                'name': test_name,
                'status': status,
                'duration': duration,
                'timestamp': datetime.now().isoformat()
            })
            
            return result
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار {test_name}: {e}")
            self.test_results['failed_tests'] += 1
            self.test_results['test_details'].append({
                'name': test_name,
                'status': 'ERROR',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False
    
    def test_module_imports(self):
        """اختبار استيراد الوحدات"""
        try:
            from modules.seowl_indexing_checker import SEOwlIndexingChecker, seowl_checker
            from modules.seowl_integration import SEOwlIntegration, seowl_integration
            from seowl_web_interface import app
            from modules.database import db
            return True
        except Exception as e:
            print(f"خطأ في الاستيراد: {e}")
            return False
    
    def test_seowl_checker_creation(self):
        """اختبار إنشاء فاحص SEOwl"""
        try:
            from modules.seowl_indexing_checker import seowl_checker
            
            # فحص الإعدادات الأساسية
            api_key = seowl_checker.api_key
            if not api_key or len(api_key) < 10:
                return False
            
            # فحص الإحصائيات
            stats = seowl_checker.get_stats()
            required_keys = ['total_checks', 'successful_checks', 'failed_checks', 'issues_found', 'issues_fixed']
            
            return all(key in stats for key in required_keys)
            
        except Exception as e:
            print(f"خطأ في إنشاء فاحص SEOwl: {e}")
            return False
    
    def test_integration_creation(self):
        """اختبار إنشاء التكامل"""
        try:
            from modules.seowl_integration import seowl_integration
            
            # فحص الإعدادات الأساسية
            cooldown_hours = seowl_integration.publish_cooldown_hours
            if cooldown_hours != 3:
                return False
            
            # فحص الحالة
            status = seowl_integration.get_integration_status()
            required_keys = ['integration_active', 'can_publish_now', 'cooldown_hours']
            
            return all(key in status for key in required_keys)
            
        except Exception as e:
            print(f"خطأ في إنشاء التكامل: {e}")
            return False
    
    async def test_seowl_api_connection(self):
        """اختبار الاتصال بـ SEOwl API"""
        try:
            from modules.seowl_indexing_checker import seowl_checker
            
            # اختبار فحص وهمي
            test_url = "https://example.com"
            
            # محاولة فحص الفهرسة (قد يفشل بسبب الرابط الوهمي، لكن يجب أن يتصل بـ API)
            result = await seowl_checker._check_indexing_status(test_url)
            
            # إذا حصلنا على استجابة (حتى لو كانت خطأ)، فالاتصال يعمل
            return 'indexed' in result or 'error' in result
            
        except Exception as e:
            print(f"خطأ في اختبار SEOwl API: {e}")
            return False
    
    async def test_article_scheduling(self):
        """اختبار جدولة فحص المقالات"""
        try:
            from modules.seowl_indexing_checker import seowl_checker
            
            # إنشاء مقال وهمي للاختبار
            test_article = {
                'id': 'test_article_123',
                'title': 'مقال اختبار',
                'content': 'محتوى اختبار للمقال'
            }
            
            test_url = "https://example.com/test-article"
            
            # جدولة الفحص
            result = await seowl_checker.schedule_article_check(test_article, test_url)
            
            if not result:
                return False
            
            # فحص وجود المقال في قائمة الانتظار
            pending_checks = seowl_checker.get_pending_checks()
            
            return any(check['url'] == test_url for check in pending_checks)
            
        except Exception as e:
            print(f"خطأ في اختبار جدولة المقالات: {e}")
            return False
    
    def test_cooldown_system(self):
        """اختبار نظام فترة الانتظار"""
        try:
            from modules.seowl_integration import seowl_integration
            
            # فحص إمكانية النشر قبل تسجيل وقت النشر
            can_publish_before = seowl_integration.can_publish_now()
            
            # تسجيل وقت نشر وهمي
            seowl_integration.last_publish_time = datetime.now()
            
            # فحص إمكانية النشر بعد تسجيل وقت النشر
            can_publish_after = seowl_integration.can_publish_now()
            
            # يجب أن يكون النشر ممكناً قبل التسجيل وغير ممكن بعده
            if not can_publish_before or can_publish_after:
                return False
            
            # فحص الوقت المتبقي
            time_until_next = seowl_integration.get_time_until_next_publish()
            
            return time_until_next is not None and time_until_next > timedelta(hours=2)
            
        except Exception as e:
            print(f"خطأ في اختبار نظام فترة الانتظار: {e}")
            return False
    
    def test_database_operations(self):
        """اختبار عمليات قاعدة البيانات"""
        try:
            from modules.database import db
            
            # اختبار حفظ إحصائيات SEOwl
            test_stats = {
                'total_checks': 10,
                'successful_checks': 8,
                'failed_checks': 2,
                'issues_found': 5,
                'issues_fixed': 4,
                'last_check': datetime.now().isoformat()
            }
            
            db.save_seowl_stats(test_stats)
            
            # اختبار حفظ تحسين SEO
            test_improvement = {
                'url': 'https://example.com/test',
                'improvement_type': 'title',
                'description': 'تحسين العنوان',
                'status': 'pending',
                'created_at': datetime.now().isoformat()
            }
            
            db.save_seo_improvement(test_improvement)
            
            # اختبار الاستعلام
            seo_improvements = db.get_pending_seo_improvements(10)
            
            return len(seo_improvements) >= 0  # يجب أن يعيد قائمة (حتى لو فارغة)
            
        except Exception as e:
            print(f"خطأ في اختبار قاعدة البيانات: {e}")
            return False
    
    def test_web_interface(self):
        """اختبار واجهة الويب"""
        try:
            from seowl_web_interface import app
            
            with app.test_client() as client:
                # اختبار الصفحة الرئيسية
                response = client.get('/')
                if response.status_code != 200:
                    return False
                
                # اختبار API الحالة
                response = client.get('/api/seowl-status')
                if response.status_code != 200:
                    return False
                
                data = response.get_json()
                if not data or not data.get('success'):
                    return False
                
                # اختبار API فحص إمكانية النشر
                response = client.get('/api/can-publish')
                if response.status_code != 200:
                    return False
                
                return True
                
        except Exception as e:
            print(f"خطأ في اختبار واجهة الويب: {e}")
            return False
    
    async def test_issue_detection_and_fixing(self):
        """اختبار كشف وإصلاح المشاكل"""
        try:
            from modules.seowl_indexing_checker import seowl_checker
            
            # إنشاء مشاكل وهمية للاختبار
            test_issues = [
                {
                    'type': 'not_indexed',
                    'severity': 'high',
                    'description': 'الصفحة غير مفهرسة',
                    'fix_action': 'submit_to_google'
                },
                {
                    'type': 'seo_issue',
                    'severity': 'medium',
                    'description': 'مشكلة في العنوان',
                    'fix_action': 'fix_title'
                }
            ]
            
            test_url = "https://example.com/test"
            
            # اختبار إصلاح المشاكل
            fixed_count = await seowl_checker._fix_article_issues(test_url, test_issues)
            
            # يجب أن يحاول إصلاح المشاكل (حتى لو فشل بسبب الرابط الوهمي)
            return fixed_count >= 0
            
        except Exception as e:
            print(f"خطأ في اختبار كشف وإصلاح المشاكل: {e}")
            return False
    
    def generate_report(self):
        """توليد تقرير شامل للاختبارات"""
        self.test_results['end_time'] = datetime.now().isoformat()
        
        success_rate = (self.test_results['passed_tests'] / self.test_results['total_tests']) * 100 if self.test_results['total_tests'] > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 تقرير اختبارات نظام SEOwl")
        print("=" * 60)
        print(f"📈 إجمالي الاختبارات: {self.test_results['total_tests']}")
        print(f"✅ اختبارات ناجحة: {self.test_results['passed_tests']}")
        print(f"❌ اختبارات فاشلة: {self.test_results['failed_tests']}")
        print(f"📊 معدل النجاح: {success_rate:.1f}%")
        print()
        
        if success_rate >= 90:
            print("🎉 ممتاز! نظام SEOwl يعمل بشكل مثالي")
        elif success_rate >= 70:
            print("👍 جيد! نظام SEOwl يعمل بشكل جيد مع بعض المشاكل البسيطة")
        else:
            print("⚠️ يحتاج تحسين! هناك مشاكل تحتاج إلى إصلاح")
        
        print("\n📋 تفاصيل الاختبارات:")
        for test in self.test_results['test_details']:
            status_icon = "✅" if test['status'] == 'PASSED' else "❌"
            print(f"  {status_icon} {test['name']}")
            if test['status'] == 'ERROR':
                print(f"    خطأ: {test.get('error', 'غير محدد')}")
        
        # حفظ التقرير في ملف
        report_filename = f"seowl_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ التقرير في: {report_filename}")
        
        return success_rate >= 70


async def main():
    """الدالة الرئيسية للاختبار"""
    try:
        print("🧪 بدء الاختبار الشامل لنظام SEOwl")
        print("=" * 60)
        print()
        
        tester = SEOwlSystemTester()
        
        # الاختبارات المتزامنة
        print("📦 اختبارات الوحدات والإعداد:")
        tester.run_test("استيراد الوحدات", tester.test_module_imports)
        tester.run_test("إنشاء فاحص SEOwl", tester.test_seowl_checker_creation)
        tester.run_test("إنشاء التكامل", tester.test_integration_creation)
        tester.run_test("نظام فترة الانتظار", tester.test_cooldown_system)
        tester.run_test("عمليات قاعدة البيانات", tester.test_database_operations)
        tester.run_test("واجهة الويب", tester.test_web_interface)
        
        print("\n🔄 الاختبارات غير المتزامنة:")
        await tester.run_async_test("الاتصال بـ SEOwl API", tester.test_seowl_api_connection)
        await tester.run_async_test("جدولة فحص المقالات", tester.test_article_scheduling)
        await tester.run_async_test("كشف وإصلاح المشاكل", tester.test_issue_detection_and_fixing)
        
        # توليد التقرير النهائي
        success = tester.generate_report()
        
        if success:
            print("\n🚀 نظام SEOwl جاهز للاستخدام!")
            print("يمكنك تشغيله باستخدام: START_SEOWL_SYSTEM.bat")
            print("🌐 واجهة الإدارة: http://localhost:5003")
        else:
            print("\n⚠️ يرجى مراجعة الأخطاء وإصلاحها قبل الاستخدام")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ خطأ في الاختبار الشامل: {e}")
        print(f"❌ خطأ عام في الاختبار: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\n✅ تم إكمال جميع الاختبارات بنجاح!")
    else:
        print("\n❌ فشل في بعض الاختبارات")
        sys.exit(1)
