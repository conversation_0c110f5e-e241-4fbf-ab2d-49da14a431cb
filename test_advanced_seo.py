#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام SEO المتقدم مع Keyword Tool API
"""

import asyncio
import sys
import os
import sqlite3
from datetime import datetime, timedelta

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.advanced_seo_system import advanced_seo_system, KeywordToolAPI, InternalLinkingOptimizer
from modules.logger import logger

def create_test_articles_for_seo():
    """إنشاء مقالات تجريبية لاختبار SEO"""
    try:
        with sqlite3.connect("data/articles.db") as conn:
            cursor = conn.cursor()
            
            # إنشاء مقالات متنوعة لاختبار SEO
            seo_test_articles = [
                {
                    'title': 'مراجعة شاملة للعبة Cyberpunk 2077 - دليل كامل 2025',
                    'content': '''
                    تعتبر لعبة Cyberpunk 2077 واحدة من أهم الألعاب في عالم الألعاب الحديث. 
                    
                    ## مميزات اللعبة
                    تحتوي اللعبة على العديد من المميزات الرائعة:
                    • رسومات مذهلة وتفاصيل دقيقة
                    • قصة مثيرة ومعقدة
                    • عالم مفتوح واسع للاستكشاف
                    
                    ## طريقة اللعب
                    تقدم Cyberpunk 2077 تجربة لعب فريدة تجمع بين عناصر RPG والأكشن.
                    
                    ## الخلاصة
                    في النهاية، تستحق هذه اللعبة التجربة لكل محبي الألعاب.
                    ''',
                    'keywords': 'cyberpunk 2077, مراجعة, لعبة, RPG, أكشن, ألعاب',
                    'category': 'مراجعات',
                    'published_at': (datetime.now() - timedelta(days=2)).isoformat(),
                    'view_count': 250,
                    'engagement_score': 85.0
                },
                {
                    'title': 'أفضل 10 ألعاب لعام 2025',
                    'content': '''
                    إليكم قائمة بأفضل الألعاب المتوقعة في عام 2025.
                    
                    1. The Elder Scrolls VI
                    2. Grand Theft Auto VI
                    3. Cyberpunk 2078
                    4. Call of Duty: Future Warfare
                    5. FIFA 2025
                    
                    هذه الألعاب ستغير مستقبل صناعة الألعاب.
                    ''',
                    'keywords': 'أفضل ألعاب, 2025, قائمة, ألعاب جديدة',
                    'category': 'قوائم',
                    'published_at': (datetime.now() - timedelta(days=1)).isoformat(),
                    'view_count': 180,
                    'engagement_score': 75.0
                },
                {
                    'title': 'دليل المبتدئين للعبة Minecraft',
                    'content': '''
                    Minecraft هي لعبة بناء وإبداع رائعة.
                    
                    ## البداية
                    أولاً، تحتاج لفهم أساسيات اللعبة.
                    
                    ## النصائح
                    • ابدأ بجمع الخشب
                    • ابني مأوى آمن
                    • اجمع الموارد
                    
                    ## الخلاصة
                    مع الممارسة ستصبح خبيراً في Minecraft.
                    ''',
                    'keywords': 'minecraft, دليل, مبتدئين, نصائح, بناء',
                    'category': 'أدلة',
                    'published_at': (datetime.now() - timedelta(days=3)).isoformat(),
                    'view_count': 320,
                    'engagement_score': 90.0
                },
                {
                    'title': 'خبر عاجل: إعلان لعبة جديدة',
                    'content': 'تم إعلان لعبة جديدة اليوم. اللعبة ستكون رائعة.',
                    'keywords': 'خبر, إعلان, لعبة جديدة',
                    'category': 'أخبار',
                    'published_at': (datetime.now() - timedelta(hours=6)).isoformat(),
                    'view_count': 45,
                    'engagement_score': 40.0
                }
            ]
            
            for article in seo_test_articles:
                cursor.execute('''
                    INSERT OR IGNORE INTO published_articles 
                    (title, content, keywords, category, published_at, view_count, engagement_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    article['title'],
                    article['content'],
                    article['keywords'],
                    article['category'],
                    article['published_at'],
                    article['view_count'],
                    article['engagement_score']
                ))
            
            conn.commit()
            print("✅ تم إنشاء المقالات التجريبية لاختبار SEO")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المقالات التجريبية: {e}")

async def test_keyword_tool_api():
    """اختبار Keyword Tool API"""
    print("🔑 اختبار Keyword Tool API...")
    
    try:
        keyword_tool = KeywordToolAPI()
        
        # اختبار الحصول على كلمات مفتاحية رائجة
        test_keywords = ['minecraft', 'cyberpunk', 'gaming']
        
        for keyword in test_keywords:
            trending = await keyword_tool.get_trending_keywords(keyword)
            
            if trending:
                print(f"✅ كلمات رائجة لـ '{keyword}':")
                for i, kw in enumerate(trending[:5], 1):
                    print(f"   {i}. {kw['keyword']} (حجم: {kw['search_volume']}, منافسة: {kw['competition']})")
            else:
                print(f"⚠️ لم يتم العثور على كلمات رائجة لـ '{keyword}'")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Keyword Tool API: {e}")
        return False

async def test_internal_linking():
    """اختبار نظام الربط الداخلي"""
    print("\n🔗 اختبار نظام الربط الداخلي...")
    
    try:
        link_optimizer = InternalLinkingOptimizer()
        
        # الحصول على مقال للاختبار
        with sqlite3.connect("data/articles.db") as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id, title FROM published_articles LIMIT 1')
            result = cursor.fetchone()
            
            if not result:
                print("❌ لا توجد مقالات للاختبار")
                return False
            
            article_id, title = result
        
        # تحليل الروابط الداخلية
        linking_analysis = await link_optimizer.analyze_and_optimize_internal_links(article_id)
        
        if linking_analysis:
            print(f"✅ تحليل الروابط للمقال: {title[:40]}...")
            
            current_links = linking_analysis.get('current_links', {})
            print(f"   📊 الروابط الصادرة: {current_links.get('outgoing_internal_links', 0)}")
            print(f"   📈 الروابط الواردة: {current_links.get('incoming_internal_links', 0)}")
            print(f"   📏 كثافة الروابط: {current_links.get('link_density', 0):.2f}%")
            
            suggestions = linking_analysis.get('link_suggestions', [])
            if suggestions:
                print(f"   💡 اقتراحات الربط ({len(suggestions)}):")
                for i, suggestion in enumerate(suggestions[:3], 1):
                    print(f"      {i}. {suggestion['target_title'][:30]}... (صلة: {suggestion['relevance_score']:.2f})")
            
            optimization_score = linking_analysis.get('optimization_score', 0)
            print(f"   🎯 نقاط التحسين: {optimization_score:.1f}/100")
            
            return True
        else:
            print("❌ فشل في تحليل الروابط الداخلية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الربط الداخلي: {e}")
        return False

async def test_comprehensive_seo():
    """اختبار تحليل SEO الشامل"""
    print("\n📊 اختبار تحليل SEO الشامل...")
    
    try:
        # الحصول على مقال للاختبار
        with sqlite3.connect("data/articles.db") as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id, title FROM published_articles ORDER BY id DESC LIMIT 1')
            result = cursor.fetchone()
            
            if not result:
                print("❌ لا توجد مقالات للاختبار")
                return False
            
            article_id, title = result
        
        # تشغيل تحليل SEO الشامل
        seo_analysis = await advanced_seo_system.comprehensive_seo_analysis(article_id)
        
        if seo_analysis:
            print(f"✅ تحليل SEO شامل للمقال: {title[:40]}...")
            
            # عرض نتائج التحليل
            keyword_analysis = seo_analysis.get('keyword_analysis', {})
            if keyword_analysis:
                print(f"   🔑 نقاط الكلمات المفتاحية: {keyword_analysis.get('optimization_score', 0):.1f}/100")
                current_kw = keyword_analysis.get('current_keywords', [])
                print(f"   📝 الكلمات الحالية: {len(current_kw)} كلمة")
                
                suggested_kw = keyword_analysis.get('suggested_keywords', [])
                if suggested_kw:
                    print(f"   💡 اقتراحات جديدة: {len(suggested_kw)} كلمة")
                    for kw in suggested_kw[:3]:
                        print(f"      • {kw['keyword']} (حجم: {kw['search_volume']})")
            
            content_analysis = seo_analysis.get('content_analysis', {})
            if content_analysis:
                print(f"   📄 نقاط المحتوى: {content_analysis.get('optimization_score', 0):.1f}/100")
                
                title_analysis = content_analysis.get('title_analysis', {})
                if title_analysis:
                    print(f"      📝 طول العنوان: {title_analysis.get('length', 0)} حرف")
                    print(f"      ✅ طول مثالي: {title_analysis.get('optimal_length', False)}")
            
            linking_analysis = seo_analysis.get('linking_analysis', {})
            if linking_analysis:
                print(f"   🔗 نقاط الربط الداخلي: {linking_analysis.get('optimization_score', 0):.1f}/100")
            
            technical_analysis = seo_analysis.get('technical_analysis', {})
            if technical_analysis:
                print(f"   ⚙️ نقاط SEO التقني: {technical_analysis.get('optimization_score', 0):.1f}/100")
            
            overall_score = seo_analysis.get('overall_score', 0)
            print(f"   🎯 النقاط الإجمالية: {overall_score:.1f}/100")
            
            recommendations = seo_analysis.get('recommendations', [])
            if recommendations:
                print(f"   📋 التوصيات ({len(recommendations)}):")
                for i, rec in enumerate(recommendations, 1):
                    print(f"      {i}. {rec}")
            
            return True
        else:
            print("❌ فشل في تحليل SEO الشامل")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار SEO الشامل: {e}")
        return False

async def test_multiple_articles_seo():
    """اختبار تحليل SEO لعدة مقالات"""
    print("\n📈 اختبار تحليل SEO لعدة مقالات...")
    
    try:
        # الحصول على عدة مقالات
        with sqlite3.connect("data/articles.db") as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id, title FROM published_articles ORDER BY id DESC LIMIT 3')
            articles = cursor.fetchall()
        
        if not articles:
            print("❌ لا توجد مقالات للاختبار")
            return False
        
        seo_scores = []
        
        for article_id, title in articles:
            try:
                seo_analysis = await advanced_seo_system.comprehensive_seo_analysis(article_id)
                
                if seo_analysis:
                    overall_score = seo_analysis.get('overall_score', 0)
                    seo_scores.append(overall_score)
                    print(f"   📊 {title[:30]}... → {overall_score:.1f}/100")
                
            except Exception as e:
                print(f"   ❌ فشل في تحليل المقال {article_id}: {e}")
                continue
        
        if seo_scores:
            avg_score = sum(seo_scores) / len(seo_scores)
            print(f"✅ متوسط نقاط SEO: {avg_score:.1f}/100")
            print(f"📊 أعلى نقاط: {max(seo_scores):.1f}")
            print(f"📉 أقل نقاط: {min(seo_scores):.1f}")
            
            return True
        else:
            print("❌ لم يتم تحليل أي مقال بنجاح")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عدة مقالات: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبارات نظام SEO المتقدم")
    print("=" * 60)
    
    # إنشاء مقالات تجريبية
    create_test_articles_for_seo()
    
    # اختبار Keyword Tool API
    keyword_success = await test_keyword_tool_api()
    
    # اختبار الربط الداخلي
    linking_success = await test_internal_linking()
    
    # اختبار SEO الشامل
    comprehensive_success = await test_comprehensive_seo()
    
    # اختبار عدة مقالات
    multiple_success = await test_multiple_articles_seo()
    
    print("\n" + "=" * 60)
    print("📋 ملخص نتائج الاختبارات:")
    print(f"🔑 Keyword Tool API: {'✅ نجح' if keyword_success else '❌ فشل'}")
    print(f"🔗 الربط الداخلي: {'✅ نجح' if linking_success else '❌ فشل'}")
    print(f"📊 SEO الشامل: {'✅ نجح' if comprehensive_success else '❌ فشل'}")
    print(f"📈 تحليل عدة مقالات: {'✅ نجح' if multiple_success else '❌ فشل'}")
    
    all_tests = [keyword_success, linking_success, comprehensive_success, multiple_success]
    
    if all(all_tests):
        print("\n🎉 جميع الاختبارات نجحت! نظام SEO المتقدم جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")

if __name__ == "__main__":
    asyncio.run(main())
