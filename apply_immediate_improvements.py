#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق التحسينات الفورية على الوكيل
"""

import os
import re
import sys
from datetime import datetime

def clean_unused_imports():
    """تنظيف الاستيرادات غير المستخدمة من main.py"""
    print("🧹 تنظيف الاستيرادات غير المستخدمة...")
    
    try:
        # قراءة main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إزالة الاستيرادات غير المستخدمة
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # إزالة random import
            if 'import random' in line and line.strip().startswith('import random'):
                print("   ❌ إزالة: import random")
                continue
            
            # إزالة advanced_seo import
            if 'from modules.advanced_seo import advanced_seo' in line:
                print("   ❌ إزالة: from modules.advanced_seo import advanced_seo")
                continue
            
            # إزالة performance_monitor import
            if 'from modules.performance_monitor import performance_monitor' in line:
                print("   ❌ إزالة: from modules.performance_monitor import performance_monitor")
                continue
            
            # إزالة retry_mechanism و sync_retry_decorator
            if 'retry_mechanism, scheduler,' in line:
                line = line.replace('retry_mechanism, scheduler,', 'scheduler,')
                print("   ❌ إزالة: retry_mechanism")
            
            if 'async_retry_decorator, sync_retry_decorator' in line:
                line = line.replace(', sync_retry_decorator', '')
                print("   ❌ إزالة: sync_retry_decorator")
            
            cleaned_lines.append(line)
        
        # كتابة الملف المحدث
        cleaned_content = '\n'.join(cleaned_lines)
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print("✅ تم تنظيف الاستيرادات غير المستخدمة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف الاستيرادات: {e}")
        return False

def fix_signal_handler():
    """إصلاح معالج الإشارات"""
    print("🔧 إصلاح معالج الإشارات...")
    
    try:
        # قراءة main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استبدال frame بـ _
        old_pattern = r'def signal_handler\(signum, frame\):'
        new_pattern = 'def signal_handler(signum, _):'
        
        if re.search(old_pattern, content):
            content = re.sub(old_pattern, new_pattern, content)
            
            # كتابة الملف المحدث
            with open('main.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم إصلاح معالج الإشارات")
            return True
        else:
            print("ℹ️ معالج الإشارات لا يحتاج إصلاح")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح معالج الإشارات: {e}")
        return False

def add_performance_improvements():
    """إضافة تحسينات الأداء الأساسية"""
    print("⚡ إضافة تحسينات الأداء الأساسية...")
    
    try:
        # إنشاء ملف تحسينات الأداء
        performance_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحسينات الأداء الأساسية للوكيل
"""

import asyncio
import time
from functools import wraps
from typing import Dict, Any, Optional
import sqlite3
from datetime import datetime, timedelta

class PerformanceOptimizer:
    """محسن الأداء الأساسي"""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = {}
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'db_queries': 0,
            'api_calls': 0
        }
    
    def cache_result(self, key: str, value: Any, ttl: int = 3600):
        """تخزين نتيجة في الذاكرة المؤقتة"""
        self.cache[key] = value
        self.cache_ttl[key] = time.time() + ttl
    
    def get_cached_result(self, key: str) -> Optional[Any]:
        """الحصول على نتيجة من الذاكرة المؤقتة"""
        if key in self.cache:
            if time.time() < self.cache_ttl[key]:
                self.stats['cache_hits'] += 1
                return self.cache[key]
            else:
                # انتهت صلاحية الكاش
                del self.cache[key]
                del self.cache_ttl[key]
        
        self.stats['cache_misses'] += 1
        return None
    
    def clear_expired_cache(self):
        """مسح الكاش المنتهي الصلاحية"""
        current_time = time.time()
        expired_keys = [
            key for key, ttl in self.cache_ttl.items() 
            if current_time >= ttl
        ]
        
        for key in expired_keys:
            del self.cache[key]
            del self.cache_ttl[key]
    
    def get_stats(self) -> Dict:
        """الحصول على إحصائيات الأداء"""
        total_requests = self.stats['cache_hits'] + self.stats['cache_misses']
        hit_rate = (self.stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.stats,
            'cache_hit_rate': round(hit_rate, 2),
            'cache_size': len(self.cache)
        }

def performance_monitor(func):
    """ديكوريتر لمراقبة الأداء"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # تسجيل الأداء
            if execution_time > 5.0:  # أكثر من 5 ثواني
                print(f"⚠️ دالة بطيئة: {func.__name__} استغرقت {execution_time:.2f} ثانية")
            
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"❌ خطأ في {func.__name__} بعد {execution_time:.2f} ثانية: {e}")
            raise
    
    return wrapper

class DatabaseOptimizer:
    """محسن قاعدة البيانات"""
    
    @staticmethod
    def add_missing_indexes():
        """إضافة فهارس مفقودة لتحسين الأداء"""
        try:
            with sqlite3.connect("data/articles.db") as conn:
                cursor = conn.cursor()
                
                # فهارس للجداول الرئيسية (مصحح)
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_published_articles_date ON published_articles(published_at)",
                    "CREATE INDEX IF NOT EXISTS idx_published_articles_category ON published_articles(category)",
                    "CREATE INDEX IF NOT EXISTS idx_published_articles_engagement ON published_articles(engagement_score)",
                    "CREATE INDEX IF NOT EXISTS idx_published_articles_content_hash ON published_articles(content_hash)",
                    "CREATE INDEX IF NOT EXISTS idx_performance_stats_date ON performance_stats(date)",
                ]
                
                for index_sql in indexes:
                    cursor.execute(index_sql)
                
                conn.commit()
                print("✅ تم إضافة فهارس قاعدة البيانات")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في إضافة الفهارس: {e}")
            return False
    
    @staticmethod
    def optimize_database():
        """تحسين قاعدة البيانات"""
        try:
            with sqlite3.connect("data/articles.db") as conn:
                cursor = conn.cursor()
                
                # تحليل وتحسين الجداول
                cursor.execute("ANALYZE")
                cursor.execute("VACUUM")
                
                conn.commit()
                print("✅ تم تحسين قاعدة البيانات")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في تحسين قاعدة البيانات: {e}")
            return False

# إنشاء مثيل عام لمحسن الأداء
performance_optimizer = PerformanceOptimizer()
'''
        
        with open('modules/performance_optimizer.py', 'w', encoding='utf-8') as f:
            f.write(performance_code)
        
        print("✅ تم إنشاء محسن الأداء الأساسي")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة تحسينات الأداء: {e}")
        return False

def create_monitoring_dashboard():
    """إنشاء لوحة مراقبة بسيطة"""
    print("📊 إنشاء لوحة مراقبة بسيطة...")
    
    try:
        dashboard_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة مراقبة بسيطة للوكيل
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List

class SimpleDashboard:
    """لوحة مراقبة بسيطة"""
    
    def __init__(self):
        self.db_path = "data/articles.db"
    
    def get_system_status(self) -> Dict:
        """الحصول على حالة النظام"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # إحصائيات عامة
                cursor.execute("SELECT COUNT(*) FROM published_articles")
                total_articles = cursor.fetchone()[0]
                
                cursor.execute("""
                    SELECT COUNT(*) FROM published_articles 
                    WHERE published_at >= datetime('now', '-24 hours')
                """)
                articles_today = cursor.fetchone()[0]
                
                cursor.execute("""
                    SELECT AVG(engagement_score) FROM published_articles 
                    WHERE published_at >= datetime('now', '-7 days')
                """)
                avg_engagement = cursor.fetchone()[0] or 0
                
                return {
                    'total_articles': total_articles,
                    'articles_today': articles_today,
                    'avg_engagement_week': round(avg_engagement, 2),
                    'status': 'healthy' if articles_today > 0 else 'warning',
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                'error': str(e),
                'status': 'error',
                'last_updated': datetime.now().isoformat()
            }
    
    def get_performance_metrics(self) -> Dict:
        """الحصول على مقاييس الأداء"""
        try:
            from modules.performance_optimizer import performance_optimizer
            return performance_optimizer.get_stats()
        except:
            return {'error': 'Performance optimizer not available'}
    
    def generate_simple_report(self) -> str:
        """إنشاء تقرير بسيط"""
        status = self.get_system_status()
        performance = self.get_performance_metrics()
        
        report = f"""
🤖 تقرير حالة الوكيل - {datetime.now().strftime('%Y-%m-%d %H:%M')}
{'='*50}

📊 الإحصائيات العامة:
   📰 إجمالي المقالات: {status.get('total_articles', 0)}
   📅 مقالات اليوم: {status.get('articles_today', 0)}
   📈 متوسط التفاعل (أسبوع): {status.get('avg_engagement_week', 0)}
   🟢 الحالة: {status.get('status', 'unknown')}

⚡ الأداء:
   🎯 معدل إصابة الكاش: {performance.get('cache_hit_rate', 0)}%
   💾 حجم الكاش: {performance.get('cache_size', 0)}
   🔍 استعلامات قاعدة البيانات: {performance.get('db_queries', 0)}
   🌐 استدعاءات API: {performance.get('api_calls', 0)}

{'='*50}
        """
        
        return report

# إنشاء مثيل عام للوحة المراقبة
simple_dashboard = SimpleDashboard()
'''
        
        with open('modules/simple_dashboard.py', 'w', encoding='utf-8') as f:
            f.write(dashboard_code)
        
        print("✅ تم إنشاء لوحة المراقبة البسيطة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء لوحة المراقبة: {e}")
        return False

def create_backup_system():
    """إنشاء نظام نسخ احتياطية بسيط"""
    print("💾 إنشاء نظام نسخ احتياطية بسيط...")
    
    try:
        backup_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطية البسيط
"""

import os
import shutil
import sqlite3
import json
from datetime import datetime
from typing import Dict, List

class SimpleBackupSystem:
    """نظام نسخ احتياطية بسيط"""
    
    def __init__(self):
        self.backup_dir = "backups"
        self.ensure_backup_dir()
    
    def ensure_backup_dir(self):
        """التأكد من وجود مجلد النسخ الاحتياطية"""
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(f"{self.backup_dir}/daily", exist_ok=True)
        os.makedirs(f"{self.backup_dir}/weekly", exist_ok=True)
    
    def create_database_backup(self) -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"articles_backup_{timestamp}.db"
            backup_path = os.path.join(self.backup_dir, "daily", backup_filename)
            
            # نسخ قاعدة البيانات
            if os.path.exists("data/articles.db"):
                shutil.copy2("data/articles.db", backup_path)
                print(f"✅ تم إنشاء نسخة احتياطية: {backup_filename}")
                
                # حفظ معلومات النسخة الاحتياطية
                self._save_backup_info(backup_filename, "database")
                
                return True
            else:
                print("⚠️ قاعدة البيانات غير موجودة")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def create_config_backup(self) -> bool:
        """إنشاء نسخة احتياطية من ملفات التكوين"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"config_backup_{timestamp}.json"
            backup_path = os.path.join(self.backup_dir, "daily", backup_filename)
            
            config_data = {}
            
            # نسخ ملفات التكوين المهمة
            config_files = [
                "config/settings.py",
                ".env",
                "config/api_config.py"
            ]
            
            for config_file in config_files:
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data[config_file] = f.read()
            
            # حفظ النسخة الاحتياطية
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ تم إنشاء نسخة احتياطية للتكوين: {backup_filename}")
            self._save_backup_info(backup_filename, "config")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في نسخ التكوين: {e}")
            return False
    
    def _save_backup_info(self, filename: str, backup_type: str):
        """حفظ معلومات النسخة الاحتياطية"""
        try:
            info_file = os.path.join(self.backup_dir, "backup_log.json")
            
            backup_info = {
                'filename': filename,
                'type': backup_type,
                'created_at': datetime.now().isoformat(),
                'size': os.path.getsize(os.path.join(self.backup_dir, "daily", filename))
            }
            
            # قراءة السجل الحالي
            backup_log = []
            if os.path.exists(info_file):
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_log = json.load(f)
            
            # إضافة النسخة الجديدة
            backup_log.append(backup_info)
            
            # الاحتفاظ بآخر 50 نسخة فقط
            backup_log = backup_log[-50:]
            
            # حفظ السجل المحدث
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_log, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"⚠️ خطأ في حفظ معلومات النسخة الاحتياطية: {e}")
    
    def cleanup_old_backups(self, days: int = 7):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            for backup_dir in ["daily", "weekly"]:
                backup_path = os.path.join(self.backup_dir, backup_dir)
                
                if os.path.exists(backup_path):
                    for filename in os.listdir(backup_path):
                        file_path = os.path.join(backup_path, filename)
                        
                        # فحص تاريخ الملف
                        file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                        
                        if file_time < cutoff_date:
                            os.remove(file_path)
                            print(f"🗑️ تم حذف النسخة القديمة: {filename}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تنظيف النسخ القديمة: {e}")
            return False
    
    def create_full_backup(self) -> bool:
        """إنشاء نسخة احتياطية كاملة"""
        print("💾 إنشاء نسخة احتياطية كاملة...")
        
        database_success = self.create_database_backup()
        config_success = self.create_config_backup()
        
        if database_success and config_success:
            print("✅ تم إنشاء النسخة الاحتياطية الكاملة بنجاح")
            return True
        else:
            print("⚠️ تم إنشاء نسخة احتياطية جزئية")
            return False

# إنشاء مثيل عام لنظام النسخ الاحتياطية
simple_backup = SimpleBackupSystem()
'''
        
        with open('modules/simple_backup.py', 'w', encoding='utf-8') as f:
            f.write(backup_code)
        
        print("✅ تم إنشاء نظام النسخ الاحتياطية البسيط")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء نظام النسخ الاحتياطية: {e}")
        return False

def main():
    """الدالة الرئيسية لتطبيق التحسينات"""
    print("🚀 بدء تطبيق التحسينات الفورية على الوكيل")
    print("=" * 60)
    
    improvements = [
        ("تنظيف الاستيرادات غير المستخدمة", clean_unused_imports),
        ("إصلاح معالج الإشارات", fix_signal_handler),
        ("إضافة تحسينات الأداء", add_performance_improvements),
        ("إنشاء لوحة المراقبة", create_monitoring_dashboard),
        ("إنشاء نظام النسخ الاحتياطية", create_backup_system)
    ]
    
    results = []
    
    for improvement_name, improvement_func in improvements:
        print(f"\n🔧 {improvement_name}...")
        try:
            result = improvement_func()
            results.append((improvement_name, result))
        except Exception as e:
            print(f"❌ خطأ في {improvement_name}: {e}")
            results.append((improvement_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 60)
    print("📋 ملخص التحسينات:")
    
    successful = 0
    for improvement_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {improvement_name}")
        if result:
            successful += 1
    
    print(f"\n🎯 تم تطبيق {successful}/{len(results)} تحسين بنجاح")
    
    if successful == len(results):
        print("🎉 تم تطبيق جميع التحسينات الفورية بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. تشغيل الوكيل للتأكد من عمله: python main.py")
        print("2. مراقبة الأداء باستخدام: python -c \"from modules.simple_dashboard import simple_dashboard; print(simple_dashboard.generate_simple_report())\"")
        print("3. إنشاء نسخة احتياطية: python -c \"from modules.simple_backup import simple_backup; simple_backup.create_full_backup()\"")
    else:
        print("⚠️ بعض التحسينات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return successful == len(results)

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحسينات بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
