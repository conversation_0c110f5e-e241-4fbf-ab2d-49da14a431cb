#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مستخرج أنماط صور الألعاب
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.game_image_style_extractor import game_style_extractor
from modules.logger import logger

async def create_diverse_game_images():
    """إنشاء صور ألعاب متنوعة للاختبار"""
    test_images = []
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # صورة 1: لعبة FPS حديثة
        img1 = Image.new('RGB', (1200, 800), color='#0a0a0a')
        draw1 = ImageDraw.Draw(img1)
        
        # خلفية مستقبلية
        draw1.rectangle([0, 0, 1200, 600], fill='#1a2332')
        
        # عناصر UI متقدمة
        draw1.rectangle([50, 50, 300, 100], fill='#ff4444', outline='#ffffff', width=2)
        draw1.text((60, 65), "HEALTH: 87%", fill='white')
        
        draw1.rectangle([50, 120, 250, 170], fill='#4444ff', outline='#ffffff', width=2)
        draw1.text((60, 135), "SHIELD: 100%", fill='white')
        
        draw1.rectangle([50, 190, 200, 240], fill='#ffaa00', outline='#ffffff', width=2)
        draw1.text((60, 205), "AMMO: 30/120", fill='white')
        
        # خريطة مصغرة متقدمة
        draw1.rectangle([950, 50, 1150, 250], fill='#333333', outline='#00ff00', width=3)
        draw1.text((1000, 200), "RADAR", fill='#00ff00')
        
        # نقاط وإحصائيات
        draw1.text((500, 50), "SCORE: 15,420", fill='#ffff00')
        draw1.text((500, 80), "KILLS: 23", fill='#ff0000')
        draw1.text((500, 110), "OBJECTIVE: SECURE AREA", fill='#00ffff')
        
        # تأثيرات بصرية
        for i in range(5):
            x = 200 + i * 100
            draw1.ellipse([x, 300, x+20, 320], fill='#ff6600')
        
        img1_path = 'test_modern_fps_game.png'
        img1.save(img1_path, 'PNG')
        test_images.append(img1_path)
        
        # صورة 2: لعبة RPG فانتازيا
        img2 = Image.new('RGB', (1200, 800), color='#2d1810')
        draw2 = ImageDraw.Draw(img2)
        
        # خلفية فانتازيا
        draw2.rectangle([0, 0, 1200, 600], fill='#4a3728')
        
        # شخصية في المنتصف
        draw2.ellipse([500, 200, 700, 400], fill='#8b4513', outline='#daa520', width=5)
        draw2.text((570, 290), "HERO", fill='#ffffff')
        
        # واجهة RPG كلاسيكية
        draw2.rectangle([0, 600, 1200, 800], fill='#1a1a1a')
        
        # أشرطة الشخصية
        draw2.rectangle([50, 620, 400, 650], fill='#ff0000', outline='#ffffff', width=2)
        draw2.text((60, 625), "HEALTH: 850/1000", fill='white')
        
        draw2.rectangle([50, 660, 350, 690], fill='#0066ff', outline='#ffffff', width=2)
        draw2.text((60, 665), "MANA: 420/500", fill='white')
        
        draw2.rectangle([50, 700, 300, 730], fill='#ffff00', outline='#ffffff', width=2)
        draw2.text((60, 705), "EXP: 2,450/3,000", fill='white')
        
        # معلومات الشخصية
        draw2.text((500, 620), "LEVEL: 25 PALADIN", fill='#daa520')
        draw2.text((500, 650), "LOCATION: ENCHANTED FOREST", fill='#90ee90')
        draw2.text((500, 680), "QUEST: DEFEAT THE DRAGON KING", fill='#87ceeb')
        draw2.text((500, 710), "GOLD: 15,750", fill='#ffd700')
        
        # جرد الأدوات
        for i in range(6):
            x = 800 + (i % 3) * 60
            y = 620 + (i // 3) * 60
            draw2.rectangle([x, y, x+50, y+50], fill='#666666', outline='#ffffff', width=1)
        
        img2_path = 'test_fantasy_rpg_game.png'
        img2.save(img2_path, 'PNG')
        test_images.append(img2_path)
        
        # صورة 3: لعبة سباق مستقبلية
        img3 = Image.new('RGB', (1200, 800), color='#000033')
        draw3 = ImageDraw.Draw(img3)
        
        # مضمار مستقبلي
        draw3.rectangle([0, 300, 1200, 500], fill='#333366')
        
        # سيارة مستقبلية
        draw3.rectangle([500, 350, 700, 450], fill='#ff0066', outline='#ffffff', width=3)
        draw3.text((570, 390), "RACER", fill='white')
        
        # تأثيرات السرعة
        for i in range(10):
            x = 50 + i * 100
            draw3.line([x, 320, x+50, 330], fill='#00ffff', width=2)
            draw3.line([x, 470, x+50, 480], fill='#00ffff', width=2)
        
        # واجهة السباق المتقدمة
        draw3.rectangle([0, 0, 1200, 100], fill='#000000')
        draw3.rectangle([0, 700, 1200, 800], fill='#000000')
        
        # معلومات السباق
        draw3.text((50, 20), "SPEED: 285 KM/H", fill='#00ff00')
        draw3.text((50, 40), "LAP: 3/5", fill='#ffffff')
        draw3.text((50, 60), "POSITION: 2nd/12", fill='#ffff00')
        
        draw3.text((600, 20), "TIME: 04:32.15", fill='#ffffff')
        draw3.text((600, 40), "BEST LAP: 01:45.23", fill='#00ff00')
        draw3.text((600, 60), "NEXT: TURBO BOOST", fill='#ff6600')
        
        # عداد السرعة متقدم
        draw3.ellipse([1000, 720, 1180, 780], fill='#333333', outline='#00ffff', width=3)
        draw3.text((1070, 745), "285", fill='#ff0000')
        
        # خريطة المضمار
        draw3.rectangle([50, 720, 200, 780], fill='#444444', outline='#00ff00', width=2)
        draw3.text((90, 745), "TRACK MAP", fill='#00ff00')
        
        img3_path = 'test_futuristic_racing_game.png'
        img3.save(img3_path, 'PNG')
        test_images.append(img3_path)
        
        logger.info(f"✅ تم إنشاء {len(test_images)} صورة لعبة متنوعة للاختبار")
        return test_images
        
    except ImportError:
        logger.warning("⚠️ PIL غير متوفر")
        return []
    except Exception as e:
        logger.error(f"❌ فشل في إنشاء صور الاختبار: {e}")
        return []

async def test_style_extraction():
    """اختبار استخراج الأنماط"""
    
    print("\n" + "="*80)
    print("🎨 اختبار مستخرج أنماط صور الألعاب - Gemini 2.5 Pro")
    print("="*80)
    
    if not game_style_extractor.enabled:
        print("❌ مستخرج الأنماط غير مفعل - تحقق من مفتاح API")
        return
    
    # إنشاء صور اختبار
    test_images = await create_diverse_game_images()
    
    if not test_images:
        print("❌ لا توجد صور للاختبار")
        return
    
    print(f"🎨 استخراج الأنماط من {len(test_images)} صورة...")
    
    results = []
    
    for i, image_path in enumerate(test_images, 1):
        print(f"\n🔍 {i}. استخراج النمط من: {os.path.basename(image_path)}")
        print("-" * 60)
        
        try:
            style = await game_style_extractor.extract_game_style(image_path)
            
            if style:
                results.append(style)
                
                print(f"✅ نجح الاستخراج - ثقة: {style.confidence_score:.2f}")
                print(f"🎮 اللعبة: {style.game_title}")
                print(f"🎯 النوع: {style.genre}")
                print(f"🎨 النمط الفني: {style.art_style}")
                print(f"🌈 الموضوع البصري: {style.visual_theme}")
                
                if style.color_palette:
                    print(f"🎨 لوحة الألوان: {', '.join(style.color_palette[:5])}")
                
                if style.dominant_colors:
                    print(f"🌈 الألوان المهيمنة: {', '.join(style.dominant_colors)}")
                
                print(f"💡 التكوين: {style.composition_style}")
                print(f"💡 الإضاءة: {style.lighting_type}")
                print(f"👁️ المنظور: {style.perspective}")
                print(f"😊 المزاج: {style.mood}")
                
                if style.ui_elements:
                    print(f"🖥️ عناصر UI: {', '.join(style.ui_elements[:3])}...")
                
                if style.creation_prompt:
                    print(f"\n💡 Prompt للإنشاء:")
                    print(f"   {style.creation_prompt[:150]}...")
                
                if style.style_prompt:
                    print(f"\n🎨 Prompt النمط:")
                    print(f"   {style.style_prompt[:150]}...")
                
                # حفظ النمط في ملف
                style_file = f"extracted_style_{i}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                game_style_extractor.save_style_to_file(style, style_file)
                
            else:
                print(f"❌ فشل في استخراج النمط")
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    # عرض الإحصائيات
    print(f"\n📊 ملخص النتائج:")
    print("=" * 60)
    
    successful = len(results)
    avg_confidence = sum(r.confidence_score for r in results) / max(successful, 1)
    
    print(f"نجح: {successful}/{len(test_images)}")
    print(f"متوسط الثقة: {avg_confidence:.2f}")
    
    if successful > 0:
        print(f"\n🎯 تحليل القدرات:")
        
        # تحليل القدرات المختلفة
        games_identified = sum(1 for r in results if r.game_title != "Unknown")
        genres_identified = sum(1 for r in results if r.genre != "Unknown")
        styles_identified = sum(1 for r in results if r.art_style != "Unknown")
        colors_extracted = sum(1 for r in results if r.color_palette)
        prompts_created = sum(1 for r in results if r.creation_prompt)
        
        print(f"   🎮 تحديد الألعاب: {games_identified}/{successful}")
        print(f"   🎯 تحديد الأنواع: {genres_identified}/{successful}")
        print(f"   🎨 تحليل الأنماط: {styles_identified}/{successful}")
        print(f"   🌈 استخراج الألوان: {colors_extracted}/{successful}")
        print(f"   💡 إنشاء Prompts: {prompts_created}/{successful}")
        
        # تحليل الأنماط المكتشفة
        if styles_identified > 0:
            print(f"\n🎨 الأنماط المكتشفة:")
            styles_found = [r.art_style for r in results if r.art_style != "Unknown"]
            for style in set(styles_found):
                count = styles_found.count(style)
                print(f"   • {style}: {count} مرة")
        
        # تحليل الألوان
        if colors_extracted > 0:
            print(f"\n🌈 الألوان الشائعة:")
            all_colors = []
            for r in results:
                all_colors.extend(r.color_palette)
            
            color_counts = {}
            for color in all_colors:
                color_counts[color] = color_counts.get(color, 0) + 1
            
            top_colors = sorted(color_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            for color, count in top_colors:
                print(f"   • {color}: {count} مرة")
    
    # عرض إحصائيات الاستخدام
    print(f"\n📈 إحصائيات النظام:")
    stats = game_style_extractor.get_usage_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # حفظ تقرير شامل
    report = {
        'test_timestamp': datetime.now().isoformat(),
        'total_images': len(test_images),
        'successful_extractions': successful,
        'average_confidence': avg_confidence,
        'system_stats': stats,
        'extracted_styles': [
            {
                'image': os.path.basename(r.source_image),
                'game_title': r.game_title,
                'genre': r.genre,
                'art_style': r.art_style,
                'visual_theme': r.visual_theme,
                'color_palette': r.color_palette,
                'confidence_score': r.confidence_score,
                'creation_prompt': r.creation_prompt[:200] if r.creation_prompt else ""
            }
            for r in results
        ]
    }
    
    report_file = f"game_style_extraction_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ التقرير في: {report_file}")
    
    # تنظيف الملفات
    for image_path in test_images:
        if os.path.exists(image_path):
            try:
                os.remove(image_path)
            except:
                pass

async def main():
    """الدالة الرئيسية"""
    try:
        await test_style_extraction()
        print("\n🎉 اكتمل اختبار مستخرج أنماط صور الألعاب!")
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في التشغيل: {e}")

if __name__ == "__main__":
    asyncio.run(main())
