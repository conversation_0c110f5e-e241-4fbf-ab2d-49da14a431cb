#!/usr/bin/env python3
# اختبار التحسينات المطبقة على نظام Whisper
import asyncio
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.whisper_quality_checker import whisper_quality_checker
from modules.advanced_text_analyzer import advanced_text_analyzer
from modules.text_cleanup_processor import text_cleanup_processor
from modules.logger import logger

async def test_whisper_improvements():
    """اختبار شامل للتحسينات المطبقة على نظام Whisper"""
    
    print("🧪 بدء اختبار تحسينات نظام Whisper...")
    print("=" * 60)
    
    # نصوص اختبار مختلفة
    test_cases = [
        {
            'name': 'نص عادي جيد',
            'text': 'مرحبا بكم في قناة الألعاب اليوم سنتحدث عن لعبة ماين كرافت الجديدة وتحديثاتها المثيرة',
            'expected_quality': 'جيد'
        },
        {
            'name': 'نص قصير جداً',
            'text': 'ل ع ب ة',
            'expected_quality': 'ضعيف'
        },
        {
            'name': 'نص مع رموز غريبة',
            'text': 'م ي ن ك ر ا ف ت !!! @@@ 123 456 789',
            'expected_quality': 'ضعيف'
        },
        {
            'name': 'نص مختلط اللغات',
            'text': 'hello مرحبا game لعبة minecraft ماين كرافت',
            'expected_quality': 'مقبول'
        },
        {
            'name': 'نص بتكرار مفرط',
            'text': 'اللعبة اللعبة اللعبة ممممممتعة جججججداً',
            'expected_quality': 'ضعيف'
        },
        {
            'name': 'نص فارغ',
            'text': '',
            'expected_quality': 'سيء'
        }
    ]
    
    # بيانات فيديو وهمية للاختبار
    test_video_data = {
        'id': 'test_video_123',
        'title': 'مراجعة لعبة ماين كرافت الجديدة',
        'description': 'في هذا الفيديو نستعرض أحدث تحديثات لعبة ماين كرافت',
        'duration': 600,  # 10 دقائق
        'channel_info': {
            'name': 'قناة الألعاب',
            'language': 'ar'
        }
    }
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 اختبار {i}: {test_case['name']}")
        print(f"النص الأصلي: '{test_case['text']}'")
        print("-" * 40)
        
        try:
            # 1. اختبار فحص الجودة الأساسي
            print("1️⃣ فحص الجودة الأساسي...")
            basic_quality = await whisper_quality_checker.check_transcript_quality(
                test_case['text'], test_video_data
            )
            print(f"   النقاط: {basic_quality['score']:.1f}/100")
            print(f"   المستوى: {basic_quality['quality_level']}")
            print(f"   مقبول: {'✅' if basic_quality['is_acceptable'] else '❌'}")
            
            # 2. اختبار التحليل المتقدم (إذا كان النص مقبولاً)
            advanced_result = None
            if basic_quality['is_acceptable']:
                print("\n2️⃣ التحليل المتقدم...")
                advanced_result = await advanced_text_analyzer.perform_deep_analysis(
                    test_case['text'], test_video_data
                )
                print(f"   النقاط المتقدمة: {advanced_result['score']:.1f}/100")
                print(f"   المستوى المتقدم: {advanced_result['quality_level']}")
                print(f"   ممتاز: {'✅' if advanced_result.get('is_excellent', False) else '❌'}")
            else:
                print("2️⃣ تخطي التحليل المتقدم (النص غير مقبول)")
            
            # 3. اختبار معالجة النصوص المشكوك فيها
            print("\n3️⃣ معالجة النصوص المشكوك فيها...")
            cleanup_result = await text_cleanup_processor.process_problematic_text(
                test_case['text'], test_video_data
            )
            print(f"   النص المحسن: '{cleanup_result['processed_text']}'")
            print(f"   قابل للاستخدام: {'✅' if cleanup_result['is_usable'] else '❌'}")
            print(f"   تحسن الطول: {cleanup_result['details'].get('improvement_ratio', 0):.2f}x")
            
            # تسجيل النتائج
            results.append({
                'test_name': test_case['name'],
                'original_text': test_case['text'],
                'basic_quality': basic_quality,
                'advanced_analysis': advanced_result,
                'cleanup_result': cleanup_result,
                'overall_success': cleanup_result['is_usable'] or basic_quality['is_acceptable']
            })
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append({
                'test_name': test_case['name'],
                'error': str(e),
                'overall_success': False
            })
    
    # عرض ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    successful_tests = sum(1 for result in results if result.get('overall_success', False))
    total_tests = len(results)
    
    print(f"إجمالي الاختبارات: {total_tests}")
    print(f"الاختبارات الناجحة: {successful_tests}")
    print(f"معدل النجاح: {successful_tests/total_tests*100:.1f}%")
    
    print("\n📋 تفاصيل النتائج:")
    for result in results:
        status = "✅ نجح" if result.get('overall_success', False) else "❌ فشل"
        print(f"  {result['test_name']}: {status}")
        
        if 'error' in result:
            print(f"    خطأ: {result['error']}")
        else:
            basic_score = result.get('basic_quality', {}).get('score', 0)
            cleanup_usable = result.get('cleanup_result', {}).get('is_usable', False)
            print(f"    الجودة الأساسية: {basic_score:.1f}/100")
            print(f"    قابل للاستخدام بعد المعالجة: {'نعم' if cleanup_usable else 'لا'}")
    
    print("\n🎯 التوصيات:")
    if successful_tests == total_tests:
        print("✅ جميع الاختبارات نجحت! النظام يعمل بشكل ممتاز.")
    elif successful_tests >= total_tests * 0.8:
        print("✅ معظم الاختبارات نجحت. النظام يعمل بشكل جيد.")
    elif successful_tests >= total_tests * 0.5:
        print("⚠️ نجح نصف الاختبارات. قد تحتاج لتحسينات إضافية.")
    else:
        print("❌ فشل معظم الاختبارات. يحتاج النظام لمراجعة شاملة.")
    
    print("\n🔧 ميزات النظام المحسن:")
    print("  • موافقة تلقائية على جميع الفيديوهات")
    print("  • فحص جودة متقدم للنصوص المستخرجة")
    print("  • معالجة ذكية للنصوص القصيرة والرموز الغريبة")
    print("  • تحليل عميق للمحتوى والسياق")
    print("  • طرق بديلة متعددة عند فشل Whisper")
    
    return results

async def test_specific_whisper_issues():
    """اختبار مشاكل Whisper المحددة"""
    
    print("\n🔍 اختبار مشاكل Whisper المحددة...")
    print("=" * 50)
    
    # مشاكل شائعة في Whisper
    whisper_issues = [
        {
            'issue': 'كلمات مقطعة',
            'text': 'م ا ي ن ك ر ا ف ت ل ع ب ة ج م ي ل ة',
            'expected_fix': 'ماين كرافت لعبة جميلة'
        },
        {
            'issue': 'تكرار مفرط',
            'text': 'اللعبة اللعبة اللعبة ممممتعة جججداً',
            'expected_fix': 'اللعبة متعة جداً'
        },
        {
            'issue': 'رموز غريبة',
            'text': 'hello@@@ world!!! 123 456',
            'expected_fix': 'hello world'
        },
        {
            'issue': 'نص قصير جداً',
            'text': 'hi',
            'expected_fix': 'محتوى محسن مع سياق'
        }
    ]
    
    for issue_test in whisper_issues:
        print(f"\n🐛 مشكلة: {issue_test['issue']}")
        print(f"النص الأصلي: '{issue_test['text']}'")
        
        try:
            # معالجة المشكلة
            cleanup_result = await text_cleanup_processor.process_problematic_text(
                issue_test['text']
            )
            
            processed_text = cleanup_result['processed_text']
            print(f"النص المعالج: '{processed_text}'")
            print(f"قابل للاستخدام: {'✅' if cleanup_result['is_usable'] else '❌'}")
            
            # فحص التحسن
            original_words = len(issue_test['text'].split())
            processed_words = len(processed_text.split())
            improvement = processed_words / original_words if original_words > 0 else 0
            
            print(f"تحسن عدد الكلمات: {improvement:.2f}x")
            
        except Exception as e:
            print(f"❌ خطأ في معالجة المشكلة: {e}")

if __name__ == "__main__":
    print("🚀 بدء اختبار تحسينات نظام Whisper...")
    
    try:
        # تشغيل الاختبارات
        asyncio.run(test_whisper_improvements())
        asyncio.run(test_specific_whisper_issues())
        
        print("\n✅ اكتملت جميع الاختبارات بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبارات: {e}")
        sys.exit(1)
