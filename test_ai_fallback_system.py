# اختبار النظام المحسن للبحث مع النماذج الاحتياطية
import asyncio
import sys
import os
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.enhanced_search_manager import enhanced_search_manager, SearchPriority
from modules.fallback_ai_manager import fallback_ai_manager, SearchRequest
from modules.content_scraper import ContentScraper
from modules.logger import logger

async def test_fallback_ai_models():
    """اختبار النماذج الاحتياطية للذكاء الاصطناعي"""
    print("🤖 اختبار النماذج الاحتياطية للذكاء الاصطناعي")
    print("=" * 60)
    
    # اختبار جميع النماذج
    test_results = await fallback_ai_manager.test_all_models()
    
    print("📊 نتائج اختبار النماذج:")
    for model_name, result in test_results.items():
        status = result.get('status', 'unknown')
        if status == 'success':
            response_time = result.get('response_time', 0)
            content_length = result.get('content_length', 0)
            supports_web_search = result.get('supports_web_search', False)
            
            print(f"   ✅ {model_name}:")
            print(f"      وقت الاستجابة: {response_time:.2f}s")
            print(f"      طول المحتوى: {content_length} حرف")
            print(f"      يدعم البحث على الويب: {'نعم' if supports_web_search else 'لا'}")
        elif status == 'unavailable':
            print(f"   ⚠️ {model_name}: غير متاح - {result.get('reason', 'سبب غير محدد')}")
        else:
            print(f"   ❌ {model_name}: فشل - {result.get('reason', 'خطأ غير محدد')}")
    
    print()

async def test_enhanced_search_manager():
    """اختبار مدير البحث المحسن"""
    print("🔍 اختبار مدير البحث المحسن")
    print("=" * 60)
    
    test_queries = [
        "gaming news today",
        "new video game releases",
        "أخبار الألعاب الجديدة"
    ]
    
    priorities = [
        ("سريع", SearchPriority.FAST),
        ("متوازن", SearchPriority.BALANCED),
        ("جودة عالية", SearchPriority.HIGH_QUALITY)
    ]
    
    for query in test_queries:
        print(f"\n🎯 اختبار البحث عن: '{query}'")
        print("-" * 40)
        
        for priority_name, priority in priorities:
            try:
                print(f"   📊 الأولوية: {priority_name}")
                start_time = datetime.now()
                
                result = await enhanced_search_manager.comprehensive_search(
                    query=query,
                    max_results=5,
                    priority=priority,
                    include_ai_analysis=True
                )
                
                end_time = datetime.now()
                response_time = (end_time - start_time).total_seconds()
                
                if result and result.get('success'):
                    print(f"   ✅ نجح في {response_time:.2f}s")
                    print(f"      الاستراتيجية: {result.get('best_strategy', 'غير محدد')}")
                    print(f"      النتائج: {result.get('unique_results', 0)}")
                    print(f"      الجودة: {result.get('quality_score', 0):.2f}")
                    
                    if result.get('ai_analysis'):
                        print(f"      تحليل AI: متوفر ({len(result['ai_analysis'])} حرف)")
                    
                else:
                    print(f"   ❌ فشل في {response_time:.2f}s")
                    print(f"      السبب: {result.get('error', 'غير محدد')}")
                
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
        
        print()

async def test_content_scraper_enhanced():
    """اختبار ContentScraper مع النظام المحسن"""
    print("📰 اختبار ContentScraper مع النظام المحسن")
    print("=" * 60)
    
    scraper = ContentScraper()
    
    test_methods = [
        ("البحث السريع", scraper.quick_ai_search),
        ("البحث المحسن", scraper.enhanced_search_with_ai_fallbacks),
        ("البحث العميق", scraper.deep_ai_search)
    ]
    
    test_query = "gaming news"
    
    for method_name, method in test_methods:
        try:
            print(f"\n🔍 اختبار {method_name}")
            print("-" * 30)
            
            start_time = datetime.now()
            
            if method_name == "البحث المحسن":
                results = await method(test_query, 5, "balanced")
            else:
                results = await method(test_query, 5)
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            
            if results:
                print(f"   ✅ {method_name}: {len(results)} نتيجة في {response_time:.2f}s")
                
                for i, result in enumerate(results[:2], 1):
                    print(f"   📄 نتيجة {i}:")
                    print(f"      العنوان: {result.get('title', 'غير محدد')[:50]}...")
                    print(f"      المصدر: {result.get('source', 'غير محدد')}")
                    print(f"      جودة المحتوى: {result.get('content_quality', 'غير محدد')}/10")
                    print(f"      صلة الألعاب: {result.get('gaming_relevance', 'غير محدد')}/10")
                    
                    if result.get('ai_enhanced'):
                        print(f"      معزز بالذكاء الاصطناعي: نعم")
                    
                    if result.get('search_strategy'):
                        print(f"      الاستراتيجية: {result['search_strategy']}")
            else:
                print(f"   ❌ {method_name}: لم يتم العثور على نتائج في {response_time:.2f}s")
                
        except Exception as e:
            print(f"   ❌ خطأ في {method_name}: {e}")

async def test_search_statistics():
    """اختبار إحصائيات البحث"""
    print("\n📊 إحصائيات النظام")
    print("=" * 60)
    
    # إحصائيات مدير البحث المحسن
    search_stats = enhanced_search_manager.get_search_statistics()
    print("🔍 إحصائيات مدير البحث المحسن:")
    print(f"   إجمالي البحثات: {search_stats['total_searches']}")
    print(f"   البحثات الناجحة: {search_stats['successful_searches']}")
    print(f"   معدل النجاح: {search_stats['success_rate']}%")
    print(f"   استخدام الاستراتيجيات: {search_stats['strategy_usage']}")
    
    # إحصائيات النماذج الاحتياطية
    ai_stats = fallback_ai_manager.get_usage_stats()
    print(f"\n🤖 إحصائيات النماذج الاحتياطية:")
    print(f"   إجمالي الطلبات: {ai_stats['total_requests']}")
    print(f"   الطلبات الناجحة: {ai_stats['successful_requests']}")
    print(f"   الطلبات الفاشلة: {ai_stats['failed_requests']}")
    print(f"   النماذج المستخدمة: {ai_stats['models_used']}")
    
    # توصيات النماذج
    recommendations = fallback_ai_manager.get_model_recommendations()
    print(f"\n💡 توصيات النماذج:")
    print(f"   الأفضل للبحث على الويب: {recommendations['best_for_web_search']}")
    print(f"   الأفضل للتحليل العميق: {recommendations['best_for_deep_analysis']}")
    print(f"   الأسرع: {recommendations['best_for_speed']}")
    print(f"   الأكثر موثوقية: {recommendations['most_reliable_fallback']}")

async def main():
    """الوظيفة الرئيسية للاختبار"""
    print("🚀 بدء اختبار النظام المحسن للبحث مع النماذج الاحتياطية")
    print("=" * 80)
    print(f"⏰ وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # اختبار النماذج الاحتياطية
        await test_fallback_ai_models()
        
        # اختبار مدير البحث المحسن
        await test_enhanced_search_manager()
        
        # اختبار ContentScraper المحسن
        await test_content_scraper_enhanced()
        
        # عرض الإحصائيات
        await test_search_statistics()
        
        print("\n🎉 اكتمل الاختبار بنجاح!")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
