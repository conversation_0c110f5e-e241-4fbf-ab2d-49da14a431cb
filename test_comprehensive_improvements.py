#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع التحسينات المطبقة على الوكيل
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_performance_optimizer():
    """اختبار محسن الأداء"""
    print("⚡ اختبار محسن الأداء...")
    
    try:
        from modules.performance_optimizer import performance_optimizer, DatabaseOptimizer
        
        # اختبار الكاش
        test_key = "test_key"
        test_value = {"data": "test_data", "timestamp": time.time()}
        
        # تخزين في الكاش
        performance_optimizer.cache_result(test_key, test_value, ttl=60)
        print("   ✅ تم تخزين البيانات في الكاش")
        
        # استرجاع من الكاش
        cached_result = performance_optimizer.get_cached_result(test_key)
        if cached_result == test_value:
            print("   ✅ تم استرجاع البيانات من الكاش بنجاح")
        else:
            print("   ❌ فشل في استرجاع البيانات من الكاش")
            return False
        
        # اختبار الإحصائيات
        stats = performance_optimizer.get_stats()
        print(f"   📊 إحصائيات الكاش: {stats}")
        
        # اختبار تحسين قاعدة البيانات
        db_success = DatabaseOptimizer.add_missing_indexes()
        if db_success:
            print("   ✅ تم إضافة فهارس قاعدة البيانات")
        
        optimize_success = DatabaseOptimizer.optimize_database()
        if optimize_success:
            print("   ✅ تم تحسين قاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار محسن الأداء: {e}")
        return False

async def test_advanced_monitoring():
    """اختبار نظام المراقبة المتقدم"""
    print("🔍 اختبار نظام المراقبة المتقدم...")
    
    try:
        from modules.advanced_monitoring import advanced_monitoring
        
        # اختبار الحالة الحالية
        status = advanced_monitoring.get_current_status()
        if 'overall_status' in status:
            print(f"   ✅ الحالة العامة: {status['overall_status']}")
            print(f"   💻 استخدام المعالج: {status.get('system_metrics', {}).get('cpu_percent', 0):.1f}%")
            print(f"   💾 استخدام الذاكرة: {status.get('system_metrics', {}).get('memory_percent', 0):.1f}%")
            print(f"   🚨 التنبيهات النشطة: {status.get('active_alerts', 0)}")
        else:
            print("   ❌ فشل في الحصول على الحالة الحالية")
            return False
        
        # اختبار التقرير
        report = advanced_monitoring.generate_monitoring_report()
        if report and len(report) > 200:
            print("   ✅ تم إنشاء تقرير المراقبة المتقدم")
            print(f"   📄 طول التقرير: {len(report)} حرف")
        else:
            print("   ❌ فشل في إنشاء تقرير المراقبة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار نظام المراقبة المتقدم: {e}")
        return False

async def run_comprehensive_test():
    """تشغيل الاختبار الشامل"""
    print("🧪 بدء الاختبار الشامل لجميع التحسينات")
    print("=" * 60)
    
    tests = [
        ("محسن الأداء", test_performance_optimizer),
        ("نظام المراقبة المتقدم", test_advanced_monitoring)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔧 اختبار {test_name}...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 60)
    print("📋 ملخص نتائج الاختبارات:")
    
    successful = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {test_name}")
        if result:
            successful += 1
    
    print(f"\n🎯 نجح {successful}/{len(results)} اختبار")
    
    if successful == len(results):
        print("🎉 جميع الاختبارات نجحت! الوكيل محسن ومجهز بالكامل.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return successful == len(results)

async def main():
    """الدالة الرئيسية"""
    try:
        success = await run_comprehensive_test()
        return success
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبارات بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {e}")
        sys.exit(1)
