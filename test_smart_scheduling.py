#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الجدولة الذكية الجديد
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.intelligent_cms import intelligent_cms
from modules.logger import logger

async def test_smart_scheduling():
    """اختبار نظام الجدولة الذكية"""
    print("🧪 بدء اختبار نظام الجدولة الذكية...")
    
    try:
        # اختبار 1: حساب الوقت الأمثل بدون بيانات مقال
        print("\n📋 اختبار 1: حساب الوقت الأمثل بدون بيانات مقال")
        result1 = await intelligent_cms.calculate_optimal_publishing_time()
        
        print(f"⏰ وقت الانتظار: {result1['wait_hours']:.1f} ساعة")
        print(f"🎯 مستوى الثقة: {result1['confidence_score']:.1f}%")
        print(f"📅 موعد النشر المقترح: {result1['target_time'].strftime('%Y-%m-%d %H:%M')}")
        print(f"💡 الأسباب: {', '.join(result1['reasoning'])}")
        
        # اختبار 2: حساب الوقت الأمثل مع بيانات مقال عادي
        print("\n📋 اختبار 2: حساب الوقت الأمثل مع مقال عادي")
        article_data_normal = {
            'title': 'مراجعة لعبة جديدة',
            'content': 'هذا محتوى مقال عادي عن مراجعة لعبة جديدة. ' * 20,
            'keywords': ['gaming', 'review', 'new'],
            'source': 'gamespot.com',
            'seo_score': 75,
            'image_urls': ['https://example.com/image.jpg']
        }
        
        result2 = await intelligent_cms.calculate_optimal_publishing_time(article_data_normal)
        
        print(f"⏰ وقت الانتظار: {result2['wait_hours']:.1f} ساعة")
        print(f"🎯 مستوى الثقة: {result2['confidence_score']:.1f}%")
        print(f"📅 موعد النشر المقترح: {result2['target_time'].strftime('%Y-%m-%d %H:%M')}")
        print(f"💡 الأسباب: {', '.join(result2['reasoning'])}")
        
        # اختبار 3: حساب الوقت الأمثل مع مقال عاجل
        print("\n📋 اختبار 3: حساب الوقت الأمثل مع مقال عاجل")
        article_data_urgent = {
            'title': 'خبر عاجل: إعلان لعبة جديدة',
            'content': 'هذا خبر عاجل وحصري عن إعلان لعبة جديدة مثيرة. ' * 30,
            'keywords': ['breaking', 'exclusive', 'announcement', 'new'],
            'source': 'ign.com',
            'seo_score': 90,
            'image_urls': ['https://example.com/image1.jpg', 'https://example.com/image2.jpg']
        }
        
        result3 = await intelligent_cms.calculate_optimal_publishing_time(article_data_urgent)
        
        print(f"⏰ وقت الانتظار: {result3['wait_hours']:.1f} ساعة")
        print(f"🎯 مستوى الثقة: {result3['confidence_score']:.1f}%")
        print(f"📅 موعد النشر المقترح: {result3['target_time'].strftime('%Y-%m-%d %H:%M')}")
        print(f"💡 الأسباب: {', '.join(result3['reasoning'])}")
        
        # اختبار 4: مقارنة النتائج
        print("\n📊 مقارنة النتائج:")
        print(f"بدون مقال: {result1['wait_hours']:.1f} ساعة (ثقة: {result1['confidence_score']:.1f}%)")
        print(f"مقال عادي: {result2['wait_hours']:.1f} ساعة (ثقة: {result2['confidence_score']:.1f}%)")
        print(f"مقال عاجل: {result3['wait_hours']:.1f} ساعة (ثقة: {result3['confidence_score']:.1f}%)")
        
        # اختبار 5: فحص العوامل المؤثرة
        print("\n🔍 العوامل المؤثرة في القرار:")
        factors = result3.get('factors_considered', {})
        
        if 'historical_performance' in factors:
            hist = factors['historical_performance']
            print(f"📈 أفضل ساعات تاريخياً: {hist.get('best_hours', [])}")
            print(f"📅 أفضل أيام تاريخياً: {hist.get('best_weekdays', [])}")
        
        if 'current_trends' in factors:
            trends = factors['current_trends']
            print(f"🕐 الساعة الحالية: {trends.get('current_hour', 'غير محدد')}")
            print(f"📆 اليوم الحالي: {trends.get('current_weekday', 'غير محدد')}")
            print(f"👥 نشاط المستخدمين: {trends.get('user_activity_score', 'غير محدد')}")
        
        if 'competition_analysis' in factors:
            comp = factors['competition_analysis']
            print(f"🏆 مستوى المنافسة: {comp.get('competition_level', 'غير محدد')}")
            print(f"⏰ الساعات المثلى: {comp.get('optimal_hours', [])}")
        
        print("\n✅ اكتمل اختبار نظام الجدولة الذكية بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الجدولة الذكية: {e}")
        logger.error("❌ فشل اختبار نظام الجدولة الذكية", e)
        return False

async def test_decision_making():
    """اختبار نظام اتخاذ القرارات"""
    print("\n🧠 اختبار نظام اتخاذ القرارات...")
    
    try:
        decisions = await intelligent_cms.make_content_decisions()
        
        if decisions:
            print("✅ تم اتخاذ قرارات بنجاح")
            print(f"📊 الثقة في القرارات: {decisions.get('confidence_score', 'غير محدد')}")
            print(f"⏰ وقت القرار: {decisions.get('timestamp', 'غير محدد')}")
        else:
            print("⚠️ لم يتم اتخاذ قرارات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اتخاذ القرارات: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبارات النظام الذكي الجديد")
    print("=" * 50)
    
    # اختبار نظام الجدولة الذكية
    scheduling_success = await test_smart_scheduling()
    
    # اختبار نظام اتخاذ القرارات
    decision_success = await test_decision_making()
    
    print("\n" + "=" * 50)
    print("📋 ملخص نتائج الاختبارات:")
    print(f"🗓️ نظام الجدولة الذكية: {'✅ نجح' if scheduling_success else '❌ فشل'}")
    print(f"🧠 نظام اتخاذ القرارات: {'✅ نجح' if decision_success else '❌ فشل'}")
    
    if scheduling_success and decision_success:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")

if __name__ == "__main__":
    asyncio.run(main())
