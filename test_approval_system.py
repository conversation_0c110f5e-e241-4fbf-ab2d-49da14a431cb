#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الموافقة على الفيديوهات
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_test_header(test_name):
    """طباعة رأس الاختبار"""
    print(f"\n{'='*60}")
    print(f"🧪 اختبار: {test_name}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """طباعة نتيجة الاختبار"""
    status = "✅ نجح" if success else "❌ فشل"
    print(f"{status} {test_name}")
    if details:
        print(f"   📝 {details}")

async def test_approval_system_setup():
    """اختبار إعداد نظام الموافقة"""
    print_test_header("إعداد نظام الموافقة")
    
    try:
        from modules.video_approval_system import VideoApprovalSystem
        from config.settings import BotConfig
        
        # إنشاء نظام الموافقة
        approval_system = VideoApprovalSystem()
        print_result("إنشاء VideoApprovalSystem", True)
        
        # فحص التكوين
        has_token = bool(approval_system.bot_token)
        print_result("توكن Telegram", has_token, 
                    f"التوكن: {'متوفر' if has_token else 'غير متوفر'}")
        
        # فحص معرف المدير
        admin_id = approval_system._get_admin_chat_id()
        has_admin = bool(admin_id)
        print_result("معرف المدير", has_admin,
                    f"المعرف: {admin_id if admin_id else 'غير محدد'}")
        
        # فحص حالة التفعيل
        is_enabled = getattr(approval_system, 'approval_enabled', False)
        print_result("حالة التفعيل", is_enabled,
                    f"النظام: {'مفعل' if is_enabled else 'معطل'}")
        
        # فحص نوع التطبيق
        app_type = type(approval_system.application).__name__ if approval_system.application else "None"
        print_result("نوع التطبيق", True,
                    f"التطبيق: {app_type}")
        
        return has_token and has_admin
        
    except Exception as e:
        print_result("إعداد نظام الموافقة", False, f"خطأ: {str(e)}")
        return False

async def test_approval_message_format():
    """اختبار تنسيق رسالة الموافقة"""
    print_test_header("تنسيق رسالة الموافقة")
    
    try:
        from modules.video_approval_system import VideoApprovalSystem
        
        approval_system = VideoApprovalSystem()
        
        # بيانات فيديو اختبار
        test_video = {
            'id': 'test_video_123',
            'title': 'Star Citizen (Pyro) and It Took Forever!',
            'duration': 720,  # 12 دقيقة
            'published_at': '2025-01-20T15:30:00Z',
            'channel_info': {
                'name': 'LevelCap Gaming',
                'language': 'en'
            }
        }
        
        # تنسيق الرسالة
        message = approval_system._format_approval_message(test_video)
        
        # فحص محتوى الرسالة
        has_title = test_video['title'] in message
        has_channel = test_video['channel_info']['name'] in message
        has_duration = '12:00' in message
        message_length = len(message)
        
        print_result("تضمين العنوان", has_title)
        print_result("تضمين اسم القناة", has_channel)
        print_result("تضمين المدة", has_duration)
        print_result("طول الرسالة مناسب", message_length > 100,
                    f"الطول: {message_length} حرف")
        
        # عرض جزء من الرسالة
        print(f"\n📄 عينة من الرسالة:")
        print(message[:200] + "..." if len(message) > 200 else message)
        
        return has_title and has_channel and message_length > 100
        
    except Exception as e:
        print_result("تنسيق رسالة الموافقة", False, f"خطأ: {str(e)}")
        return False

async def test_approval_request():
    """اختبار طلب الموافقة"""
    print_test_header("طلب الموافقة")
    
    try:
        from modules.video_approval_system import VideoApprovalSystem
        
        approval_system = VideoApprovalSystem()
        
        # بيانات فيديو اختبار
        test_video = {
            'id': 'test_video_456',
            'title': 'Gaming News Update - January 2025',
            'duration': 480,  # 8 دقائق
            'published_at': '2025-01-20T12:00:00Z',
            'channel_info': {
                'name': 'Gaming News Channel',
                'language': 'en'
            }
        }
        
        # متغير لتتبع نتيجة الموافقة
        approval_result = {'approved': None, 'reason': None}
        
        async def test_callback(approved: bool, reason: str):
            """callback اختبار"""
            approval_result['approved'] = approved
            approval_result['reason'] = reason
            print(f"📞 تم استدعاء callback: موافقة={approved}, السبب={reason}")
        
        # طلب الموافقة
        approval_id = await approval_system.request_video_approval(test_video, test_callback)
        
        # فحص النتائج
        has_approval_id = bool(approval_id)
        callback_called = approval_result['approved'] is not None
        
        print_result("إنشاء معرف الموافقة", has_approval_id,
                    f"المعرف: {approval_id}")
        print_result("استدعاء callback", callback_called,
                    f"النتيجة: {approval_result}")
        
        # فحص إذا تم حفظ الفيديو في الانتظار
        if approval_system.approval_enabled:
            video_saved = approval_id in approval_system.pending_videos
            print_result("حفظ الفيديو في الانتظار", video_saved)
        else:
            print_result("الموافقة التلقائية", True, "النظام معطل - موافقة تلقائية")
        
        return has_approval_id and callback_called
        
    except Exception as e:
        print_result("طلب الموافقة", False, f"خطأ: {str(e)}")
        return False

async def test_telegram_connection():
    """اختبار الاتصال بـ Telegram"""
    print_test_header("اختبار الاتصال بـ Telegram")
    
    try:
        from telegram import Bot
        from config.settings import BotConfig
        
        if not BotConfig.TELEGRAM_BOT_TOKEN:
            print_result("توكن Telegram", False, "التوكن غير متوفر")
            return False
        
        # إنشاء البوت
        bot = Bot(token=BotConfig.TELEGRAM_BOT_TOKEN)
        
        # اختبار الحصول على معلومات البوت
        try:
            bot_info = await bot.get_me()
            bot_username = bot_info.username
            bot_name = bot_info.first_name
            
            print_result("الاتصال بـ Telegram", True,
                        f"البوت: @{bot_username} ({bot_name})")
            
            # اختبار معرف المدير
            admin_id = getattr(BotConfig, 'TELEGRAM_ADMIN_ID', None)
            if admin_id:
                print_result("معرف المدير", True, f"المدير: {admin_id}")
                return True
            else:
                print_result("معرف المدير", False, "غير محدد")
                return False
                
        except Exception as bot_error:
            print_result("الاتصال بـ Telegram", False, f"خطأ: {bot_error}")
            return False
        
    except Exception as e:
        print_result("اختبار Telegram", False, f"خطأ: {str(e)}")
        return False

async def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار نظام الموافقة...")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("إعداد نظام الموافقة", test_approval_system_setup),
        ("تنسيق رسالة الموافقة", test_approval_message_format),
        ("طلب الموافقة", test_approval_request),
        ("اختبار الاتصال بـ Telegram", test_telegram_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print_result(test_name, False, f"خطأ غير متوقع: {str(e)}")
            results.append((test_name, False))
    
    # تقرير النتائج النهائية
    print_test_header("تقرير النتائج النهائية")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        print_result(test_name, result)
    
    print(f"\n📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 نظام الموافقة يعمل بشكل صحيح!")
        print("📱 سيتم إرسال رسائل الموافقة للمدير @Yaasssssin")
    elif passed >= 3:
        print("✅ نظام الموافقة يعمل بشكل جيد مع بعض التحسينات")
    else:
        print("⚠️ نظام الموافقة يحتاج إصلاحات")
    
    return passed >= 3

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ حرج: {e}")
        sys.exit(1)
