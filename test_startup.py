#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بدء التشغيل للتشخيص
"""

import sys
import traceback

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        print("   📦 استيراد الوحدات الأساسية...")
        import os
        import asyncio
        from datetime import datetime
        print("   ✅ الوحدات الأساسية")
        
        print("   📦 استيراد وحدات المشروع...")
        from modules.logger import logger
        print("   ✅ logger")
        
        from modules.database import db
        print("   ✅ database")
        
        from config.settings import BotConfig
        print("   ✅ BotConfig")
        
        print("   📦 اختبار الأنظمة المحسنة...")
        try:
            from modules.agent_state_manager import agent_state_manager, AgentState
            print("   ✅ agent_state_manager")
            
            from modules.smart_database_manager import smart_db_manager
            print("   ✅ smart_database_manager")
            
            from modules.operation_manager import operation_manager, OperationType, OperationPriority
            print("   ✅ operation_manager")
            
            from modules.smart_lifecycle_manager import lifecycle_manager, StartupMode, ShutdownReason
            print("   ✅ smart_lifecycle_manager")
            
            print("   🎉 جميع الأنظمة المحسنة متوفرة!")
            return True
            
        except Exception as e:
            print(f"   ⚠️ الأنظمة المحسنة غير متوفرة: {e}")
            print("   📝 سيتم استخدام النظام التقليدي")
            return False
            
    except Exception as e:
        print(f"   ❌ فشل في الاستيراد: {e}")
        traceback.print_exc()
        return False

def test_config():
    """اختبار التكوين"""
    print("\n🔧 اختبار التكوين...")
    
    try:
        from config.settings import BotConfig

        # فحص المتغيرات المطلوبة
        missing_vars = []

        if not BotConfig.GEMINI_API_KEY:
            missing_vars.append('GEMINI_API_KEY')
        if not BotConfig.BLOGGER_CLIENT_ID:
            missing_vars.append('BLOGGER_CLIENT_ID')
        if not BotConfig.BLOGGER_CLIENT_SECRET:
            missing_vars.append('BLOGGER_CLIENT_SECRET')
        if not BotConfig.BLOGGER_BLOG_ID:
            missing_vars.append('BLOGGER_BLOG_ID')

        if missing_vars:
            print(f"   ⚠️ متغيرات مفقودة: {', '.join(missing_vars)}")
            return False
        else:
            print("   ✅ جميع المتغيرات المطلوبة متوفرة")
            return True
            
    except Exception as e:
        print(f"   ❌ فشل في فحص التكوين: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from modules.database import db
        
        # محاولة إنشاء الجداول
        db.init_database()
        print("   ✅ تم إنشاء/فحص الجداول")
        
        # اختبار الاتصال
        articles = db.get_recent_articles(limit=1)
        print(f"   ✅ تم الاتصال بقاعدة البيانات ({len(articles)} مقال)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ فشل في اختبار قاعدة البيانات: {e}")
        traceback.print_exc()
        return False

async def test_enhanced_system():
    """اختبار النظام المحسن"""
    print("\n🚀 اختبار النظام المحسن...")
    
    try:
        from modules.smart_lifecycle_manager import lifecycle_manager, StartupMode
        
        # اختبار البدء
        print("   🔄 اختبار البدء الذكي...")
        success = await lifecycle_manager.smart_startup(StartupMode.SAFE_MODE)
        
        if success:
            print("   ✅ نجح البدء الذكي")
            
            # اختبار الإيقاف
            print("   🛑 اختبار الإيقاف الآمن...")
            from modules.smart_lifecycle_manager import ShutdownReason
            success = await lifecycle_manager.graceful_shutdown(
                reason=ShutdownReason.USER_REQUEST,
                initiated_by="test_startup"
            )
            
            if success:
                print("   ✅ نجح الإيقاف الآمن")
                return True
            else:
                print("   ⚠️ مشاكل في الإيقاف الآمن")
                return False
        else:
            print("   ❌ فشل البدء الذكي")
            return False
            
    except Exception as e:
        print(f"   ❌ فشل في اختبار النظام المحسن: {e}")
        traceback.print_exc()
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار التشخيص...")
    print("=" * 50)
    
    # اختبار الاستيرادات
    imports_ok = test_imports()
    
    # اختبار التكوين
    config_ok = test_config()
    
    # اختبار قاعدة البيانات
    database_ok = test_database()
    
    # اختبار النظام المحسن (إذا كان متوفراً)
    enhanced_ok = True
    if imports_ok:
        enhanced_ok = await test_enhanced_system()
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    print("📋 نتائج التشخيص:")
    print(f"   📦 الاستيرادات: {'✅ نجح' if imports_ok else '❌ فشل'}")
    print(f"   🔧 التكوين: {'✅ نجح' if config_ok else '❌ فشل'}")
    print(f"   🗄️ قاعدة البيانات: {'✅ نجح' if database_ok else '❌ فشل'}")
    print(f"   🚀 النظام المحسن: {'✅ نجح' if enhanced_ok else '❌ فشل'}")
    
    all_ok = imports_ok and config_ok and database_ok and enhanced_ok
    
    if all_ok:
        print("\n🎉 جميع الاختبارات نجحت! الوكيل جاهز للتشغيل.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return all_ok

if __name__ == "__main__":
    import asyncio
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ حرج في الاختبار: {e}")
        traceback.print_exc()
        sys.exit(1)
