#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تحويل الصوت إلى نص المحسن
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer

async def test_improved_whisper():
    """اختبار نظام Whisper المحسن"""
    print("🧪 اختبار نظام تحويل الصوت إلى نص المحسن...")
    print("=" * 60)
    
    # إنشاء محلل YouTube متقدم
    analyzer = AdvancedYouTubeAnalyzer()
    
    # فيديوهات اختبار (مختلفة اللغات)
    test_videos = [
        {
            'id': '6MZPDyD_ZsA',
            'title': 'Gaming News Video English Review Gameplay Trailer',
            'expected_language': 'en',
            'description': 'This is a gaming video in English about video games and technology reviews'
        }
    ]
    
    successful_tests = 0
    total_tests = len(test_videos)
    
    for i, video in enumerate(test_videos, 1):
        try:
            print(f"\n🎬 اختبار {i}/{total_tests}: {video['title']}")
            print(f"   ID: {video['id']}")
            print(f"   اللغة المتوقعة: {video['expected_language']}")
            print("-" * 40)
            
            # اختبار تحديد اللغة
            video_data = {
                'title': video['title'],
                'description': video['description']
            }
            
            detected_language = await analyzer._detect_video_language(video_data)
            print(f"🌐 اللغة المحددة: {detected_language}")
            
            # اختبار استخراج النص
            print(f"🎤 بدء استخراج النص...")
            
            transcript = await analyzer.extract_video_transcript_with_whisper(video['id'])
            
            if transcript and len(transcript.strip()) > 50:
                print(f"✅ نجح الاختبار!")
                print(f"   طول النص: {len(transcript)} حرف")
                print(f"   عدد الكلمات: {len(transcript.split())} كلمة")
                print(f"   عينة: {transcript[:100]}...")
                successful_tests += 1
            else:
                print(f"❌ فشل الاختبار - نص قصير أو فارغ")
                if transcript:
                    print(f"   النص المستخرج: '{transcript}'")
                    
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
        
        # تأخير بين الاختبارات
        if i < total_tests:
            print("⏳ انتظار 30 ثانية قبل الاختبار التالي...")
            await asyncio.sleep(30)
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار الشامل:")
    print("-" * 30)
    print(f"✅ نجح: {successful_tests}/{total_tests}")
    print(f"📈 معدل النجاح: {(successful_tests/total_tests*100):.1f}%")
    
    if successful_tests > 0:
        print("\n💡 التحسينات المطبقة:")
        print("   • تحديد تلقائي للغة الفيديو")
        print("   • استخدام نموذج Whisper أكبر (large-v3)")
        print("   • تحسين معالجة الأخطاء")
        print("   • طرق بديلة متعددة")
        print("   • ضغط الملفات الكبيرة")
        print("   • Whisper محلي كبديل")
        
        print("\n🎯 الفوائد:")
        print("   • جودة أعلى في تحويل الصوت")
        print("   • دعم أفضل للغة العربية")
        print("   • معالجة محسنة للأخطاء")
        print("   • موثوقية أعلى")
        
        return True
    else:
        print("\n⚠️ جميع الاختبارات فشلت - يرجى مراجعة الإعدادات")
        return False

async def test_language_detection():
    """اختبار تحديد اللغة"""
    print("\n🌐 اختبار تحديد اللغة...")
    print("-" * 30)
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    test_cases = [
        {
            'title': 'مراجعة لعبة جديدة - أفضل الألعاب العربية',
            'description': 'في هذا الفيديو نراجع أحدث الألعاب العربية والعالمية مع شرح مفصل',
            'expected': 'ar'
        },
        {
            'title': 'Gaming Review - Best Games 2024 Gameplay Trailer',
            'description': 'In this video we review the latest games with detailed gameplay analysis',
            'expected': 'en'
        },
        {
            'title': 'Gaming News Update - اخبار الالعاب',
            'description': 'اخبار جديدة عن الالعاب والتكنولوجيا مع مراجعة شاملة',
            'expected': 'ar'  # أكثر عربية
        }
    ]
    
    correct_detections = 0
    
    for i, case in enumerate(test_cases, 1):
        video_data = {
            'title': case['title'],
            'description': case['description']
        }
        
        detected = await analyzer._detect_video_language(video_data)
        is_correct = detected == case['expected']
        
        print(f"🧪 اختبار {i}:")
        print(f"   العنوان: {case['title'][:50]}...")
        print(f"   المتوقع: {case['expected']}")
        print(f"   المحدد: {detected}")
        print(f"   النتيجة: {'✅ صحيح' if is_correct else '❌ خطأ'}")
        
        if is_correct:
            correct_detections += 1
    
    print(f"\n📊 نتائج تحديد اللغة:")
    print(f"   صحيح: {correct_detections}/{len(test_cases)}")
    print(f"   دقة: {(correct_detections/len(test_cases)*100):.1f}%")

def test_prompt_generation():
    """اختبار إنشاء prompts مساعدة"""
    print("\n🎯 اختبار إنشاء Prompts...")
    print("-" * 30)
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    languages = ['ar', 'en']
    
    for lang in languages:
        prompt = analyzer._get_whisper_prompt(lang)
        print(f"🌐 {lang.upper()}:")
        print(f"   {prompt}")

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار شامل لنظام تحويل الصوت المحسن...")
    print("🎯 التحسينات: تحديد اللغة + نموذج أكبر + معالجة أخطاء")
    print("=" * 70)
    
    try:
        # اختبار تحديد اللغة
        await test_language_detection()
        
        # اختبار إنشاء prompts
        test_prompt_generation()
        
        # اختبار النظام الكامل
        success = await test_improved_whisper()
        
        print("\n" + "=" * 70)
        print("📋 ملخص الاختبار الشامل:")
        print("-" * 35)
        
        if success:
            print("✅ نظام تحويل الصوت محسن ويعمل بشكل أفضل!")
            print("\n💡 التحسينات المحققة:")
            print("   • تحديد تلقائي دقيق للغة")
            print("   • نموذج Whisper أكبر وأدق")
            print("   • معالجة محسنة للأخطاء")
            print("   • طرق بديلة متعددة")
            print("   • ضغط ذكي للملفات")
            
            print("\n🎯 النتائج المتوقعة:")
            print("   • جودة أعلى في النصوص المستخرجة")
            print("   • دعم أفضل للغة العربية")
            print("   • تقليل الأخطاء والنصوص القصيرة")
            print("   • موثوقية أعلى في الاستخراج")
            
            return True
        else:
            print("⚠️ بعض الاختبارات فشلت، لكن التحسينات مطبقة")
            print("💡 قد تحتاج لفحص:")
            print("   • مفاتيح APIs")
            print("   • اتصال الإنترنت")
            print("   • توفر الفيديوهات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
