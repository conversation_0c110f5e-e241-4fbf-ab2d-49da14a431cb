# دليل استخدام النظام المحسن لإنشاء الصور اليدوية

## 🎯 نظرة عامة

تم تطوير نظام محسن لإنشاء الصور اليدوية يدعم:
- ✅ خطوط مخصصة للعربية والإنجليزية
- ✅ خلفيات حقيقية مصنفة حسب نوع اللعبة
- ✅ اختيار ذكي للخلفيات والخطوط
- ✅ دعم كامل للغتين العربية والإنجليزية

## 📁 هيكل المجلدات

```
assets/
├── fonts/
│   ├── arabic/          # الخطوط العربية
│   │   ├── README.md
│   │   └── [ضع هنا ملفات .ttf و .otf العربية]
│   └── english/         # الخطوط الإنجليزية
│       ├── README.md
│       └── [ضع هنا ملفات .ttf و .otf الإنجليزية]
└── backgrounds/
    ├── العاب_اكشن/        # ألعاب الأكشن والقتال
    ├── العاب_كلاسيكية/    # الألعاب الكلاسيكية والقديمة
    ├── العاب_مغامرة/      # ألعاب المغامرة والاستكشاف
    ├── العاب_رعب/        # ألعاب الرعب والإثارة
    ├── العاب_رياضية/     # الألعاب الرياضية
    ├── العاب_سباق/       # ألعاب السباق والسيارات
    ├── العاب_ار_بي_جي/   # ألعاب لعب الأدوار
    ├── العاب_استراتيجية/ # الألعاب الاستراتيجية
    ├── العاب_محاكاة/     # ألعاب المحاكاة
    ├── العاب_الغاز/      # ألعاب الألغاز والذكاء
    ├── العاب_قتال/       # ألعاب القتال
    ├── العاب_اطلاق_نار/  # ألعاب إطلاق النار
    ├── العاب_منصات/      # ألعاب المنصات
    └── العاب_متنوعة/     # ألعاب متنوعة أخرى
```

## 🔤 إضافة الخطوط

### الخطوط العربية
1. انتقل إلى مجلد `assets/fonts/arabic/`
2. ضع ملفات الخطوط العربية (.ttf أو .otf)
3. أمثلة على خطوط عربية جيدة:
   - Cairo-Regular.ttf
   - Amiri-Regular.ttf
   - Tajawal-Regular.ttf
   - NotoSansArabic-Regular.ttf
   - Almarai-Regular.ttf

### الخطوط الإنجليزية
1. انتقل إلى مجلد `assets/fonts/english/`
2. ضع ملفات الخطوط الإنجليزية (.ttf أو .otf)
3. أمثلة على خطوط إنجليزية جيدة:
   - Roboto-Regular.ttf
   - OpenSans-Regular.ttf
   - Montserrat-Regular.ttf
   - Lato-Regular.ttf
   - SourceSansPro-Regular.ttf

## 🖼️ إضافة الخلفيات

### المواصفات المطلوبة
- **الصيغ المدعومة**: JPG, JPEG, PNG, WEBP
- **الحد الأدنى للحجم**: 1200x800 بكسل
- **الحد الأقصى للحجم**: 4000x3000 بكسل
- **الجودة**: عالية ووضوح جيد

### دليل الفئات

#### 🔫 العاب_اكشن
- ألعاب الأكشن والقتال
- أمثلة: Call of Duty, Battlefield, Assassin's Creed
- خلفيات مناسبة: مشاهد قتال، انفجارات، أسلحة

#### 🏛️ العاب_كلاسيكية  
- الألعاب الكلاسيكية والقديمة
- أمثلة: Super Mario, Pac-Man, Tetris
- خلفيات مناسبة: بكسل آرت، ألوان زاهية، طابع قديم

#### 🗺️ العاب_مغامرة
- ألعاب المغامرة والاستكشاف
- أمثلة: The Legend of Zelda, Uncharted, Tomb Raider
- خلفيات مناسبة: مناظر طبيعية، خرائط، كنوز

#### 👻 العاب_رعب
- ألعاب الرعب والإثارة
- أمثلة: Resident Evil, Silent Hill, Dead Space
- خلفيات مناسبة: أجواء مظلمة، مخيفة، غامضة

#### ⚽ العاب_رياضية
- الألعاب الرياضية
- أمثلة: FIFA, NBA 2K, PES
- خلفيات مناسبة: ملاعب، كرات، رياضيين

#### 🏎️ العاب_سباق
- ألعاب السباق والسيارات
- أمثلة: Forza, Gran Turismo, Need for Speed
- خلفيات مناسبة: سيارات، حلبات سباق، سرعة

#### 🧙 العاب_ار_بي_جي
- ألعاب لعب الأدوار
- أمثلة: Final Fantasy, The Witcher, Skyrim
- خلفيات مناسبة: خيال، سحر، شخصيات، عوالم خيالية

#### 🏰 العاب_استراتيجية
- الألعاب الاستراتيجية
- أمثلة: Age of Empires, Civilization, StarCraft
- خلفيات مناسبة: خرائط، جيوش، حضارات

#### 🏙️ العاب_محاكاة
- ألعاب المحاكاة
- أمثلة: The Sims, SimCity, Cities: Skylines
- خلفيات مناسبة: مدن، حياة يومية، بناء

#### 🧩 العاب_الغاز
- ألعاب الألغاز والذكاء
- أمثلة: Portal, Tetris, Monument Valley
- خلفيات مناسبة: أشكال هندسية، ألغاز، تفكير

#### 🥊 العاب_قتال
- ألعاب القتال
- أمثلة: Street Fighter, Tekken, Mortal Kombat
- خلفيات مناسبة: مقاتلين، حلبات قتال، طاقة

#### 🎯 العاب_اطلاق_نار
- ألعاب إطلاق النار
- أمثلة: Counter-Strike, Overwatch, Valorant
- خلفيات مناسبة: أسلحة، خرائط، تكتيكات

#### 🦘 العاب_منصات
- ألعاب المنصات
- أمثلة: Super Mario, Sonic, Crash Bandicoot
- خلفيات مناسبة: منصات، قفز، مغامرات ملونة

#### 🎮 العاب_متنوعة
- ألعاب متنوعة أخرى
- أمثلة: أي لعبة لا تنتمي للفئات الأخرى
- خلفيات مناسبة: عامة، متنوعة، ألعاب

## 🚀 كيف يعمل النظام

### 1. تحديد الموضوع
- النظام يحلل عنوان ومحتوى المقال
- يحدد الفئة المناسبة بدقة 90%
- يستخدم كلمات مفتاحية بالعربية والإنجليزية

### 2. اختيار الخلفية
- يبحث عن صور في المجلد المناسب للفئة
- يختار صورة عشوائية من الصور المتاحة
- إذا لم توجد صور، يستخدم خلفية متدرجة كبديل

### 3. اختيار الخط
- يحدد لغة النص (عربي أو إنجليزي)
- يختار خطاً عشوائياً من المجلد المناسب
- إذا لم توجد خطوط مخصصة، يستخدم خطوط النظام

### 4. إنشاء الصورة
- يدمج النص مع الخلفية
- يضيف تأثيرات للوضوح (حواف سوداء، طبقة شفافة)
- يضيف العلامة المائية
- يحفظ الصورة بجودة عالية

## 🔧 نصائح للحصول على أفضل النتائج

### للخطوط:
- استخدم خطوط عالية الجودة
- تأكد من دعم الخط للغة المطلوبة
- نوع في الخطوط لتجنب التكرار

### للخلفيات:
- استخدم صور عالية الدقة (1920x1080 أو أكبر)
- تأكد من مناسبة الصورة لفئة الألعاب
- تجنب الصور المزدحمة بالتفاصيل
- استخدم ألوان متناسقة وجذابة

### عامة:
- أضف عدة صور في كل فئة للتنوع
- أضف عدة خطوط لكل لغة
- اختبر النظام بانتظام
- احذف الصور أو الخطوط غير المناسبة

## 🎉 الخلاصة

النظام الآن جاهز للاستخدام! ما عليك سوى:
1. إضافة الخطوط في المجلدات المناسبة
2. إضافة الخلفيات في الفئات المناسبة  
3. تشغيل النظام والاستمتاع بالصور المحسنة

النظام سيختار تلقائياً الخط والخلفية المناسبة لكل مقال حسب موضوعه ولغته!
