#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إنشاء مقال كامل مع صورة يدوية
Test Article Generation with Manual Image
"""

import asyncio
import os
import sys
import json
from datetime import datetime
from typing import Dict, Optional

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.manual_image_generator import ManualImageGenerator
from modules.content_generator import ContentGenerator
from modules.logger import logger

class ArticleWithManualImageTester:
    """فئة اختبار إنشاء مقال مع صورة يدوية"""
    
    def __init__(self):
        self.manual_generator = ManualImageGenerator("Gaming News Pro")
        self.content_generator = ContentGenerator()
        self.output_dir = "test_results/articles_with_manual_images"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # مقالات اختبار متنوعة
        self.test_game_articles = [
            {
                'title': 'Cyberpunk 2077: Phantom Liberty - مراجعة شاملة للتوسعة الجديدة',
                'game_name': 'Cyberpunk 2077',
                'content_type': 'review',
                'language': 'arabic',
                'expected_theme': 'pc_gaming'
            },
            {
                'title': 'The Legend of Zelda: Tears of the Kingdom - Game of the Year 2024',
                'game_name': 'The Legend of Zelda: Tears of the Kingdom',
                'content_type': 'review',
                'language': 'english',
                'expected_theme': 'nintendo'
            },
            {
                'title': 'Forza Horizon 6 يحصل على تحديث جديد مع سيارات كلاسيكية',
                'game_name': 'Forza Horizon 6',
                'content_type': 'news',
                'language': 'arabic',
                'expected_theme': 'xbox'
            },
            {
                'title': 'God of War Ragnarök: Complete Edition Coming to PC',
                'game_name': 'God of War Ragnarök',
                'content_type': 'news',
                'language': 'english',
                'expected_theme': 'playstation'
            },
            {
                'title': 'PUBG Mobile يضيف خريطة جديدة وأسلحة متطورة',
                'game_name': 'PUBG Mobile',
                'content_type': 'update',
                'language': 'arabic',
                'expected_theme': 'mobile_gaming'
            }
        ]
    
    def generate_detailed_content(self, article_info: Dict) -> Dict:
        """إنشاء محتوى مفصل للمقال"""
        
        if article_info['language'] == 'arabic':
            content_templates = {
                'review': f"""
# {article_info['title']}

## نظرة عامة على اللعبة
تعتبر لعبة {article_info['game_name']} واحدة من أبرز الألعاب التي صدرت مؤخراً، وقد حققت نجاحاً كبيراً بين اللاعبين حول العالم.

## الجرافيك والتصميم البصري
تتميز اللعبة بجرافيك عالي الجودة ورسوميات مذهلة تجعل تجربة اللعب غامرة وممتعة. التفاصيل البصرية دقيقة جداً والألوان زاهية ومتناسقة.

## طريقة اللعب والتحكم
نظام التحكم سهل ومرن، مما يجعل اللعبة مناسبة للمبتدئين والمحترفين على حد سواء. الآليات الأساسية للعبة مصممة بعناية فائقة.

## القصة والشخصيات
تحتوي اللعبة على قصة مشوقة وشخصيات متطورة تجذب اللاعب وتجعله يستمر في اللعب لساعات طويلة.

## التقييم النهائي
نعطي هذه اللعبة تقييم 9/10 لما تقدمه من تجربة لعب استثنائية ومحتوى غني ومتنوع.

## الخلاصة
{article_info['game_name']} لعبة رائعة تستحق التجربة من كل محبي الألعاب. ننصح بشدة بتجربتها.
                """,
                'news': f"""
# {article_info['title']}

## الخبر الجديد
في تطور مثير للاهتمام، أعلنت الشركة المطورة للعبة {article_info['game_name']} عن تحديثات جديدة ومثيرة قادمة للعبة.

## التفاصيل الكاملة
التحديث الجديد سيتضمن ميزات متطورة وإضافات جديدة ستعزز من تجربة اللعب بشكل كبير.

## ردود أفعال اللاعبين
استقبل اللاعبون الخبر بحماس كبير وترقب شديد للتحديث الجديد.

## موعد الإصدار
من المتوقع أن يصدر التحديث خلال الأسابيع القادمة.

## الخلاصة
هذا التحديث سيضيف قيمة كبيرة للعبة ويجعلها أكثر إثارة ومتعة.
                """,
                'update': f"""
# {article_info['title']}

## تفاصيل التحديث
أصدرت الشركة المطورة تحديثاً جديداً للعبة {article_info['game_name']} يتضمن إضافات وتحسينات مهمة.

## الميزات الجديدة
- إضافة محتوى جديد ومثير
- تحسينات في الأداء والاستقرار
- إصلاح الأخطاء المعروفة
- تحسينات في واجهة المستخدم

## كيفية الحصول على التحديث
يمكن للاعبين تحميل التحديث مجاناً من المتجر الرسمي.

## الخلاصة
هذا التحديث يعزز من تجربة اللعب ويضيف قيمة كبيرة للعبة.
                """
            }
        else:  # English
            content_templates = {
                'review': f"""
# {article_info['title']}

## Game Overview
{article_info['game_name']} stands as one of the most remarkable games released recently, achieving tremendous success among players worldwide.

## Graphics and Visual Design
The game features high-quality graphics and stunning visuals that create an immersive and enjoyable gaming experience. Visual details are incredibly precise with vibrant and harmonious colors.

## Gameplay and Controls
The control system is easy and flexible, making the game suitable for both beginners and professionals. The core game mechanics are carefully designed.

## Story and Characters
The game contains an engaging story and well-developed characters that captivate players and keep them playing for hours.

## Final Rating
We give this game a 9/10 rating for its exceptional gaming experience and rich, diverse content.

## Conclusion
{article_info['game_name']} is an excellent game that deserves to be tried by all gaming enthusiasts. We highly recommend experiencing it.
                """,
                'news': f"""
# {article_info['title']}

## Breaking News
In an exciting development, the developers of {article_info['game_name']} have announced new and exciting updates coming to the game.

## Full Details
The new update will include advanced features and new additions that will significantly enhance the gaming experience.

## Player Reactions
Players have received the news with great enthusiasm and anticipation for the new update.

## Release Date
The update is expected to be released in the coming weeks.

## Conclusion
This update will add great value to the game and make it more exciting and enjoyable.
                """,
                'update': f"""
# {article_info['title']}

## Update Details
The developers have released a new update for {article_info['game_name']} that includes important additions and improvements.

## New Features
- Addition of new and exciting content
- Performance and stability improvements
- Bug fixes for known issues
- User interface enhancements

## How to Get the Update
Players can download the update for free from the official store.

## Conclusion
This update enhances the gaming experience and adds significant value to the game.
                """
            }
        
        template = content_templates.get(article_info['content_type'], content_templates['news'])
        
        return {
            'title': article_info['title'],
            'content': template.strip(),
            'game_name': article_info['game_name'],
            'content_type': article_info['content_type'],
            'language': article_info['language'],
            'expected_theme': article_info['expected_theme'],
            'word_count': len(template.split()),
            'creation_date': datetime.now().isoformat()
        }
    
    async def test_single_article_with_manual_image(self, article_info: Dict) -> Dict:
        """اختبار إنشاء مقال واحد مع صورة يدوية"""
        
        logger.info(f"📝 بدء إنشاء مقال: {article_info['title'][:50]}...")
        
        try:
            # 1. إنشاء المحتوى المفصل
            article_content = self.generate_detailed_content(article_info)
            
            # 2. إنشاء الصورة اليدوية
            logger.info("🎨 إنشاء صورة يدوية للمقال...")
            image_result = await self.manual_generator.generate_manual_image(article_content)
            
            if not image_result:
                logger.error("❌ فشل في إنشاء الصورة اليدوية")
                return {
                    'success': False,
                    'error': 'Failed to generate manual image',
                    'article_info': article_info
                }
            
            # 3. دمج المقال مع الصورة
            complete_article = {
                **article_content,
                'image_url': image_result['url'],
                'image_path': image_result['local_path'],
                'image_filename': image_result['filename'],
                'image_metadata': image_result,
                'generation_method': 'manual_image_with_content'
            }
            
            # 4. حفظ المقال الكامل
            article_filename = f"article_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{article_info['game_name'].replace(' ', '_')}.json"
            article_path = os.path.join(self.output_dir, article_filename)
            
            with open(article_path, 'w', encoding='utf-8') as f:
                json.dump(complete_article, f, ensure_ascii=False, indent=2)
            
            # 5. إنشاء ملف HTML للعرض
            html_content = self.generate_html_preview(complete_article)
            html_filename = article_filename.replace('.json', '.html')
            html_path = os.path.join(self.output_dir, html_filename)
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"✅ تم إنشاء المقال بنجاح: {article_filename}")
            logger.info(f"🌐 معاينة HTML: {html_filename}")
            
            return {
                'success': True,
                'article_filename': article_filename,
                'article_path': article_path,
                'html_filename': html_filename,
                'html_path': html_path,
                'image_filename': image_result['filename'],
                'image_theme': image_result.get('theme', 'unknown'),
                'word_count': complete_article['word_count'],
                'language': complete_article['language'],
                'content_type': complete_article['content_type']
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المقال: {e}")
            return {
                'success': False,
                'error': str(e),
                'article_info': article_info
            }
    
    def generate_html_preview(self, article: Dict) -> str:
        """إنشاء معاينة HTML للمقال"""
        
        # تحويل المحتوى من Markdown إلى HTML بسيط
        content_html = article['content'].replace('\n## ', '\n<h2>').replace('\n# ', '\n<h1>')
        content_html = content_html.replace('\n- ', '\n<li>').replace('\n\n', '</p><p>')
        content_html = f"<p>{content_html}</p>"
        
        html_template = f"""
<!DOCTYPE html>
<html lang="{article['language']}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{article['title']}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }}
        .article-container {{
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .article-image {{
            width: 100%;
            max-width: 600px;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }}
        h1 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            margin-top: 30px;
        }}
        .metadata {{
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-size: 0.9em;
        }}
        .tag {{
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-right: 10px;
        }}
    </style>
</head>
<body>
    <div class="article-container">
        <h1>{article['title']}</h1>
        
        <div class="metadata">
            <span class="tag">{article['content_type'].title()}</span>
            <span class="tag">{article['language'].title()}</span>
            <span class="tag">{article['word_count']} كلمة</span>
            <span class="tag">صورة يدوية</span>
        </div>
        
        <img src="{article['image_url']}" alt="{article['title']}" class="article-image">
        
        <div class="content">
            {content_html}
        </div>
        
        <div class="metadata">
            <strong>معلومات الصورة:</strong><br>
            الملف: {article['image_filename']}<br>
            الموضوع: {article['image_metadata'].get('theme', 'غير محدد')}<br>
            طريقة الإنشاء: {article['image_metadata'].get('generation_method', 'غير محدد')}<br>
            تاريخ الإنشاء: {article['creation_date'][:19]}
        </div>
    </div>
</body>
</html>
        """
        
        return html_template

    async def run_comprehensive_article_test(self) -> Dict:
        """تشغيل اختبار شامل لإنشاء المقالات مع الصور اليدوية"""

        logger.info("🚀 بدء اختبار إنشاء المقالات مع الصور اليدوية...")
        logger.info("=" * 60)

        start_time = datetime.now()

        results = {
            'start_time': start_time.isoformat(),
            'total_articles': len(self.test_game_articles),
            'successful_articles': 0,
            'failed_articles': 0,
            'articles_details': []
        }

        for i, article_info in enumerate(self.test_game_articles):
            logger.info(f"📝 معالجة المقال {i+1}/{len(self.test_game_articles)}: {article_info['title'][:40]}...")

            article_result = await self.test_single_article_with_manual_image(article_info)

            if article_result['success']:
                results['successful_articles'] += 1
                logger.info(f"✅ نجح إنشاء المقال: {article_result['article_filename']}")
            else:
                results['failed_articles'] += 1
                logger.error(f"❌ فشل إنشاء المقال: {article_result.get('error', 'Unknown error')}")

            results['articles_details'].append(article_result)

        end_time = datetime.now()
        results['end_time'] = end_time.isoformat()
        results['duration_seconds'] = (end_time - start_time).total_seconds()
        results['success_rate'] = (results['successful_articles'] / results['total_articles']) * 100

        # حفظ النتائج
        results_file = os.path.join(self.output_dir, f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # طباعة الملخص
        logger.info("=" * 60)
        logger.info("📊 ملخص نتائج اختبار إنشاء المقالات:")
        logger.info(f"⏱️ المدة الإجمالية: {results['duration_seconds']:.2f} ثانية")
        logger.info(f"📈 إجمالي المقالات: {results['total_articles']}")
        logger.info(f"✅ المقالات الناجحة: {results['successful_articles']}")
        logger.info(f"❌ المقالات الفاشلة: {results['failed_articles']}")
        logger.info(f"🎯 معدل النجاح: {results['success_rate']:.1f}%")
        logger.info(f"💾 تم حفظ النتائج في: {results_file}")
        logger.info("=" * 60)

        return results

    def print_article_previews(self, results: Dict):
        """طباعة معاينة للمقالات المُنشأة"""
        logger.info("📋 معاينة المقالات المُنشأة:")

        successful_articles = [article for article in results['articles_details'] if article['success']]

        for i, article in enumerate(successful_articles[:3]):  # عرض أول 3 مقالات فقط
            logger.info(f"\n📝 المقال {i+1}:")
            logger.info(f"  📄 الملف: {article['article_filename']}")
            logger.info(f"  🌐 HTML: {article['html_filename']}")
            logger.info(f"  🎨 الصورة: {article['image_filename']}")
            logger.info(f"  🎯 الموضوع: {article['image_theme']}")
            logger.info(f"  📊 عدد الكلمات: {article['word_count']}")
            logger.info(f"  🌍 اللغة: {article['language']}")

        if len(successful_articles) > 3:
            logger.info(f"\n... و {len(successful_articles) - 3} مقال آخر")

async def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🎮 بدء اختبار إنشاء المقالات مع الصور اليدوية")

        tester = ArticleWithManualImageTester()
        results = await tester.run_comprehensive_article_test()

        # طباعة معاينة المقالات
        tester.print_article_previews(results)

        logger.info("🎉 تم الانتهاء من اختبار إنشاء المقالات بنجاح!")

        # فتح أول مقال HTML في المتصفح للمعاينة
        successful_articles = [article for article in results['articles_details'] if article['success']]
        if successful_articles:
            first_html = successful_articles[0]['html_path']
            logger.info(f"🌐 يمكنك فتح الملف التالي في المتصفح للمعاينة: {first_html}")

        return results

    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الاختبار: {e}")
        return None

if __name__ == "__main__":
    # تشغيل الاختبار
    results = asyncio.run(main())
