#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المحسن لوكيل أخبار الألعاب
"""

import asyncio
import time
import sys
import os
from datetime import datetime, timedelta

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الأنظمة المحسنة
from modules.agent_state_manager import agent_state_manager, AgentState
from modules.smart_database_manager import smart_db_manager
from modules.operation_manager import operation_manager, OperationType, OperationPriority
from modules.smart_lifecycle_manager import lifecycle_manager, StartupMode, ShutdownReason
from modules.enhanced_error_handler import enhanced_error_handler, ErrorSeverity, ErrorCategory
from modules.enhanced_web_interface import enhanced_web_interface
from modules.logger import logger

class EnhancedSystemTester:
    """فئة اختبار النظام المحسن"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار النظام المحسن...")
        print("=" * 60)
        
        tests = [
            ("اختبار مدير حالة الوكيل", self.test_agent_state_manager),
            ("اختبار مدير قاعدة البيانات الذكي", self.test_smart_database_manager),
            ("اختبار مدير العمليات", self.test_operation_manager),
            ("اختبار مدير دورة الحياة", self.test_lifecycle_manager),
            ("اختبار معالج الأخطاء المحسن", self.test_enhanced_error_handler),
            ("اختبار الواجهة المحسنة", self.test_enhanced_web_interface),
            ("اختبار الأداء تحت الضغط", self.test_performance_under_load),
            ("اختبار الاستعادة من الأخطاء", self.test_error_recovery)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 {test_name}...")
            try:
                result = await test_func()
                self.test_results[test_name] = {
                    'status': 'نجح' if result else 'فشل',
                    'success': result
                }
                print(f"{'✅' if result else '❌'} {test_name}: {'نجح' if result else 'فشل'}")
            except Exception as e:
                self.test_results[test_name] = {
                    'status': 'خطأ',
                    'success': False,
                    'error': str(e)
                }
                print(f"❌ {test_name}: خطأ - {str(e)}")
        
        self.print_summary()
    
    async def test_agent_state_manager(self):
        """اختبار مدير حالة الوكيل"""
        try:
            # اختبار تعيين الحالة
            agent_state_manager.set_state(AgentState.STARTING, "اختبار البدء")
            current_state = agent_state_manager.get_current_state()
            
            if current_state != AgentState.STARTING:
                return False
            
            # اختبار الحصول على معلومات الحالة
            state_info = agent_state_manager.get_current_state_info()
            
            if not state_info or state_info.state != AgentState.STARTING:
                return False
            
            # اختبار بدء عملية
            operation_id = agent_state_manager.start_operation("test_operation", {"test": True})
            
            if not operation_id:
                return False
            
            # اختبار إكمال العملية
            agent_state_manager.complete_operation(operation_id, True)
            
            # اختبار حفظ الحالة
            agent_state_manager.save_state()
            
            return True
            
        except Exception as e:
            print(f"خطأ في اختبار مدير حالة الوكيل: {e}")
            return False
    
    async def test_smart_database_manager(self):
        """اختبار مدير قاعدة البيانات الذكي"""
        try:
            # اختبار فحص وتهيئة قاعدة البيانات
            db_healthy = smart_db_manager.check_and_initialize_database()
            
            if not db_healthy:
                return False
            
            # اختبار تقرير الصحة
            health_report = smart_db_manager.get_database_health_report()
            
            if not health_report or 'health_score' not in health_report:
                return False
            
            # اختبار إنشاء نسخة احتياطية
            backup_path = smart_db_manager.backup_database("test_backup.db")
            
            if not backup_path or not os.path.exists(backup_path):
                return False
            
            # تنظيف
            if os.path.exists(backup_path):
                os.remove(backup_path)
            
            return True
            
        except Exception as e:
            print(f"خطأ في اختبار مدير قاعدة البيانات: {e}")
            return False
    
    async def test_operation_manager(self):
        """اختبار مدير العمليات"""
        try:
            # بدء مدير العمليات
            await operation_manager.start()
            
            # اختبار إرسال عملية
            def test_function():
                time.sleep(1)
                return "test_result"
            
            task_id = operation_manager.submit_operation(
                operation_type=OperationType.CONTENT_COLLECTION,
                func=test_function,
                priority=OperationPriority.HIGH
            )
            
            if not task_id:
                return False
            
            # انتظار اكتمال العملية
            await asyncio.sleep(3)
            
            # فحص حالة العملية
            operation_status = operation_manager.get_operation_status(task_id)
            
            if not operation_status:
                return False
            
            # اختبار الإحصائيات
            stats = operation_manager.get_statistics()
            
            if not stats or 'total_operations' not in stats:
                return False
            
            # إيقاف مدير العمليات
            await operation_manager.stop()
            
            return True
            
        except Exception as e:
            print(f"خطأ في اختبار مدير العمليات: {e}")
            return False
    
    async def test_lifecycle_manager(self):
        """اختبار مدير دورة الحياة"""
        try:
            # اختبار الحصول على حالة دورة الحياة
            lifecycle_status = lifecycle_manager.get_lifecycle_status()
            
            if not lifecycle_status:
                return False
            
            # اختبار تقرير الصحة
            health_report = lifecycle_manager.get_health_report()
            
            if not health_report or 'health_score' not in health_report:
                return False
            
            # اختبار البدء الذكي (وضع آمن)
            # ملاحظة: لا نختبر البدء الفعلي لتجنب التداخل مع النظام الحالي
            
            return True
            
        except Exception as e:
            print(f"خطأ في اختبار مدير دورة الحياة: {e}")
            return False
    
    async def test_enhanced_error_handler(self):
        """اختبار معالج الأخطاء المحسن"""
        try:
            # اختبار معالجة خطأ
            test_error = Exception("خطأ اختبار")
            
            error_info = enhanced_error_handler.handle_error(
                error=test_error,
                source_component="test_component",
                context={"test": True},
                severity=ErrorSeverity.LOW
            )
            
            if not error_info or not error_info.error_id:
                return False
            
            # اختبار الإحصائيات
            stats = enhanced_error_handler.get_error_statistics(timedelta(hours=1))
            
            if not stats or 'total_errors' not in stats:
                return False
            
            return True
            
        except Exception as e:
            print(f"خطأ في اختبار معالج الأخطاء: {e}")
            return False
    
    async def test_enhanced_web_interface(self):
        """اختبار الواجهة المحسنة"""
        try:
            # بدء الخادم
            enhanced_web_interface.start_server(host='localhost', port=5001, debug=False)
            
            # انتظار قصير للتأكد من بدء الخادم
            await asyncio.sleep(2)
            
            # فحص ما إذا كان الخادم يعمل
            if not enhanced_web_interface.is_running:
                return False
            
            # إيقاف الخادم
            enhanced_web_interface.stop_server()
            
            return True
            
        except Exception as e:
            print(f"خطأ في اختبار الواجهة المحسنة: {e}")
            return False
    
    async def test_performance_under_load(self):
        """اختبار الأداء تحت الضغط"""
        try:
            print("   📊 اختبار الأداء تحت الضغط...")
            
            # بدء مدير العمليات
            await operation_manager.start()
            
            # إرسال عدة عمليات متزامنة
            task_ids = []
            
            def load_test_function(task_num):
                time.sleep(0.5)  # محاكاة عمل
                return f"result_{task_num}"
            
            for i in range(10):
                task_id = operation_manager.submit_operation(
                    operation_type=OperationType.CONTENT_PROCESSING,
                    func=load_test_function,
                    args=(i,),
                    priority=OperationPriority.NORMAL
                )
                task_ids.append(task_id)
            
            # انتظار اكتمال العمليات
            await asyncio.sleep(10)
            
            # فحص النتائج
            completed_count = 0
            for task_id in task_ids:
                status = operation_manager.get_operation_status(task_id)
                if status and status['state'] == 'completed':
                    completed_count += 1
            
            # إيقاف مدير العمليات
            await operation_manager.stop()
            
            # نجح الاختبار إذا اكتملت معظم العمليات
            success_rate = completed_count / len(task_ids)
            print(f"   📈 معدل النجاح: {success_rate:.1%} ({completed_count}/{len(task_ids)})")
            
            return success_rate >= 0.8  # 80% نجاح على الأقل
            
        except Exception as e:
            print(f"خطأ في اختبار الأداء: {e}")
            return False
    
    async def test_error_recovery(self):
        """اختبار الاستعادة من الأخطاء"""
        try:
            print("   🔄 اختبار الاستعادة من الأخطاء...")
            
            # محاكاة أخطاء مختلفة
            errors = [
                (Exception("Network timeout"), ErrorCategory.NETWORK),
                (Exception("Database locked"), ErrorCategory.DATABASE),
                (Exception("API rate limit"), ErrorCategory.API),
                (Exception("Memory error"), ErrorCategory.MEMORY)
            ]
            
            recovery_count = 0
            
            for error, category in errors:
                error_info = enhanced_error_handler.handle_error(
                    error=error,
                    source_component="test_recovery",
                    severity=ErrorSeverity.MEDIUM
                )
                
                # انتظار محاولة الاستعادة
                await asyncio.sleep(2)
                
                # فحص ما إذا تم حل الخطأ
                if error_info.resolved:
                    recovery_count += 1
            
            recovery_rate = recovery_count / len(errors)
            print(f"   🔧 معدل الاستعادة: {recovery_rate:.1%} ({recovery_count}/{len(errors)})")
            
            return recovery_rate >= 0.5  # 50% استعادة على الأقل
            
        except Exception as e:
            print(f"خطأ في اختبار الاستعادة: {e}")
            return False
    
    def print_summary(self):
        """طباعة ملخص النتائج"""
        print("\n" + "=" * 60)
        print("📋 ملخص نتائج الاختبار")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 إجمالي الاختبارات: {total_tests}")
        print(f"✅ نجح: {passed_tests}")
        print(f"❌ فشل: {failed_tests}")
        print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        duration = (datetime.now() - self.start_time).total_seconds()
        print(f"⏱️ مدة الاختبار: {duration:.1f} ثانية")
        
        print("\n📝 تفاصيل النتائج:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['success'] else "❌"
            print(f"  {status_icon} {test_name}: {result['status']}")
            if 'error' in result:
                print(f"     خطأ: {result['error']}")
        
        print("\n" + "=" * 60)
        
        if passed_tests == total_tests:
            print("🎉 جميع الاختبارات نجحت! النظام المحسن جاهز للاستخدام.")
        elif passed_tests >= total_tests * 0.8:
            print("⚠️ معظم الاختبارات نجحت. النظام جاهز مع بعض التحسينات المطلوبة.")
        else:
            print("❌ عدة اختبارات فشلت. يحتاج النظام لمراجعة وإصلاح.")

async def main():
    """الدالة الرئيسية للاختبار"""
    tester = EnhancedSystemTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
