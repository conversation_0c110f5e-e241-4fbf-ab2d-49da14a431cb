# نظام إدارة الأخطاء المحسن والذكي
import asyncio
import traceback
import threading
import time
import json
import smtplib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum
from dataclasses import dataclass, asdict
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from .logger import logger
from .database import db

class ErrorSeverity(Enum):
    """مستويات خطورة الأخطاء"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """فئات الأخطاء"""
    NETWORK = "network"
    DATABASE = "database"
    API = "api"
    MEMORY = "memory"
    DISK = "disk"
    AUTHENTICATION = "authentication"
    PERMISSION = "permission"
    CONFIGURATION = "configuration"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_SERVICE = "external_service"
    UNKNOWN = "unknown"

class RecoveryStrategy(Enum):
    """استراتيجيات الاستعادة"""
    RETRY = "retry"
    FALLBACK = "fallback"
    RESTART_COMPONENT = "restart_component"
    RESTART_SYSTEM = "restart_system"
    MANUAL_INTERVENTION = "manual_intervention"
    IGNORE = "ignore"

@dataclass
class ErrorInfo:
    """معلومات الخطأ"""
    error_id: str
    timestamp: datetime
    error_type: str
    error_message: str
    stack_trace: str
    severity: ErrorSeverity
    category: ErrorCategory
    source_component: str
    context: Dict[str, Any]
    recovery_strategy: RecoveryStrategy
    recovery_attempts: int
    resolved: bool
    resolution_notes: Optional[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['severity'] = self.severity.value
        data['category'] = self.category.value
        data['recovery_strategy'] = self.recovery_strategy.value
        return data

class ErrorPattern:
    """نمط الخطأ للكشف عن الأخطاء المتكررة"""
    
    def __init__(self, pattern_id: str, description: str, conditions: Dict[str, Any]):
        self.pattern_id = pattern_id
        self.description = description
        self.conditions = conditions
        self.occurrences = []
        self.last_occurrence = None
    
    def matches(self, error_info: ErrorInfo) -> bool:
        """فحص ما إذا كان الخطأ يطابق النمط"""
        for key, expected_value in self.conditions.items():
            if key == 'error_type':
                if error_info.error_type != expected_value:
                    return False
            elif key == 'category':
                if error_info.category != expected_value:
                    return False
            elif key == 'severity':
                if error_info.severity != expected_value:
                    return False
            elif key == 'message_contains':
                if expected_value.lower() not in error_info.error_message.lower():
                    return False
        
        return True
    
    def add_occurrence(self, error_info: ErrorInfo):
        """إضافة حدوث للنمط"""
        self.occurrences.append(error_info)
        self.last_occurrence = error_info.timestamp
        
        # الاحتفاظ بآخر 100 حدوث فقط
        if len(self.occurrences) > 100:
            self.occurrences = self.occurrences[-100:]
    
    def get_frequency(self, time_window: timedelta = timedelta(hours=1)) -> int:
        """الحصول على تكرار النمط في فترة زمنية"""
        cutoff_time = datetime.now() - time_window
        return len([occ for occ in self.occurrences if occ.timestamp > cutoff_time])

class EnhancedErrorHandler:
    """معالج الأخطاء المحسن والذكي"""
    
    def __init__(self):
        self._error_history: List[ErrorInfo] = []
        self._error_patterns: List[ErrorPattern] = []
        self._recovery_strategies: Dict[str, Callable] = {}
        self._notification_handlers: List[Callable] = []
        self._lock = threading.Lock()
        
        # إعدادات الإشعارات
        self.notification_settings = {
            'email_enabled': False,
            'email_smtp_server': '',
            'email_smtp_port': 587,
            'email_username': '',
            'email_password': '',
            'email_recipients': [],
            'critical_error_threshold': 3,  # عدد الأخطاء الحرجة قبل الإشعار
            'notification_cooldown': 300  # 5 دقائق بين الإشعارات
        }
        
        self._last_notification_time = {}
        
        # تسجيل أنماط الأخطاء الشائعة
        self._register_common_error_patterns()
        
        # تسجيل استراتيجيات الاستعادة
        self._register_recovery_strategies()
    
    def _register_common_error_patterns(self):
        """تسجيل أنماط الأخطاء الشائعة"""
        
        # نمط أخطاء الشبكة
        self._error_patterns.append(ErrorPattern(
            pattern_id="network_timeout",
            description="انتهاء مهلة الاتصال بالشبكة",
            conditions={
                'category': ErrorCategory.NETWORK,
                'message_contains': 'timeout'
            }
        ))
        
        # نمط أخطاء قاعدة البيانات
        self._error_patterns.append(ErrorPattern(
            pattern_id="database_lock",
            description="قفل قاعدة البيانات",
            conditions={
                'category': ErrorCategory.DATABASE,
                'message_contains': 'database is locked'
            }
        ))
        
        # نمط أخطاء API
        self._error_patterns.append(ErrorPattern(
            pattern_id="api_rate_limit",
            description="تجاوز حد استخدام API",
            conditions={
                'category': ErrorCategory.API,
                'message_contains': 'rate limit'
            }
        ))
        
        # نمط أخطاء الذاكرة
        self._error_patterns.append(ErrorPattern(
            pattern_id="memory_exhaustion",
            description="نفاد الذاكرة",
            conditions={
                'category': ErrorCategory.MEMORY,
                'message_contains': 'memory'
            }
        ))
    
    def _register_recovery_strategies(self):
        """تسجيل استراتيجيات الاستعادة"""
        
        self._recovery_strategies['retry_with_backoff'] = self._retry_with_backoff
        self._recovery_strategies['restart_component'] = self._restart_component
        self._recovery_strategies['clear_cache'] = self._clear_cache
        self._recovery_strategies['wait_and_retry'] = self._wait_and_retry
        self._recovery_strategies['fallback_method'] = self._fallback_method
    
    def handle_error(
        self,
        error: Exception,
        source_component: str,
        context: Dict[str, Any] = None,
        severity: Optional[ErrorSeverity] = None
    ) -> ErrorInfo:
        """معالجة خطأ جديد"""
        
        # تحليل الخطأ
        error_info = self._analyze_error(error, source_component, context, severity)
        
        # حفظ في التاريخ
        with self._lock:
            self._error_history.append(error_info)
            
            # الاحتفاظ بآخر 1000 خطأ فقط
            if len(self._error_history) > 1000:
                self._error_history = self._error_history[-1000:]
        
        # فحص الأنماط
        self._check_error_patterns(error_info)
        
        # حفظ في قاعدة البيانات
        self._save_error_to_database(error_info)
        
        # تسجيل الخطأ
        self._log_error(error_info)
        
        # محاولة الاستعادة
        asyncio.create_task(self._attempt_recovery(error_info))
        
        # إرسال إشعارات إذا لزم الأمر
        self._send_notifications(error_info)
        
        return error_info
    
    def _analyze_error(
        self,
        error: Exception,
        source_component: str,
        context: Dict[str, Any] = None,
        severity: Optional[ErrorSeverity] = None
    ) -> ErrorInfo:
        """تحليل الخطأ وتصنيفه"""
        
        error_type = type(error).__name__
        error_message = str(error)
        stack_trace = traceback.format_exc()
        
        # تحديد الفئة
        category = self._categorize_error(error, error_message)
        
        # تحديد مستوى الخطورة
        if severity is None:
            severity = self._determine_severity(error, category, error_message)
        
        # تحديد استراتيجية الاستعادة
        recovery_strategy = self._determine_recovery_strategy(category, error_type, error_message)
        
        # إنشاء معرف فريد للخطأ
        error_id = f"{source_component}_{error_type}_{int(time.time() * 1000)}"
        
        return ErrorInfo(
            error_id=error_id,
            timestamp=datetime.now(),
            error_type=error_type,
            error_message=error_message,
            stack_trace=stack_trace,
            severity=severity,
            category=category,
            source_component=source_component,
            context=context or {},
            recovery_strategy=recovery_strategy,
            recovery_attempts=0,
            resolved=False,
            resolution_notes=None
        )
    
    def _categorize_error(self, error: Exception, error_message: str) -> ErrorCategory:
        """تصنيف الخطأ"""
        error_message_lower = error_message.lower()
        error_type = type(error).__name__.lower()
        
        # فحص أخطاء الشبكة
        if any(keyword in error_message_lower for keyword in ['connection', 'timeout', 'network', 'socket']):
            return ErrorCategory.NETWORK
        
        # فحص أخطاء قاعدة البيانات
        if any(keyword in error_message_lower for keyword in ['database', 'sqlite', 'sql', 'db']):
            return ErrorCategory.DATABASE
        
        # فحص أخطاء API
        if any(keyword in error_message_lower for keyword in ['api', 'http', 'request', 'response']):
            return ErrorCategory.API
        
        # فحص أخطاء الذاكرة
        if any(keyword in error_message_lower for keyword in ['memory', 'ram', 'allocation']):
            return ErrorCategory.MEMORY
        
        # فحص أخطاء القرص
        if any(keyword in error_message_lower for keyword in ['disk', 'file', 'directory', 'space']):
            return ErrorCategory.DISK
        
        # فحص أخطاء المصادقة
        if any(keyword in error_message_lower for keyword in ['auth', 'login', 'credential', 'token']):
            return ErrorCategory.AUTHENTICATION
        
        # فحص أخطاء الصلاحيات
        if any(keyword in error_message_lower for keyword in ['permission', 'access', 'forbidden', 'unauthorized']):
            return ErrorCategory.PERMISSION
        
        # فحص أخطاء التكوين
        if any(keyword in error_message_lower for keyword in ['config', 'setting', 'environment']):
            return ErrorCategory.CONFIGURATION
        
        return ErrorCategory.UNKNOWN

    def _determine_severity(self, error: Exception, category: ErrorCategory, error_message: str) -> ErrorSeverity:
        """تحديد مستوى خطورة الخطأ"""
        error_message_lower = error_message.lower()

        # أخطاء حرجة
        if any(keyword in error_message_lower for keyword in ['critical', 'fatal', 'crash', 'shutdown']):
            return ErrorSeverity.CRITICAL

        # أخطاء عالية الخطورة
        if category in [ErrorCategory.DATABASE, ErrorCategory.MEMORY]:
            return ErrorSeverity.HIGH

        if any(keyword in error_message_lower for keyword in ['failed', 'error', 'exception']):
            return ErrorSeverity.HIGH

        # أخطاء متوسطة الخطورة
        if category in [ErrorCategory.API, ErrorCategory.NETWORK]:
            return ErrorSeverity.MEDIUM

        # أخطاء منخفضة الخطورة
        return ErrorSeverity.LOW

    def _determine_recovery_strategy(self, category: ErrorCategory, error_type: str, error_message: str) -> RecoveryStrategy:
        """تحديد استراتيجية الاستعادة"""
        error_message_lower = error_message.lower()

        # استراتيجيات حسب الفئة
        if category == ErrorCategory.NETWORK:
            if 'timeout' in error_message_lower:
                return RecoveryStrategy.RETRY
            return RecoveryStrategy.FALLBACK

        elif category == ErrorCategory.DATABASE:
            if 'locked' in error_message_lower:
                return RecoveryStrategy.RETRY
            return RecoveryStrategy.RESTART_COMPONENT

        elif category == ErrorCategory.API:
            if 'rate limit' in error_message_lower:
                return RecoveryStrategy.RETRY
            return RecoveryStrategy.FALLBACK

        elif category == ErrorCategory.MEMORY:
            return RecoveryStrategy.RESTART_COMPONENT

        elif category == ErrorCategory.AUTHENTICATION:
            return RecoveryStrategy.MANUAL_INTERVENTION

        return RecoveryStrategy.RETRY

    def _check_error_patterns(self, error_info: ErrorInfo):
        """فحص أنماط الأخطاء"""
        for pattern in self._error_patterns:
            if pattern.matches(error_info):
                pattern.add_occurrence(error_info)

                # فحص التكرار
                frequency = pattern.get_frequency(timedelta(minutes=30))
                if frequency >= 5:  # 5 أخطاء في 30 دقيقة
                    logger.warning(f"🔄 نمط خطأ متكرر: {pattern.description} ({frequency} مرات في 30 دقيقة)")

                    # تصعيد مستوى الخطورة
                    if error_info.severity != ErrorSeverity.CRITICAL:
                        error_info.severity = ErrorSeverity.HIGH

    def _save_error_to_database(self, error_info: ErrorInfo):
        """حفظ الخطأ في قاعدة البيانات"""
        try:
            db.log_error(
                error_type=error_info.error_type,
                error_message=error_info.error_message,
                stack_trace=error_info.stack_trace,
                source_url=error_info.context.get('source_url'),
                severity=error_info.severity.value,
                category=error_info.category.value,
                context=json.dumps(error_info.context)
            )
        except Exception as e:
            logger.error(f"❌ فشل في حفظ الخطأ في قاعدة البيانات: {e}")

    def _log_error(self, error_info: ErrorInfo):
        """تسجيل الخطأ في السجل"""
        severity_emoji = {
            ErrorSeverity.LOW: "ℹ️",
            ErrorSeverity.MEDIUM: "⚠️",
            ErrorSeverity.HIGH: "❌",
            ErrorSeverity.CRITICAL: "🚨"
        }

        emoji = severity_emoji.get(error_info.severity, "❓")

        log_message = (
            f"{emoji} خطأ {error_info.severity.value}: {error_info.error_type} "
            f"في {error_info.source_component} - {error_info.error_message}"
        )

        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)

    async def _attempt_recovery(self, error_info: ErrorInfo):
        """محاولة الاستعادة من الخطأ"""
        strategy_name = error_info.recovery_strategy.value

        if strategy_name in self._recovery_strategies:
            try:
                logger.info(f"🔄 محاولة الاستعادة باستخدام: {strategy_name}")

                recovery_func = self._recovery_strategies[strategy_name]
                success = await recovery_func(error_info)

                error_info.recovery_attempts += 1

                if success:
                    error_info.resolved = True
                    error_info.resolution_notes = f"تم الحل باستخدام {strategy_name}"
                    logger.info(f"✅ تم حل الخطأ {error_info.error_id} باستخدام {strategy_name}")
                else:
                    logger.warning(f"⚠️ فشلت محاولة الاستعادة {strategy_name} للخطأ {error_info.error_id}")

            except Exception as e:
                logger.error(f"❌ خطأ في محاولة الاستعادة: {e}")

    async def _retry_with_backoff(self, error_info: ErrorInfo) -> bool:
        """إعادة المحاولة مع تأخير متزايد"""
        max_retries = 3
        base_delay = 1

        for attempt in range(max_retries):
            delay = base_delay * (2 ** attempt)
            await asyncio.sleep(delay)

            logger.info(f"🔄 إعادة المحاولة {attempt + 1}/{max_retries} بعد {delay} ثانية")

            # هنا يمكن إضافة منطق إعادة تنفيذ العملية الفاشلة
            # حالياً نعتبر أن إعادة المحاولة نجحت بنسبة 70%
            if attempt >= 1:  # نجح بعد المحاولة الثانية
                return True

        return False

    async def _restart_component(self, error_info: ErrorInfo) -> bool:
        """إعادة تشغيل المكون"""
        component = error_info.source_component
        logger.info(f"🔄 إعادة تشغيل المكون: {component}")

        # هنا يمكن إضافة منطق إعادة تشغيل المكونات المختلفة
        await asyncio.sleep(2)  # محاكاة إعادة التشغيل

        return True

    async def _clear_cache(self, error_info: ErrorInfo) -> bool:
        """تنظيف الكاش"""
        logger.info("🧹 تنظيف الكاش...")

        # هنا يمكن إضافة منطق تنظيف الكاش
        await asyncio.sleep(1)

        return True

    async def _wait_and_retry(self, error_info: ErrorInfo) -> bool:
        """انتظار وإعادة المحاولة"""
        wait_time = 30  # 30 ثانية
        logger.info(f"⏳ انتظار {wait_time} ثانية قبل إعادة المحاولة...")

        await asyncio.sleep(wait_time)

        return True

    async def _fallback_method(self, error_info: ErrorInfo) -> bool:
        """استخدام طريقة بديلة"""
        logger.info("🔄 التحويل للطريقة البديلة...")

        # هنا يمكن إضافة منطق الطرق البديلة
        await asyncio.sleep(1)

        return True

    def _send_notifications(self, error_info: ErrorInfo):
        """إرسال إشعارات الأخطاء"""
        # فحص ما إذا كان الخطأ يستحق الإشعار
        if not self._should_notify(error_info):
            return

        # إرسال إشعار بريد إلكتروني
        if self.notification_settings['email_enabled']:
            self._send_email_notification(error_info)

        # تشغيل معالجات الإشعارات المخصصة
        for handler in self._notification_handlers:
            try:
                handler(error_info)
            except Exception as e:
                logger.error(f"❌ فشل في معالج الإشعارات: {e}")

    def _should_notify(self, error_info: ErrorInfo) -> bool:
        """فحص ما إذا كان يجب إرسال إشعار"""
        # إشعار فوري للأخطاء الحرجة
        if error_info.severity == ErrorSeverity.CRITICAL:
            return True

        # فحص cooldown للإشعارات
        notification_key = f"{error_info.category.value}_{error_info.severity.value}"
        last_notification = self._last_notification_time.get(notification_key, 0)

        if time.time() - last_notification < self.notification_settings['notification_cooldown']:
            return False

        # فحص عتبة الأخطاء الحرجة
        if error_info.severity == ErrorSeverity.HIGH:
            recent_errors = [
                err for err in self._error_history[-10:]
                if err.severity == ErrorSeverity.HIGH and
                (datetime.now() - err.timestamp).total_seconds() < 300  # آخر 5 دقائق
            ]

            if len(recent_errors) >= self.notification_settings['critical_error_threshold']:
                return True

        return False

    def _send_email_notification(self, error_info: ErrorInfo):
        """إرسال إشعار بريد إلكتروني"""
        try:
            settings = self.notification_settings

            msg = MIMEMultipart()
            msg['From'] = settings['email_username']
            msg['To'] = ', '.join(settings['email_recipients'])
            msg['Subject'] = f"تنبيه خطأ {error_info.severity.value}: {error_info.error_type}"

            body = f"""
            تم اكتشاف خطأ في النظام:

            معرف الخطأ: {error_info.error_id}
            الوقت: {error_info.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
            نوع الخطأ: {error_info.error_type}
            مستوى الخطورة: {error_info.severity.value}
            الفئة: {error_info.category.value}
            المكون: {error_info.source_component}

            رسالة الخطأ:
            {error_info.error_message}

            استراتيجية الاستعادة: {error_info.recovery_strategy.value}
            محاولات الاستعادة: {error_info.recovery_attempts}

            تفاصيل إضافية:
            {json.dumps(error_info.context, indent=2, ensure_ascii=False)}
            """

            msg.attach(MIMEText(body, 'plain', 'utf-8'))

            server = smtplib.SMTP(settings['email_smtp_server'], settings['email_smtp_port'])
            server.starttls()
            server.login(settings['email_username'], settings['email_password'])
            server.send_message(msg)
            server.quit()

            # تحديث وقت آخر إشعار
            notification_key = f"{error_info.category.value}_{error_info.severity.value}"
            self._last_notification_time[notification_key] = time.time()

            logger.info(f"📧 تم إرسال إشعار بريد إلكتروني للخطأ {error_info.error_id}")

        except Exception as e:
            logger.error(f"❌ فشل في إرسال إشعار البريد الإلكتروني: {e}")

    def get_error_statistics(self, time_window: timedelta = timedelta(hours=24)) -> Dict[str, Any]:
        """الحصول على إحصائيات الأخطاء"""
        cutoff_time = datetime.now() - time_window
        recent_errors = [err for err in self._error_history if err.timestamp > cutoff_time]

        # إحصائيات حسب الخطورة
        severity_stats = {}
        for severity in ErrorSeverity:
            severity_stats[severity.value] = len([err for err in recent_errors if err.severity == severity])

        # إحصائيات حسب الفئة
        category_stats = {}
        for category in ErrorCategory:
            category_stats[category.value] = len([err for err in recent_errors if err.category == category])

        # إحصائيات حسب المكون
        component_stats = {}
        for error in recent_errors:
            component = error.source_component
            component_stats[component] = component_stats.get(component, 0) + 1

        # معدل الحل
        resolved_errors = len([err for err in recent_errors if err.resolved])
        resolution_rate = (resolved_errors / len(recent_errors)) * 100 if recent_errors else 0

        return {
            'time_window_hours': time_window.total_seconds() / 3600,
            'total_errors': len(recent_errors),
            'resolved_errors': resolved_errors,
            'resolution_rate_percent': resolution_rate,
            'severity_breakdown': severity_stats,
            'category_breakdown': category_stats,
            'component_breakdown': component_stats,
            'most_common_error': max(component_stats.items(), key=lambda x: x[1]) if component_stats else None
        }

# إنشاء مثيل معالج الأخطاء المحسن
enhanced_error_handler = EnhancedErrorHandler()
