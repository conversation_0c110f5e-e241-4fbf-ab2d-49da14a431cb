#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة اختبار Gemini 2.5 Pro API مع البحث العميق على الويب
تشغيل مستقل لاختبار قدرات Gemini في البحث على الويب
"""

import asyncio
import sys
import os
import json
import argparse
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.gemini_search_tester import GeminiSearchTester, quick_gemini_test
from modules.logger import logger

class GeminiTestRunner:
    """مشغل اختبارات Gemini"""
    
    def __init__(self):
        self.tester = None
        
    async def run_interactive_test(self):
        """تشغيل اختبار تفاعلي"""
        print("\n" + "="*60)
        print("🧪 أداة اختبار Gemini 2.5 Pro API - البحث العميق")
        print("="*60)
        
        # طلب API Key
        api_key = self._get_api_key()
        if not api_key:
            print("❌ لا يمكن المتابعة بدون API Key")
            return
        
        self.tester = GeminiSearchTester(api_key)
        
        while True:
            print("\n📋 الخيارات المتاحة:")
            print("  1. اختبار صحة API Key")
            print("  2. اختبار توفر النماذج")
            print("  3. اختبار البحث العميق على الويب")
            print("  4. اختبار شامل")
            print("  5. اختبار مخصص")
            print("  6. عرض الإحصائيات")
            print("  0. خروج")
            
            try:
                choice = input("\n🔍 اختر رقم الخيار: ").strip()
                
                if choice == "0":
                    print("👋 شكراً لاستخدام أداة اختبار Gemini!")
                    break
                elif choice == "1":
                    await self._test_api_key()
                elif choice == "2":
                    await self._test_models()
                elif choice == "3":
                    await self._test_web_search()
                elif choice == "4":
                    await self._run_comprehensive_test()
                elif choice == "5":
                    await self._custom_test()
                elif choice == "6":
                    self._show_statistics()
                else:
                    print("❌ خيار غير صحيح، حاول مرة أخرى")
                    
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف الاختبار")
                break
            except Exception as e:
                print(f"❌ حدث خطأ: {e}")
    
    def _get_api_key(self) -> str:
        """الحصول على API Key"""
        # محاولة الحصول من متغيرات البيئة
        api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
        
        if api_key:
            print(f"✅ تم العثور على API Key في متغيرات البيئة: {api_key[:10]}...")
            return api_key
        
        # طلب إدخال يدوي
        print("🔑 لم يتم العثور على API Key في متغيرات البيئة")
        print("يمكنك الحصول على API Key مجاناً من: https://aistudio.google.com/app/apikey")
        
        api_key = input("أدخل Gemini API Key: ").strip()
        
        if api_key:
            # حفظ في متغير البيئة للجلسة الحالية
            os.environ['GEMINI_API_KEY'] = api_key
            return api_key
        
        return None
    
    async def _test_api_key(self):
        """اختبار صحة API Key"""
        print("\n🔑 اختبار صحة API Key...")
        print("-" * 40)
        
        result = await self.tester.test_api_key_validity()
        
        if result.get('valid'):
            print("✅ API Key صحيح وفعال")
            print(f"📝 النموذج المختبر: {result.get('model', 'غير معروف')}")
            if result.get('response_preview'):
                print(f"💬 معاينة الاستجابة: {result['response_preview']}")
        else:
            print("❌ API Key غير صحيح")
            print(f"🔍 السبب: {result.get('error', 'غير معروف')}")
            if result.get('suggestion'):
                print(f"💡 اقتراح: {result['suggestion']}")
    
    async def _test_models(self):
        """اختبار توفر النماذج"""
        print("\n🤖 اختبار توفر النماذج...")
        print("-" * 40)
        
        results = await self.tester.test_model_availability()
        
        for model_name, model_result in results.items():
            if model_result.get('available'):
                print(f"✅ {model_name}: متوفر")
                print(f"   ⏱️ وقت الاستجابة: {model_result.get('response_time', 0):.2f}ث")
                print(f"   💬 معاينة: {model_result.get('response_preview', '')[:50]}...")
            else:
                print(f"❌ {model_name}: غير متوفر")
                print(f"   🔍 السبب: {model_result.get('error', 'غير معروف')}")
            print()
    
    async def _test_web_search(self):
        """اختبار البحث العميق على الويب"""
        print("\n🌐 اختبار البحث العميق على الويب...")
        print("-" * 40)
        
        model = input("أدخل اسم النموذج (افتراضي: gemini-2.5-pro): ").strip()
        if not model:
            model = 'gemini-2.5-pro'
        
        print(f"🔍 اختبار البحث باستخدام {model}...")
        
        results = await self.tester.test_web_search_capability(model)
        
        web_search_detected = False
        
        for result in results:
            test_name = result.metadata.get('test_name', 'اختبار غير معروف')
            print(f"\n📋 {test_name}:")
            
            if result.success:
                print("✅ الاختبار نجح")
                print(f"⏱️ وقت التنفيذ: {result.execution_time:.2f}ث")
                
                if result.has_web_search:
                    print("🌐 تم اكتشاف قدرة البحث على الويب!")
                    web_search_detected = True
                else:
                    print("❓ لم يتم اكتشاف بحث ويب واضح")
                
                print(f"💬 معاينة الاستجابة:")
                preview = result.response_text[:300] + "..." if len(result.response_text) > 300 else result.response_text
                print(f"   {preview}")
                
            else:
                print("❌ الاختبار فشل")
                print(f"🔍 السبب: {result.error_message}")
        
        print(f"\n{'='*40}")
        if web_search_detected:
            print("🎉 النتيجة: Gemini يدعم البحث على الويب!")
        else:
            print("😕 النتيجة: لم يتم اكتشاف قدرة بحث ويب واضحة")
    
    async def _run_comprehensive_test(self):
        """تشغيل اختبار شامل"""
        print("\n🚀 تشغيل الاختبار الشامل...")
        print("-" * 40)
        
        results = await self.tester.run_comprehensive_test()
        
        # عرض النتائج
        print("\n📊 نتائج الاختبار الشامل:")
        print("=" * 50)
        
        # اختبار API Key
        api_test = results.get('api_key_test', {})
        if api_test.get('valid'):
            print("✅ API Key: صحيح")
        else:
            print(f"❌ API Key: {api_test.get('error', 'غير صحيح')}")
        
        # توفر النماذج
        models = results.get('model_availability', {})
        available_models = [name for name, data in models.items() if data.get('available')]
        print(f"🤖 النماذج المتوفرة: {len(available_models)}/{len(models)}")
        for model in available_models:
            print(f"   ✅ {model}")
        
        # البحث على الويب
        web_tests = results.get('web_search_tests', [])
        successful_web_tests = [t for t in web_tests if t.get('success')]
        web_search_tests = [t for t in web_tests if t.get('has_web_search')]
        
        print(f"🌐 اختبارات البحث: {len(successful_web_tests)}/{len(web_tests)} نجحت")
        print(f"🔍 البحث على الويب: {'✅ مكتشف' if len(web_search_tests) > 0 else '❌ غير مكتشف'}")
        
        # الملخص
        summary = results.get('summary', {})
        print(f"\n📈 الملخص العام:")
        print(f"   الحالة: {summary.get('overall_status', 'غير معروف')}")
        print(f"   معدل النجاح: {summary.get('success_rate', '0%')}")
        print(f"   متوسط وقت الاستجابة: {summary.get('average_response_time', '0s')}")
        
        # التوصيات
        recommendations = results.get('recommendations', [])
        if recommendations:
            print(f"\n💡 التوصيات:")
            for rec in recommendations:
                print(f"   {rec}")
    
    async def _custom_test(self):
        """اختبار مخصص"""
        print("\n🎯 اختبار مخصص")
        print("-" * 40)
        
        model = input("اسم النموذج (افتراضي: gemini-2.5-pro): ").strip()
        if not model:
            model = 'gemini-2.5-pro'
        
        query = input("أدخل استعلام البحث: ").strip()
        if not query:
            print("❌ يجب إدخال استعلام")
            return
        
        print(f"\n🔍 تنفيذ البحث المخصص...")
        
        # إنشاء اختبار مخصص
        custom_test = {
            'name': 'Custom Test',
            'query': query,
            'expected_keywords': query.lower().split()
        }
        
        # حفظ الاختبار المؤقت
        original_tests = self.tester.web_search_tests
        self.tester.web_search_tests = [custom_test]
        
        try:
            results = await self.tester.test_web_search_capability(model)
            
            if results:
                result = results[0]
                print(f"\n📋 نتيجة الاختبار المخصص:")
                
                if result.success:
                    print("✅ الاختبار نجح")
                    print(f"⏱️ وقت التنفيذ: {result.execution_time:.2f}ث")
                    print(f"🌐 البحث على الويب: {'✅ مكتشف' if result.has_web_search else '❓ غير واضح'}")
                    print(f"\n💬 الاستجابة الكاملة:")
                    print("-" * 40)
                    print(result.response_text)
                else:
                    print("❌ الاختبار فشل")
                    print(f"🔍 السبب: {result.error_message}")
        
        finally:
            # استعادة الاختبارات الأصلية
            self.tester.web_search_tests = original_tests
    
    def _show_statistics(self):
        """عرض الإحصائيات"""
        if not self.tester:
            print("❌ لم يتم تشغيل أي اختبارات بعد")
            return
        
        stats = self.tester.get_test_statistics()
        
        print("\n📊 إحصائيات الاختبار:")
        print("-" * 40)
        print(f"إجمالي الاختبارات: {stats['total_tests']}")
        print(f"الاختبارات الناجحة: {stats['successful_tests']}")
        print(f"الاختبارات الفاشلة: {stats['failed_tests']}")
        print(f"البحث على الويب متوفر: {'✅ نعم' if stats['web_search_available'] else '❌ لا'}")
        print(f"النماذج المختبرة: {', '.join(stats['models_tested']) if stats['models_tested'] else 'لا يوجد'}")
        print(f"متوسط وقت الاستجابة: {stats['average_response_time']:.2f}ث")

async def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description='أداة اختبار Gemini 2.5 Pro API')
    parser.add_argument('--api-key', type=str, help='Gemini API Key')
    parser.add_argument('--quick', action='store_true', help='اختبار سريع')
    parser.add_argument('--model', type=str, default='gemini-2.5-pro', help='النموذج للاختبار')
    parser.add_argument('--query', type=str, help='استعلام مخصص للاختبار')
    
    args = parser.parse_args()
    
    try:
        if args.quick:
            # اختبار سريع
            print("🚀 تشغيل اختبار سريع...")
            results = await quick_gemini_test(args.api_key)
            
            print("\n📊 نتائج الاختبار السريع:")
            print(json.dumps(results, indent=2, ensure_ascii=False))
            
        elif args.query:
            # اختبار استعلام مخصص
            if not args.api_key:
                args.api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
            
            if not args.api_key:
                print("❌ يجب توفير API Key")
                return
            
            tester = GeminiSearchTester(args.api_key)
            
            # إنشاء اختبار مخصص
            custom_test = {
                'name': 'Command Line Test',
                'query': args.query,
                'expected_keywords': args.query.lower().split()
            }
            
            tester.web_search_tests = [custom_test]
            results = await tester.test_web_search_capability(args.model)
            
            if results:
                result = results[0]
                print(f"\n🔍 نتيجة البحث:")
                print(f"النجاح: {'✅' if result.success else '❌'}")
                print(f"البحث على الويب: {'✅' if result.has_web_search else '❌'}")
                print(f"وقت التنفيذ: {result.execution_time:.2f}ث")
                print(f"\nالاستجابة:\n{result.response_text}")
        
        else:
            # النمط التفاعلي
            runner = GeminiTestRunner()
            await runner.run_interactive_test()
    
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")

if __name__ == "__main__":
    asyncio.run(main())
