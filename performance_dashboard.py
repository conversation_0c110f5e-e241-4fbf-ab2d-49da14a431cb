#!/usr/bin/env python3
"""
لوحة تحكم الأداء
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

def display_performance_dashboard():
    """عرض لوحة تحكم الأداء"""
    print("📊 لوحة تحكم الأداء - وكيل أخبار الألعاب")
    print("=" * 60)
    
    try:
        from modules.enhanced_performance_monitor import enhanced_performance_monitor
        from modules.publishing_success_optimizer import publishing_optimizer
        
        # الحصول على المقاييس الحالية
        metrics = enhanced_performance_monitor.get_real_time_metrics()
        summary = enhanced_performance_monitor.get_performance_summary()
        publishing_stats = publishing_optimizer.get_publishing_statistics()
        
        # عرض مقاييس النظام
        print("\n🖥️ مقاييس النظام:")
        system = metrics['system_metrics']
        print(f"  المعالج: {system['cpu_usage']}%")
        print(f"  الذاكرة: {system['memory_usage']}%")
        print(f"  القرص: {system['disk_usage']}%")
        print(f"  وقت التشغيل: {system['uptime_hours']} ساعة")
        
        # عرض مقاييس الأداء
        print("\n📈 مقاييس الأداء:")
        performance = metrics['performance_metrics']
        print(f"  المقالات المعالجة: {performance['articles_processed']}")
        print(f"  المقالات المنشورة: {performance['articles_published']}")
        print(f"  المقالات/الساعة: {performance['articles_per_hour']}")
        print(f"  معدل النجاح: {performance['success_rate']}%")
        print(f"  عدد الأخطاء: {performance['errors_count']}")
        print(f"  استدعاءات API: {performance['api_calls']}")
        
        # عرض إحصاءات النشر
        print("\n📝 إحصاءات النشر:")
        if 'total_attempts' in publishing_stats:
            print(f"  محاولات النشر: {publishing_stats['total_attempts']}")
            print(f"  النشر الناجح: {publishing_stats['successful_publishes']}")
            print(f"  معدل نجاح النشر: {publishing_stats['success_rate']}%")
        else:
            print("  لا توجد محاولات نشر بعد")
        
        # عرض حالة الصحة
        health_status = metrics['health_status']
        health_emoji = {
            'excellent': '🟢',
            'good': '🟡', 
            'warning': '🟠',
            'critical': '🔴'
        }
        print(f"\n🏥 حالة النظام: {health_emoji.get(health_status, '⚪')} {health_status}")
        
        # عرض التوصيات
        recommendations = summary['recommendations']
        if recommendations:
            print("\n💡 التوصيات:")
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"  {i}. {rec}")
        
        print("\n" + "=" * 60)
        print("✅ تم تحديث لوحة التحكم بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض لوحة التحكم: {e}")
        return False

def simulate_improved_performance():
    """محاكاة أداء محسن"""
    print("\n🚀 محاكاة الأداء المحسن...")
    
    try:
        from modules.enhanced_performance_monitor import enhanced_performance_monitor
        from modules.publishing_success_optimizer import publishing_optimizer
        
        # محاكاة معالجة مقالات
        for i in range(5):
            enhanced_performance_monitor.record_article_processed()
            enhanced_performance_monitor.record_article_published()
            enhanced_performance_monitor.record_api_call()
            
            # محاكاة نشر مقال
            test_article = {
                'title': f'🎮 أخبار الألعاب الجديدة {i+1}',
                'content': 'محتوى تجريبي عن الألعاب والتحديثات الجديدة. ' * 20,
                'keywords': ['ألعاب', 'أخبار', 'تحديثات']
            }
            
            result = publishing_optimizer.attempt_publish_with_retry(test_article)
            if result['success']:
                print(f"  ✅ تم نشر المقال {i+1}")
            else:
                print(f"  ❌ فشل نشر المقال {i+1}")
        
        print("\n📊 النتائج بعد المحاكاة:")
        display_performance_dashboard()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في المحاكاة: {e}")
        return False

if __name__ == "__main__":
    print("🎮 مرحباً بك في لوحة تحكم وكيل أخبار الألعاب")
    
    # عرض الحالة الحالية
    display_performance_dashboard()
    
    # محاكاة تحسينات
    simulate_improved_performance()
