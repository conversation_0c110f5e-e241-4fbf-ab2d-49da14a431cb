#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة اختبار شاملة لتحويل الصوت إلى نص باستخدام Whisper
تختبر الفيديو المقترح: https://youtu.be/Dvtswxb51K4?si=NB-sO4SIlpIKRAvx
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, Optional, List

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.whisper_quality_checker import whisper_quality_checker
from config.settings import BotConfig

class WhisperAudioToTextTester:
    """أداة اختبار شاملة لتحويل الصوت إلى نص باستخدام Whisper"""
    
    def __init__(self):
        self.analyzer = AdvancedYouTubeAnalyzer()
        self.test_results = []
        self.start_time = None
        
    async def run_comprehensive_test(self, video_url: str = "https://youtu.be/Dvtswxb51K4?si=NB-sO4SIlpIKRAvx"):
        """تشغيل اختبار شامل لتحويل الصوت إلى نص"""
        self.start_time = datetime.now()
        logger.info("🎤 بدء اختبار شامل لتحويل الصوت إلى نص باستخدام Whisper")
        logger.info(f"🎥 الفيديو المختبر: {video_url}")
        
        try:
            # استخراج معرف الفيديو
            video_id = self._extract_video_id(video_url)
            if not video_id:
                logger.error("❌ فشل في استخراج معرف الفيديو")
                return False
            
            logger.info(f"🆔 معرف الفيديو: {video_id}")
            
            # الاختبار 1: فحص تفاصيل الفيديو
            await self._test_video_details(video_id)
            
            # الاختبار 2: اختبار تحميل الصوت
            await self._test_audio_download(video_id)
            
            # الاختبار 3: اختبار Whisper API
            await self._test_whisper_transcription(video_id)
            
            # الاختبار 4: اختبار جودة النص المستخرج
            await self._test_transcript_quality(video_id)
            
            # الاختبار 5: اختبار الطرق البديلة
            await self._test_alternative_methods(video_id)
            
            # إنشاء تقرير شامل
            await self._generate_comprehensive_report()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في الاختبار الشامل: {e}")
            return False
    
    def _extract_video_id(self, video_url: str) -> Optional[str]:
        """استخراج معرف الفيديو من الرابط"""
        try:
            import re
            
            # أنماط مختلفة لروابط YouTube
            patterns = [
                r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/)([a-zA-Z0-9_-]{11})',
                r'v=([a-zA-Z0-9_-]{11})',
                r'/([a-zA-Z0-9_-]{11})'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, video_url)
                if match:
                    return match.group(1)
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في استخراج معرف الفيديو: {e}")
            return None
    
    async def _test_video_details(self, video_id: str):
        """اختبار الحصول على تفاصيل الفيديو"""
        test_name = "فحص تفاصيل الفيديو"
        logger.info(f"🔍 {test_name}...")
        
        try:
            start_time = time.time()
            video_details = await self.analyzer._get_video_details(video_id)
            end_time = time.time()
            
            if video_details:
                duration = video_details.get('duration', 'غير محدد')
                title = video_details.get('title', 'غير محدد')
                channel = video_details.get('channel_title', 'غير محدد')
                
                logger.info(f"✅ {test_name} نجح")
                logger.info(f"   📹 العنوان: {title}")
                logger.info(f"   📺 القناة: {channel}")
                logger.info(f"   ⏱️ المدة: {duration}")
                logger.info(f"   🕐 وقت الاستجابة: {end_time - start_time:.2f} ثانية")
                
                self.test_results.append({
                    'test': test_name,
                    'status': 'نجح',
                    'duration': end_time - start_time,
                    'details': {
                        'title': title,
                        'channel': channel,
                        'duration': duration
                    }
                })
            else:
                logger.error(f"❌ {test_name} فشل - لم يتم الحصول على تفاصيل")
                self.test_results.append({
                    'test': test_name,
                    'status': 'فشل',
                    'error': 'لم يتم الحصول على تفاصيل'
                })
                
        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
    
    async def _test_audio_download(self, video_id: str):
        """اختبار تحميل الصوت من الفيديو"""
        test_name = "تحميل الصوت"
        logger.info(f"🎵 {test_name}...")
        
        try:
            start_time = time.time()
            video_url = f"https://www.youtube.com/watch?v={video_id}"
            
            # محاولة تحميل الصوت
            import aiohttp
            async with aiohttp.ClientSession() as session:
                audio_data = await self.analyzer._download_audio_from_video(video_url, session)
            
            end_time = time.time()
            
            if audio_data:
                audio_size = len(audio_data)
                logger.info(f"✅ {test_name} نجح")
                logger.info(f"   📊 حجم الصوت: {audio_size:,} بايت ({audio_size / 1024 / 1024:.2f} MB)")
                logger.info(f"   🕐 وقت التحميل: {end_time - start_time:.2f} ثانية")
                
                self.test_results.append({
                    'test': test_name,
                    'status': 'نجح',
                    'duration': end_time - start_time,
                    'details': {
                        'audio_size_bytes': audio_size,
                        'audio_size_mb': audio_size / 1024 / 1024
                    }
                })
                
                return audio_data
            else:
                logger.error(f"❌ {test_name} فشل - لم يتم تحميل الصوت")
                self.test_results.append({
                    'test': test_name,
                    'status': 'فشل',
                    'error': 'لم يتم تحميل الصوت'
                })
                return None
                
        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
            return None
    
    async def _test_whisper_transcription(self, video_id: str):
        """اختبار تحويل الصوت إلى نص باستخدام Whisper"""
        test_name = "تحويل Whisper"
        logger.info(f"🎤 {test_name}...")

        print("\n" + "🎤" * 20 + " اختبار Whisper " + "🎤" * 20)
        print("سيتم عرض تفاصيل النص المستخرج أدناه...")
        print("-" * 80)

        try:
            start_time = time.time()
            transcript = await self.analyzer.extract_video_transcript_with_whisper(video_id)
            end_time = time.time()

            if transcript and len(transcript.strip()) > 0:
                transcript_length = len(transcript)
                word_count = len(transcript.split())

                logger.info(f"✅ {test_name} نجح")
                logger.info(f"   📝 طول النص: {transcript_length:,} حرف")
                logger.info(f"   🔤 عدد الكلمات: {word_count:,} كلمة")
                logger.info(f"   🕐 وقت التحويل: {end_time - start_time:.2f} ثانية")

                # عرض النص بشكل واضح في Terminal
                print(f"\n🎉 تم استخراج النص بنجاح!")
                print(f"📊 الإحصائيات: {transcript_length:,} حرف، {word_count:,} كلمة")
                print(f"⏱️ الوقت المستغرق: {end_time - start_time:.2f} ثانية")

                self.test_results.append({
                    'test': test_name,
                    'status': 'نجح',
                    'duration': end_time - start_time,
                    'details': {
                        'transcript_length': transcript_length,
                        'word_count': word_count,
                        'sample_text': transcript[:200]
                    }
                })

                return transcript
            else:
                logger.error(f"❌ {test_name} فشل - النص فارغ أو قصير جداً")
                print(f"\n❌ فشل في استخراج النص - النتيجة فارغة أو قصيرة جداً")

                self.test_results.append({
                    'test': test_name,
                    'status': 'فشل',
                    'error': 'النص فارغ أو قصير جداً'
                })
                return None

        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            print(f"\n❌ خطأ في اختبار Whisper: {e}")

            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
            return None

        finally:
            print("🎤" * 60 + "\n")
    
    async def _test_transcript_quality(self, video_id: str):
        """اختبار جودة النص المستخرج"""
        test_name = "فحص جودة النص"
        logger.info(f"🔍 {test_name}...")
        
        try:
            # الحصول على النص أولاً
            transcript = await self.analyzer.extract_video_transcript_with_whisper(video_id)
            
            if not transcript:
                logger.warning(f"⚠️ {test_name} تخطي - لا يوجد نص للفحص")
                return
            
            start_time = time.time()
            video_data = await self.analyzer._get_video_details(video_id)
            quality_result = await self.analyzer._check_transcript_quality(transcript, video_data)
            end_time = time.time()
            
            if quality_result:
                is_acceptable = quality_result.get('is_acceptable', False)
                quality_level = quality_result.get('quality_level', 'غير محدد')
                quality_score = quality_result.get('quality_score', 0)
                
                logger.info(f"✅ {test_name} مكتمل")
                logger.info(f"   🎯 مقبول: {'نعم' if is_acceptable else 'لا'}")
                logger.info(f"   📊 مستوى الجودة: {quality_level}")
                logger.info(f"   🔢 نقاط الجودة: {quality_score:.1f}/100")
                logger.info(f"   🕐 وقت الفحص: {end_time - start_time:.2f} ثانية")
                
                self.test_results.append({
                    'test': test_name,
                    'status': 'مكتمل',
                    'duration': end_time - start_time,
                    'details': {
                        'is_acceptable': is_acceptable,
                        'quality_level': quality_level,
                        'quality_score': quality_score
                    }
                })
            else:
                logger.error(f"❌ {test_name} فشل - لم يتم فحص الجودة")
                self.test_results.append({
                    'test': test_name,
                    'status': 'فشل',
                    'error': 'لم يتم فحص الجودة'
                })
                
        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
    
    async def _test_alternative_methods(self, video_id: str):
        """اختبار الطرق البديلة لاستخراج النص"""
        test_name = "الطرق البديلة"
        logger.info(f"🔄 {test_name}...")
        
        try:
            start_time = time.time()
            
            # اختبار الترجمة المدمجة
            embedded_transcript = await self.analyzer._extract_embedded_captions(video_id)
            
            # اختبار الوصف المفصل
            detailed_description = await self.analyzer._get_detailed_video_description(video_id)
            
            # اختبار المحتوى البديل
            fallback_content = await self.analyzer._get_video_metadata_as_content(video_id)
            
            end_time = time.time()
            
            results = {
                'embedded_captions': bool(embedded_transcript and len(embedded_transcript.strip()) > 20),
                'detailed_description': bool(detailed_description and len(detailed_description.strip()) > 50),
                'fallback_content': bool(fallback_content and len(fallback_content.strip()) > 30)
            }
            
            success_count = sum(results.values())
            
            logger.info(f"✅ {test_name} مكتمل")
            logger.info(f"   📺 ترجمة مدمجة: {'متاحة' if results['embedded_captions'] else 'غير متاحة'}")
            logger.info(f"   📝 وصف مفصل: {'متاح' if results['detailed_description'] else 'غير متاح'}")
            logger.info(f"   🔄 محتوى بديل: {'متاح' if results['fallback_content'] else 'غير متاح'}")
            logger.info(f"   📊 الطرق المتاحة: {success_count}/3")
            logger.info(f"   🕐 وقت الاختبار: {end_time - start_time:.2f} ثانية")
            
            self.test_results.append({
                'test': test_name,
                'status': 'مكتمل',
                'duration': end_time - start_time,
                'details': {
                    'available_methods': success_count,
                    'methods': results
                }
            })
            
        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
    
    async def _generate_comprehensive_report(self):
        """إنشاء تقرير شامل للاختبارات"""
        logger.info("📋 إنشاء تقرير شامل...")
        
        total_time = (datetime.now() - self.start_time).total_seconds()
        successful_tests = len([r for r in self.test_results if r['status'] == 'نجح' or r['status'] == 'مكتمل'])
        total_tests = len(self.test_results)
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_duration': total_time,
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': (successful_tests / total_tests * 100) if total_tests > 0 else 0,
            'test_results': self.test_results
        }
        
        # حفظ التقرير في ملف
        report_filename = f"whisper_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📄 تم حفظ التقرير في: {report_filename}")
        except Exception as e:
            logger.error(f"❌ فشل في حفظ التقرير: {e}")
        
        # طباعة ملخص التقرير
        logger.info("📊 ملخص نتائج الاختبار:")
        logger.info(f"   🕐 الوقت الإجمالي: {total_time:.2f} ثانية")
        logger.info(f"   ✅ الاختبارات الناجحة: {successful_tests}/{total_tests}")
        logger.info(f"   📈 معدل النجاح: {report['success_rate']:.1f}%")
        
        if report['success_rate'] >= 80:
            logger.info("🎉 نتائج ممتازة! نظام Whisper يعمل بشكل صحيح")
        elif report['success_rate'] >= 60:
            logger.info("👍 نتائج جيدة، قد تحتاج بعض التحسينات")
        else:
            logger.warning("⚠️ نتائج ضعيفة، يحتاج النظام إلى مراجعة")
        
        return report

async def main():
    """الدالة الرئيسية لتشغيل الاختبار"""
    print("🎤 أداة اختبار Whisper لتحويل الصوت إلى نص")
    print("=" * 50)
    
    # إنشاء مختبر
    tester = WhisperAudioToTextTester()
    
    # تشغيل الاختبار الشامل
    success = await tester.run_comprehensive_test()
    
    if success:
        print("\n✅ اكتمل الاختبار الشامل بنجاح!")
    else:
        print("\n❌ فشل في الاختبار الشامل!")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
