# متطلبات وكيل أخبار الألعاب - محسن باستخدام Gemini 2.5 Pro
# الذكاء الاصطناعي والمحتوى (Gemini 2.5 Pro فقط - تم إزالة RAG)
google-generativeai>=0.8.0
google-api-python-client>=2.110.0
google-auth-oauthlib>=1.1.0
google-auth>=2.23.0

# تيليجرام - إصدار محدد لحل مشكلة _Updater__polling_cleanup_cb
python-telegram-bot==20.6
aiohttp>=3.9.0

# استخراج المحتوى ومعالجة الويب
beautifulsoup4>=4.12.0
requests>=2.31.0
lxml>=4.9.0
html5lib>=1.1

# قاعدة البيانات والتخزين

# معالجة النصوص والتحليل
nltk>=3.8.0
textblob>=0.17.0
python-dateutil>=2.8.0

# YouTube transcript extraction
youtube-transcript-api>=1.6.0

# الصور والوسائط
Pillow>=10.1.0
imageio>=2.31.0

# الأدوات المساعدة
asyncio
aiofiles>=23.2.0
python-dotenv>=1.0.0
schedule>=1.2.0
pytz>=2023.3

# التطوير والاختبار
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.9.0
flake8>=6.1.0

# الأمان والتشفير
cryptography>=41.0.0
bcrypt>=4.0.0

# أدوات النظام
psutil>=5.9.0
watchdog>=3.0.0

# JSON والبيانات
ujson>=5.8.0
pydantic>=2.4.0

# التسجيل المتقدم
colorlog>=6.7.0
structlog>=23.1.0

# أدوات الشبكة
httpx>=0.25.0
websockets>=12.0

# معالجة الوقت والتاريخ
arrow>=1.3.0
pendulum>=2.1.0

# الويب والAPIات
fastapi>=0.103.0
uvicorn>=0.23.0

# أدوات التحليل (مبسطة - لا نحتاج مكتبات RAG الثقيلة مثل FAISS, sentence-transformers)
pandas>=2.1.0
numpy>=1.25.0

# ملاحظة: تم إزالة مكتبات RAG التالية لأننا نستخدم Gemini 2.5 Pro بدلاً منها:
# - sentence-transformers
# - faiss-cpu
# - transformers
# - torch
# - networkx (للـ Knowledge Graph)
# - scikit-learn
# هذا يوفر مساحة كبيرة ويقلل من تعقيد التثبيت

# أدوات التحكم
click>=8.1.0
rich>=13.6.0

# تحميل الفيديو والصوت - لحل مشكلة Whisper
yt-dlp>=2023.12.30
ffmpeg-python>=0.2.0

# إنشاء الصور بالذكاء الاصطناعي - ميزة جديدة
opencv-python>=4.8.0
scikit-image>=0.21.0
matplotlib>=3.7.0

# معالجة الصور المتقدمة
Wand>=0.6.0
imageio-ffmpeg>=0.4.0

# البحث واستخراج الأخبار المتقدم - ميزة جديدة قوية
beautifulsoup4>=4.12.0
lxml>=4.9.0
html5lib>=1.1
selenium>=4.15.0
scrapy>=2.11.0

# تحليل التواريخ المتقدم
python-dateutil>=2.8.0
pytz>=2023.3
