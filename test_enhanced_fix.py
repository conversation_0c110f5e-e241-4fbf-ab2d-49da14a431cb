#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح النظام المحسن
"""

import asyncio
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger

async def test_enhanced_system_components():
    """اختبار مكونات النظام المحسن"""
    print("🔍 اختبار مكونات النظام المحسن...")
    
    try:
        # اختبار استيراد المكونات الأساسية
        print("   📦 اختبار استيراد المكونات...")
        
        from modules.agent_state_manager import agent_state_manager, AgentState
        print("   ✅ agent_state_manager")
        
        from modules.smart_database_manager import smart_db_manager
        print("   ✅ smart_database_manager")
        
        from modules.operation_manager import operation_manager, OperationType, OperationPriority
        print("   ✅ operation_manager")
        
        from modules.smart_lifecycle_manager import lifecycle_manager, StartupMode, ShutdownReason
        print("   ✅ smart_lifecycle_manager")
        
        # اختبار بدء مدير العمليات
        print("   🚀 اختبار بدء مدير العمليات...")
        await operation_manager.start()
        print("   ✅ تم بدء مدير العمليات بنجاح")
        
        # اختبار إرسال عملية بسيطة
        print("   📋 اختبار إرسال عملية...")
        
        def test_function():
            return "test_result"
        
        operation_id = operation_manager.submit_operation(
            operation_type=OperationType.CONTENT_COLLECTION,
            func=test_function,
            priority=OperationPriority.HIGH
        )
        
        if operation_id:
            print(f"   ✅ تم إرسال العملية: {operation_id}")
            
            # انتظار اكتمال العملية
            for i in range(10):  # انتظار حتى 10 ثوان
                status = operation_manager.get_operation_status(operation_id)
                if status and status['state'] in ['completed', 'failed']:
                    print(f"   ✅ اكتملت العملية بحالة: {status['state']}")
                    break
                await asyncio.sleep(1)
            else:
                print("   ⚠️ انتهت مهلة انتظار العملية")
        else:
            print("   ❌ فشل في إرسال العملية")
        
        # إيقاف مدير العمليات
        print("   🛑 إيقاف مدير العمليات...")
        await operation_manager.stop()
        print("   ✅ تم إيقاف مدير العمليات")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_lifecycle_manager():
    """اختبار مدير دورة الحياة"""
    print("🔄 اختبار مدير دورة الحياة...")
    
    try:
        from modules.smart_lifecycle_manager import lifecycle_manager, StartupMode
        
        # اختبار البدء الآمن
        print("   🚀 اختبار البدء الآمن...")
        success = await lifecycle_manager.smart_startup(StartupMode.SAFE_MODE)
        
        if success:
            print("   ✅ نجح البدء الآمن")
            
            # اختبار الإيقاف
            print("   🛑 اختبار الإيقاف...")
            await lifecycle_manager.graceful_shutdown("اختبار")
            print("   ✅ نجح الإيقاف")
            
            return True
        else:
            print("   ❌ فشل البدء الآمن")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار مدير دورة الحياة: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار إصلاح النظام المحسن")
    print("=" * 60)
    
    # اختبار المكونات
    components_ok = await test_enhanced_system_components()
    
    if components_ok:
        print("\n✅ جميع المكونات تعمل بشكل صحيح")
        
        # اختبار مدير دورة الحياة
        lifecycle_ok = await test_lifecycle_manager()
        
        if lifecycle_ok:
            print("\n🎉 النظام المحسن جاهز للعمل!")
            return True
        else:
            print("\n⚠️ مشكلة في مدير دورة الحياة")
            return False
    else:
        print("\n❌ مشكلة في المكونات الأساسية")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("\n✅ الاختبار نجح - يمكن تشغيل النظام المحسن")
            sys.exit(0)
        else:
            print("\n❌ الاختبار فشل - استخدم النظام التقليدي")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        sys.exit(1)
