#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات نظام الموافقة
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_test_header(test_name):
    """طباعة رأس الاختبار"""
    print(f"\n{'='*60}")
    print(f"🧪 اختبار: {test_name}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """طباعة نتيجة الاختبار"""
    status = "✅ نجح" if success else "❌ فشل"
    print(f"{status} {test_name}")
    if details:
        print(f"   📝 {details}")

async def test_approval_system_initialization():
    """اختبار تهيئة نظام الموافقة"""
    print_test_header("تهيئة نظام الموافقة")
    
    try:
        from modules.video_approval_system import VideoApprovalSystem
        from config.settings import BotConfig
        
        # فحص التوكن الجديد
        expected_token = "8172189631:AAEb-0eRqHfy8M2dDjpJdeGHx4fjyczsR3w"
        actual_token = BotConfig.TELEGRAM_BOT_TOKEN
        token_correct = actual_token == expected_token
        
        print_result("التوكن الصحيح", token_correct,
                    f"التوكن: {actual_token[:20]}...")
        
        # إنشاء نظام الموافقة
        approval_system = VideoApprovalSystem()
        print_result("إنشاء VideoApprovalSystem", True)
        
        # فحص حالة التفعيل
        is_enabled = getattr(approval_system, 'approval_enabled', False)
        print_result("تفعيل النظام", is_enabled,
                    f"النظام: {'مفعل' if is_enabled else 'معطل'}")
        
        # فحص نوع التطبيق
        app_type = approval_system.application
        if app_type == "manual_mode":
            app_status = "وضع يدوي"
        elif app_type:
            app_status = f"تطبيق عادي: {type(app_type).__name__}"
        else:
            app_status = "غير محدد"
        
        print_result("نوع التطبيق", True, f"التطبيق: {app_status}")
        
        # فحص معرف المدير
        admin_id = approval_system._get_admin_chat_id()
        has_admin = bool(admin_id)
        print_result("معرف المدير", has_admin,
                    f"المعرف: {admin_id}")
        
        return token_correct and is_enabled and has_admin
        
    except Exception as e:
        print_result("تهيئة نظام الموافقة", False, f"خطأ: {str(e)}")
        return False

async def test_bot_startup():
    """اختبار بدء تشغيل البوت"""
    print_test_header("بدء تشغيل البوت")
    
    try:
        from modules.video_approval_system import VideoApprovalSystem
        
        approval_system = VideoApprovalSystem()
        
        # محاولة بدء تشغيل البوت
        await approval_system.start_approval_bot()
        
        # فحص إذا تم البدء بنجاح
        startup_success = approval_system.approval_enabled
        print_result("بدء تشغيل البوت", startup_success,
                    f"الحالة: {'نجح' if startup_success else 'فشل'}")
        
        # فحص الوضع
        if approval_system.application == "manual_mode":
            mode = "وضع يدوي"
        elif approval_system.application:
            mode = "وضع تلقائي"
        else:
            mode = "غير محدد"
        
        print_result("وضع التشغيل", True, f"الوضع: {mode}")
        
        return startup_success
        
    except Exception as e:
        print_result("بدء تشغيل البوت", False, f"خطأ: {str(e)}")
        return False

async def test_telegram_connection():
    """اختبار الاتصال بـ Telegram"""
    print_test_header("اختبار الاتصال بـ Telegram")
    
    try:
        from telegram import Bot
        from config.settings import BotConfig
        
        # إنشاء البوت
        bot = Bot(token=BotConfig.TELEGRAM_BOT_TOKEN)
        
        # اختبار الحصول على معلومات البوت
        try:
            bot_info = await bot.get_me()
            bot_username = bot_info.username
            bot_name = bot_info.first_name
            
            print_result("الاتصال بـ Telegram", True,
                        f"البوت: @{bot_username} ({bot_name})")
            
            return True
                
        except Exception as bot_error:
            print_result("الاتصال بـ Telegram", False, f"خطأ: {bot_error}")
            return False
        
    except Exception as e:
        print_result("اختبار Telegram", False, f"خطأ: {str(e)}")
        return False

async def test_approval_request_simulation():
    """اختبار محاكاة طلب الموافقة"""
    print_test_header("محاكاة طلب الموافقة")
    
    try:
        from modules.video_approval_system import VideoApprovalSystem
        
        approval_system = VideoApprovalSystem()
        
        # بيانات فيديو اختبار
        test_video = {
            'id': 'test_video_123',
            'title': 'Star Citizen (Pyro) and It Took Forever!',
            'duration': 720,
            'published_at': '2025-01-20T15:30:00Z',
            'channel_info': {
                'name': 'LevelCap Gaming',
                'language': 'en'
            }
        }
        
        test_text = "This is a test transcript from the video about Star Citizen."
        
        # متغير لتتبع نتيجة الموافقة
        approval_result = {'approved': None, 'reason': None}
        
        async def test_callback(approved: bool, reason: str):
            """callback اختبار"""
            approval_result['approved'] = approved
            approval_result['reason'] = reason
            print(f"📞 تم استدعاء callback: موافقة={approved}, السبب={reason}")
        
        # طلب الموافقة
        approval_id = await approval_system.request_video_approval(
            test_video, test_callback, test_text
        )
        
        # فحص النتائج
        has_approval_id = bool(approval_id)
        callback_called = approval_result['approved'] is not None
        
        print_result("إنشاء معرف الموافقة", has_approval_id,
                    f"المعرف: {approval_id}")
        print_result("استدعاء callback", callback_called,
                    f"النتيجة: {approval_result}")
        
        return has_approval_id and callback_called
        
    except Exception as e:
        print_result("محاكاة طلب الموافقة", False, f"خطأ: {str(e)}")
        return False

async def test_main_integration():
    """اختبار التكامل مع main.py"""
    print_test_header("التكامل مع main.py")
    
    try:
        from main import GamingNewsBot
        
        # إنشاء البوت
        bot = GamingNewsBot()
        print_result("إنشاء GamingNewsBot", True)
        
        # فحص نظام الموافقة
        has_approval_system = hasattr(bot, 'video_approval_system') and bot.video_approval_system
        print_result("وجود نظام الموافقة", has_approval_system)
        
        if has_approval_system:
            approval_enabled = getattr(bot.video_approval_system, 'approval_enabled', False)
            print_result("تفعيل نظام الموافقة", approval_enabled)
            
            return approval_enabled
        
        return has_approval_system
        
    except Exception as e:
        print_result("التكامل مع main.py", False, f"خطأ: {str(e)}")
        return False

async def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار إصلاحات نظام الموافقة...")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("تهيئة نظام الموافقة", test_approval_system_initialization),
        ("بدء تشغيل البوت", test_bot_startup),
        ("اختبار الاتصال بـ Telegram", test_telegram_connection),
        ("محاكاة طلب الموافقة", test_approval_request_simulation),
        ("التكامل مع main.py", test_main_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print_result(test_name, False, f"خطأ غير متوقع: {str(e)}")
            results.append((test_name, False))
    
    # تقرير النتائج النهائية
    print_test_header("تقرير النتائج النهائية")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        print_result(test_name, result)
    
    print(f"\n📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع إصلاحات نظام الموافقة تعمل بشكل مثالي!")
        print("📱 المدير @Yaasssssin سيحصل على رسائل الموافقة")
        print("📄 النص المستخرج سيُرسل للمراجعة")
    elif passed >= 4:
        print("✅ معظم الإصلاحات تعمل بشكل جيد")
        print("🔧 قد تحتاج بعض التحسينات الطفيفة")
    else:
        print("⚠️ بعض الإصلاحات تحتاج مراجعة إضافية")
    
    return passed >= 4

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ حرج: {e}")
        sys.exit(1)
