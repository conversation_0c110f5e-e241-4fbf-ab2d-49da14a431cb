#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة النقاط الثلاث في العناوين
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import SEOConfig
from modules.content_generator import ContentGenerator
from modules.user_engagement import UserEngagementEngine

def test_title_length_settings():
    """اختبار إعدادات طول العناوين الجديدة"""
    print("🔧 اختبار إعدادات طول العناوين:")
    print(f"   الحد الأدنى: {SEOConfig.TITLE_LENGTH_MIN} حرف")
    print(f"   الحد الأقصى: {SEOConfig.TITLE_LENGTH_MAX} حرف")
    
    # التأكد من أن الحدود تم تحديثها
    assert SEOConfig.TITLE_LENGTH_MAX == 120, f"الحد الأقصى يجب أن يكون 120، لكنه {SEOConfig.TITLE_LENGTH_MAX}"
    print("✅ تم تحديث إعدادات طول العناوين بنجاح")

def test_content_generator_title_enhancement():
    """اختبار تحسين العناوين في مولد المحتوى"""
    print("\n🎯 اختبار تحسين العناوين:")
    
    generator = ContentGenerator()
    
    # اختبار عنوان طويل
    long_title = "🎮 مراجعة حصرية: Ubisoft تؤكد بهدوء العمل على لعبة Ghost Recon الجديدة مع تحسينات رائعة ومميزات جديدة"
    enhanced_title = generator._enhance_title(long_title)
    
    print(f"   العنوان الأصلي: {long_title}")
    print(f"   العنوان المحسن: {enhanced_title}")
    print(f"   طول العنوان المحسن: {len(enhanced_title)} حرف")
    
    # التأكد من عدم وجود نقاط ثلاث
    assert "..." not in enhanced_title, f"العنوان المحسن لا يجب أن يحتوي على نقاط ثلاث: {enhanced_title}"
    print("✅ لا توجد نقاط ثلاث في العنوان المحسن")

def test_user_engagement_title_optimization():
    """اختبار تحسين طول العناوين في محرك التفاعل"""
    print("\n⚡ اختبار تحسين طول العناوين:")
    
    engagement = UserEngagementEngine()
    
    # اختبار عنوان طويل
    long_title = "🔥 خبر عاجل: Ubisoft تؤكد بهدوء العمل على لعبة Ghost Recon الجديدة مع تحسينات رائعة ومميزات جديدة ونظام قتال محسن"
    optimized_title = engagement._optimize_title_length(long_title)
    
    print(f"   العنوان الأصلي: {long_title}")
    print(f"   العنوان المحسن: {optimized_title}")
    print(f"   طول العنوان المحسن: {len(optimized_title)} حرف")
    
    # التأكد من عدم وجود نقاط ثلاث
    assert "..." not in optimized_title, f"العنوان المحسن لا يجب أن يحتوي على نقاط ثلاث: {optimized_title}"
    print("✅ لا توجد نقاط ثلاث في العنوان المحسن")

def test_seo_optimization():
    """اختبار تحسين SEO للعناوين"""
    print("\n🔍 اختبار تحسين SEO:")
    
    generator = ContentGenerator()
    
    # إنشاء مقال تجريبي
    test_article = {
        'title': '🎮 مراجعة حصرية: Ubisoft تؤكد بهدوء العمل على لعبة Ghost Recon الجديدة مع تحسينات رائعة ومميزات جديدة ونظام قتال محسن وعالم مفتوح',
        'content': 'هذا محتوى تجريبي للمقال يحتوي على معلومات مفصلة حول اللعبة الجديدة وتحسيناتها المختلفة.',
        'keywords': ['Ghost Recon', 'Ubisoft', 'ألعاب', 'مراجعة']
    }
    
    # تطبيق تحسين SEO
    optimized_article = generator.optimize_for_seo(test_article)
    
    print(f"   العنوان الأصلي: {test_article['title']}")
    print(f"   العنوان المحسن: {optimized_article['title']}")
    print(f"   طول العنوان المحسن: {len(optimized_article['title'])} حرف")
    
    # التأكد من عدم وجود نقاط ثلاث
    assert "..." not in optimized_article['title'], f"العنوان المحسن لا يجب أن يحتوي على نقاط ثلاث: {optimized_article['title']}"
    print("✅ لا توجد نقاط ثلاث في العنوان المحسن بـ SEO")

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار إصلاح مشكلة النقاط الثلاث في العناوين\n")
    
    try:
        test_title_length_settings()
        test_content_generator_title_enhancement()
        test_user_engagement_title_optimization()
        test_seo_optimization()
        
        print("\n🎉 تم اجتياز جميع الاختبارات بنجاح!")
        print("✅ تم حل مشكلة النقاط الثلاث في العناوين")
        
    except Exception as e:
        print(f"\n❌ فشل في الاختبار: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
