@echo off
chcp 65001 >nul
title نظام SEOwl للفهرسة الذكية - SEOwl Intelligent Indexing System

echo.
echo ========================================
echo 🔍 نظام SEOwl للفهرسة الذكية
echo SEOwl Intelligent Indexing System
echo ========================================
echo.

echo 🚀 بدء تشغيل نظام SEOwl...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير متاح في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من وجود الملفات المطلوبة
if not exist "modules\seowl_indexing_checker.py" (
    echo ❌ ملف SEOwl Checker غير موجود
    echo تأكد من وجود جميع الملفات المطلوبة
    pause
    exit /b 1
)

if not exist "seowl_web_interface.py" (
    echo ❌ ملف واجهة الويب غير موجود
    echo تأكد من وجود جميع الملفات المطلوبة
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة

REM تثبيت المتطلبات إذا لزم الأمر
echo.
echo 📦 التحقق من المتطلبات...

python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 📥 تثبيت Flask...
    pip install flask
)

python -c "import aiohttp" >nul 2>&1
if errorlevel 1 (
    echo 📥 تثبيت aiohttp...
    pip install aiohttp
)

echo ✅ جميع المتطلبات متوفرة

echo.
echo 🔑 مفتاح SEOwl API: V0P8IMfg8yDLL2buW3ZNijYnVXQIJICFlWCdBRsmbm1KZ1nA47PlgDhS2zje
echo.

echo 🎯 اختر طريقة التشغيل:
echo.
echo 1. تشغيل واجهة الويب فقط
echo 2. تشغيل التكامل مع الوكيل
echo 3. تشغيل النظام الكامل
echo 4. اختبار سريع
echo.

set /p choice="أدخل اختيارك (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🌐 تشغيل واجهة الويب...
    echo ستكون متاحة على: http://localhost:5003
    echo.
    echo اضغط Ctrl+C لإيقاف النظام
    python seowl_web_interface.py
    
) else if "%choice%"=="2" (
    echo.
    echo 🔗 تشغيل التكامل مع الوكيل...
    echo سيتم فحص المقالات تلقائياً بعد 3 ساعات من النشر
    echo.
    echo اضغط Ctrl+C لإيقاف النظام
    python -c "from modules.seowl_integration import seowl_integration; seowl_integration.start_integration(); import time; time.sleep(999999)"
    
) else if "%choice%"=="3" (
    echo.
    echo 🚀 تشغيل النظام الكامل...
    echo واجهة الإدارة: http://localhost:5003
    echo فحص تلقائي للمقالات بعد 3 ساعات من النشر
    echo.
    echo اضغط Ctrl+C لإيقاف النظام
    start /b python seowl_web_interface.py
    timeout /t 3 /nobreak >nul
    python -c "from modules.seowl_integration import seowl_integration; seowl_integration.start_integration(); import time; time.sleep(999999)"
    
) else if "%choice%"=="4" (
    echo.
    echo 🧪 تشغيل اختبار سريع...
    python start_seowl_system.py
    
) else (
    echo.
    echo ❌ اختيار غير صحيح
    echo تشغيل الاختبار السريع بدلاً من ذلك...
    python start_seowl_system.py
)

echo.
echo 🎉 انتهى التشغيل
pause
