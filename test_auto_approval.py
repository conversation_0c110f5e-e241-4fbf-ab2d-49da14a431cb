#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الموافقة التلقائية
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.web_approval_system import web_approval_system
from modules.video_approval_system import VideoApprovalSystem

async def test_web_approval_system():
    """اختبار نظام الموافقة الويب"""
    print("🧪 اختبار نظام الموافقة الويب...")
    
    # بيانات تجريبية للفيديو
    test_video_data = {
        'id': 'test_video_123',
        'title': 'We Upgraded To The Witcher 4 Engine! - C-Beams Devlog #45',
        'description': 'Test video description',
        'channel_title': 'Test Channel',
        'published_at': '2025-01-22T18:46:50Z'
    }
    
    test_extracted_text = "This is a test transcript about gaming news and updates."
    
    # متغير لحفظ نتيجة الموافقة
    approval_result = {'approved': False, 'reason': 'في انتظار الموافقة'}
    
    # دالة callback للموافقة
    async def approval_callback(approved: bool, reason: str):
        approval_result['approved'] = approved
        approval_result['reason'] = reason
        print(f"📋 نتيجة الموافقة: {'موافق' if approved else 'مرفوض'} - {reason}")
    
    try:
        # اختبار الموافقة التلقائية
        print(f"⚙️ حالة الموافقة التلقائية: {web_approval_system.auto_approve_enabled}")
        print(f"⏰ مهلة الموافقة التلقائية: {web_approval_system.auto_approve_timeout} ثانية")
        
        # طلب الموافقة
        approval_id = await web_approval_system.request_video_approval(
            test_video_data, approval_callback, test_extracted_text
        )
        
        if approval_id:
            print(f"✅ تم إرسال طلب الموافقة: {approval_id}")
            
            # انتظار الموافقة التلقائية
            max_wait = 15
            wait_time = 0
            
            while wait_time < max_wait and not approval_result['approved']:
                await asyncio.sleep(1)
                wait_time += 1
                print(f"⏳ انتظار... {wait_time}/{max_wait}")
            
            if approval_result['approved']:
                print(f"✅ نجح الاختبار! تمت الموافقة: {approval_result['reason']}")
                return True
            else:
                print(f"❌ فشل الاختبار! لم تتم الموافقة: {approval_result['reason']}")
                return False
        else:
            print("❌ فشل في إرسال طلب الموافقة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

async def test_video_approval_system():
    """اختبار نظام الموافقة للفيديوهات"""
    print("\n🧪 اختبار نظام الموافقة للفيديوهات...")
    
    # إنشاء نظام الموافقة
    video_approval = VideoApprovalSystem()
    
    # بيانات تجريبية للفيديو
    test_video_data = {
        'id': 'test_video_456',
        'title': 'Gaming News Update - Test Video',
        'description': 'Test video description for gaming news',
        'channel_title': 'Gaming Channel',
        'published_at': '2025-01-22T18:46:50Z'
    }
    
    test_extracted_text = "This is a test transcript about gaming news and updates."
    
    # متغير لحفظ نتيجة الموافقة
    approval_result = {'approved': False, 'reason': 'في انتظار الموافقة'}
    
    # دالة callback للموافقة
    async def approval_callback(approved: bool, reason: str):
        approval_result['approved'] = approved
        approval_result['reason'] = reason
        print(f"📋 نتيجة الموافقة: {'موافق' if approved else 'مرفوض'} - {reason}")
    
    try:
        # طلب الموافقة
        approval_id = await video_approval.request_video_approval(
            test_video_data, approval_callback, test_extracted_text
        )
        
        if approval_id:
            print(f"✅ تم إرسال طلب الموافقة: {approval_id}")
            
            # انتظار قصير للموافقة الفورية
            await asyncio.sleep(2)
            
            if approval_result['approved']:
                print(f"✅ نجح الاختبار! تمت الموافقة: {approval_result['reason']}")
                return True
            else:
                print(f"❌ فشل الاختبار! لم تتم الموافقة: {approval_result['reason']}")
                return False
        else:
            print("❌ فشل في إرسال طلب الموافقة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

async def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار أنظمة الموافقة التلقائية...")
    
    # اختبار نظام الموافقة الويب
    web_test_result = await test_web_approval_system()
    
    # اختبار نظام الموافقة للفيديوهات
    video_test_result = await test_video_approval_system()
    
    # النتائج النهائية
    print("\n📊 نتائج الاختبارات:")
    print(f"   • نظام الموافقة الويب: {'✅ نجح' if web_test_result else '❌ فشل'}")
    print(f"   • نظام الموافقة للفيديوهات: {'✅ نجح' if video_test_result else '❌ فشل'}")
    
    if web_test_result and video_test_result:
        print("\n🎉 جميع الاختبارات نجحت! نظام الموافقة التلقائية يعمل بشكل صحيح.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    asyncio.run(main())
