#!/usr/bin/env python3
"""
إصلاح شامل لمشاكل Txtify API و YouTube video text extraction
"""

import subprocess
import sys
import os
import logging
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_package(package_name):
    """تثبيت مكتبة Python"""
    try:
        logger.info(f"🔄 تثبيت {package_name}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package_name], 
                              capture_output=True, text=True, check=True)
        logger.info(f"✅ تم تثبيت {package_name} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ فشل في تثبيت {package_name}: {e}")
        logger.error(f"📄 تفاصيل الخطأ: {e.stderr}")
        return False

def test_youtube_transcript_api():
    """اختبار youtube-transcript-api"""
    try:
        from youtube_transcript_api import YouTubeTranscriptApi
        
        # اختبار بسيط
        test_video_id = "dQw4w9WgXcQ"  # Rick Roll - فيديو مشهور مع ترجمة
        transcript = YouTubeTranscriptApi.get_transcript(test_video_id, languages=['en'])
        
        if transcript and len(transcript) > 0:
            logger.info("✅ youtube-transcript-api يعمل بشكل صحيح")
            logger.info(f"📝 تم استخراج {len(transcript)} جملة من الفيديو التجريبي")
            return True
        else:
            logger.warning("⚠️ youtube-transcript-api لا يعيد نتائج")
            return False
            
    except ImportError:
        logger.error("❌ youtube-transcript-api غير مثبت")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار youtube-transcript-api: {e}")
        return False

def update_txtify_config():
    """تحديث إعدادات Txtify"""
    try:
        config_path = Path("config/settings.py")
        if not config_path.exists():
            logger.error("❌ ملف الإعدادات غير موجود")
            return False
        
        # قراءة الملف
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحديث إعدادات Txtify
        txtify_urls = [
            "https://nanami34-ai55.hf.space",  # الرابط الحالي
            "https://txtify-api.hf.space",     # رابط احتياطي
            "http://localhost:8000",           # رابط محلي
        ]
        
        # إضافة قائمة بالروابط الاحتياطية
        txtify_config = f'''
    # إعدادات Txtify المحسنة مع روابط احتياطية
    TXTIFY_API_URLS = {txtify_urls}
    TXTIFY_API_URL = TXTIFY_API_URLS[0]  # الرابط الأساسي
    TXTIFY_FALLBACK_ENABLED = True  # تفعيل النظام الاحتياطي
    TXTIFY_TIMEOUT_SECONDS = 300  # مهلة 5 دقائق
'''
        
        # إضافة التكوين إذا لم يكن موجوداً
        if "TXTIFY_API_URLS" not in content:
            # البحث عن مكان مناسب لإضافة التكوين
            if "TXTIFY_API_URL" in content:
                # استبدال التكوين الموجود
                import re
                pattern = r'TXTIFY_API_URL\s*=.*'
                content = re.sub(pattern, txtify_config.strip(), content)
            else:
                # إضافة في نهاية فئة BotConfig
                content = content.replace("class BotConfig:", f"class BotConfig:{txtify_config}")
        
        # حفظ الملف
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ تم تحديث إعدادات Txtify")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في تحديث إعدادات Txtify: {e}")
        return False

def create_txtify_test_script():
    """إنشاء سكريبت اختبار Txtify"""
    test_script = '''#!/usr/bin/env python3
"""
اختبار خدمة Txtify مع الروابط المختلفة
"""

import requests
import sys
import json
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from config.settings import BotConfig

def test_txtify_endpoints():
    """اختبار جميع endpoints المتاحة لـ Txtify"""
    test_video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    
    # قائمة الروابط للاختبار
    base_urls = [
        "https://nanami34-ai55.hf.space",
        "https://txtify-api.hf.space", 
        "http://localhost:8000",
    ]
    
    endpoints = [
        "/transcribe_youtube",
        "/api/transcribe_youtube",
        "/transcribe",
        "/api/transcribe"
    ]
    
    for base_url in base_urls:
        print(f"\\n🔍 اختبار {base_url}...")
        
        for endpoint in endpoints:
            full_url = f"{base_url.rstrip('/')}{endpoint}"
            print(f"  📝 اختبار {full_url}...")
            
            try:
                # تجربة طرق مختلفة
                methods = [
                    {'data': {'youtube_url': test_video_url}},
                    {'json': {'youtube_url': test_video_url}},
                    {'data': {'url': test_video_url}},
                ]
                
                for method in methods:
                    try:
                        response = requests.post(full_url, timeout=30, **method)
                        
                        if response.status_code == 200:
                            data = response.json()
                            transcript = data.get("transcript") or data.get("text") or data.get("result")
                            
                            if transcript:
                                print(f"    ✅ نجح! طول النص: {len(transcript)} حرف")
                                print(f"    📄 عينة من النص: {transcript[:100]}...")
                                return full_url, method
                        else:
                            print(f"    ⚠️ كود الاستجابة: {response.status_code}")
                            
                    except Exception as method_error:
                        print(f"    🔄 فشلت طريقة: {method_error}")
                        continue
                        
            except Exception as e:
                print(f"    ❌ فشل الاتصال: {e}")
                continue
    
    print("\\n❌ فشلت جميع المحاولات")
    return None, None

if __name__ == "__main__":
    print("🚀 بدء اختبار خدمة Txtify...")
    working_url, working_method = test_txtify_endpoints()
    
    if working_url:
        print(f"\\n✅ تم العثور على endpoint يعمل: {working_url}")
        print(f"📋 الطريقة الناجحة: {working_method}")
    else:
        print("\\n❌ لم يتم العثور على endpoint يعمل")
'''
    
    try:
        with open("test_txtify_service.py", 'w', encoding='utf-8') as f:
            f.write(test_script)
        logger.info("✅ تم إنشاء سكريبت اختبار Txtify")
        return True
    except Exception as e:
        logger.error(f"❌ فشل في إنشاء سكريبت الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية لإصلاح مشاكل Txtify و YouTube"""
    logger.info("🚀 بدء إصلاح مشاكل Txtify و YouTube video text extraction...")
    
    success_count = 0
    total_steps = 4
    
    # 1. تثبيت youtube-transcript-api
    logger.info("\\n📦 الخطوة 1: تثبيت youtube-transcript-api...")
    if install_package("youtube-transcript-api>=1.6.0"):
        success_count += 1
    
    # 2. اختبار youtube-transcript-api
    logger.info("\\n🧪 الخطوة 2: اختبار youtube-transcript-api...")
    if test_youtube_transcript_api():
        success_count += 1
    
    # 3. تحديث إعدادات Txtify
    logger.info("\\n⚙️ الخطوة 3: تحديث إعدادات Txtify...")
    if update_txtify_config():
        success_count += 1
    
    # 4. إنشاء سكريبت اختبار
    logger.info("\\n📝 الخطوة 4: إنشاء سكريبت اختبار Txtify...")
    if create_txtify_test_script():
        success_count += 1
    
    # النتيجة النهائية
    logger.info(f"\\n🎯 اكتمل الإصلاح: {success_count}/{total_steps} خطوات نجحت")
    
    if success_count == total_steps:
        logger.info("✅ تم إصلاح جميع مشاكل Txtify و YouTube بنجاح!")
        logger.info("🔄 يرجى إعادة تشغيل الوكيل لتطبيق التحديثات")
    else:
        logger.warning(f"⚠️ تم إصلاح {success_count} من أصل {total_steps} مشاكل")
        logger.info("📋 يرجى مراجعة الأخطاء أعلاه وإصلاحها يدوياً")

if __name__ == "__main__":
    main()
