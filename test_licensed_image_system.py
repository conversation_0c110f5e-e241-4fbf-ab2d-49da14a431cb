#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الصور المرخصة الجديد
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.licensed_image_manager import licensed_image_manager
from modules.smart_image_manager import smart_image_manager
from modules.logger import logger

class LicensedImageSystemTester:
    """فئة اختبار نظام الصور المرخصة"""
    
    def __init__(self):
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'provider_results': {},
            'integration_results': {},
            'performance_metrics': {}
        }
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار نظام الصور المرخصة الجديد...")
        print("=" * 60)
        
        # 1. اختبار موفري الصور المرخصة
        await self.test_licensed_providers()
        
        # 2. اختبار التكامل مع Smart Image Manager
        await self.test_smart_manager_integration()
        
        # 3. اختبار الأداء
        await self.test_performance()
        
        # 4. عرض النتائج النهائية
        self.display_final_results()
    
    async def test_licensed_providers(self):
        """اختبار موفري الصور المرخصة"""
        print("\n🔍 اختبار موفري الصور المرخصة...")
        
        test_games = [
            "The Witcher 3",
            "Cyberpunk 2077", 
            "League of Legends",
            "Valorant",
            "Assassin's Creed Valhalla"
        ]
        
        # اختبار كل موفر
        for provider_name in licensed_image_manager.providers.keys():
            print(f"\n📋 اختبار {provider_name.upper()}...")
            
            provider_results = {
                'games_tested': 0,
                'successful_searches': 0,
                'images_found': 0,
                'errors': []
            }
            
            provider = licensed_image_manager.providers[provider_name]
            
            for game in test_games:
                try:
                    self.test_results['tests_run'] += 1
                    provider_results['games_tested'] += 1
                    
                    print(f"  🎮 اختبار {game}...")
                    
                    images = await provider.search_game_images(game, 2)
                    
                    if images:
                        provider_results['successful_searches'] += 1
                        provider_results['images_found'] += len(images)
                        
                        print(f"    ✅ تم العثور على {len(images)} صورة")
                        
                        # عرض تفاصيل الصورة الأولى
                        if images:
                            img = images[0]
                            print(f"    📸 الصورة الأولى:")
                            print(f"       • المصدر: {img.source}")
                            print(f"       • النوع: {img.image_type}")
                            print(f"       • الترخيص: {img.license_type}")
                            print(f"       • آمنة لأدسنس: {img.safe_for_adsense}")
                        
                        self.test_results['tests_passed'] += 1
                    else:
                        print(f"    ⚠️ لم يتم العثور على صور")
                        
                except Exception as e:
                    provider_results['errors'].append(f"{game}: {str(e)}")
                    print(f"    ❌ خطأ: {e}")
                    self.test_results['tests_failed'] += 1
                
                # تأخير بين الاختبارات
                await asyncio.sleep(1)
            
            # حفظ نتائج الموفر
            self.test_results['provider_results'][provider_name] = provider_results
            
            # عرض ملخص الموفر
            success_rate = (provider_results['successful_searches'] / max(provider_results['games_tested'], 1)) * 100
            print(f"  📊 ملخص {provider_name.upper()}:")
            print(f"     • معدل النجاح: {success_rate:.1f}%")
            print(f"     • إجمالي الصور: {provider_results['images_found']}")
            print(f"     • الأخطاء: {len(provider_results['errors'])}")
    
    async def test_smart_manager_integration(self):
        """اختبار التكامل مع Smart Image Manager"""
        print("\n🔗 اختبار التكامل مع Smart Image Manager...")
        
        test_articles = [
            {
                'title': 'مراجعة لعبة The Witcher 3: Wild Hunt الجديدة',
                'content': 'لعبة The Witcher 3 هي واحدة من أفضل الألعاب...',
                'keywords': ['The Witcher 3', 'RPG', 'CD Projekt Red']
            },
            {
                'title': 'أخبار League of Legends الجديدة',
                'content': 'تحديث جديد للعبة League of Legends من Riot Games...',
                'keywords': ['League of Legends', 'MOBA', 'Riot Games']
            },
            {
                'title': 'إعلان عن Cyberpunk 2077 DLC',
                'content': 'إضافة جديدة للعبة Cyberpunk 2077...',
                'keywords': ['Cyberpunk 2077', 'DLC', 'CD Projekt Red']
            }
        ]
        
        integration_results = {
            'articles_tested': 0,
            'licensed_images_used': 0,
            'ai_fallbacks': 0,
            'total_images_generated': 0
        }
        
        for article in test_articles:
            try:
                self.test_results['tests_run'] += 1
                integration_results['articles_tested'] += 1
                
                print(f"\n📰 اختبار المقال: {article['title'][:50]}...")
                
                # اختبار Smart Image Manager
                image_result = await smart_image_manager.generate_smart_image_for_article(article)
                
                if image_result:
                    integration_results['total_images_generated'] += 1
                    
                    # فحص نوع الصورة المستخدمة
                    generation_method = image_result.get('generation_method', 'unknown')
                    
                    if generation_method == 'licensed_official':
                        integration_results['licensed_images_used'] += 1
                        print(f"    ✅ تم استخدام صورة مرخصة من {image_result.get('source', 'مصدر غير محدد')}")
                    else:
                        integration_results['ai_fallbacks'] += 1
                        print(f"    🤖 تم استخدام صورة من الذكاء الاصطناعي ({generation_method})")
                    
                    print(f"    📸 تفاصيل الصورة:")
                    print(f"       • الرابط: {image_result.get('url', 'غير متوفر')[:80]}...")
                    print(f"       • المصدر: {image_result.get('source', 'غير محدد')}")
                    print(f"       • آمنة لأدسنس: {image_result.get('safe_for_adsense', 'غير محدد')}")
                    
                    self.test_results['tests_passed'] += 1
                else:
                    print(f"    ❌ فشل في إنشاء صورة للمقال")
                    self.test_results['tests_failed'] += 1
                
            except Exception as e:
                print(f"    ❌ خطأ في اختبار التكامل: {e}")
                self.test_results['tests_failed'] += 1
            
            # تأخير بين الاختبارات
            await asyncio.sleep(2)
        
        # حفظ نتائج التكامل
        self.test_results['integration_results'] = integration_results
        
        # عرض ملخص التكامل
        licensed_rate = (integration_results['licensed_images_used'] / max(integration_results['total_images_generated'], 1)) * 100
        print(f"\n📊 ملخص التكامل:")
        print(f"   • معدل استخدام الصور المرخصة: {licensed_rate:.1f}%")
        print(f"   • الصور المرخصة: {integration_results['licensed_images_used']}")
        print(f"   • صور الذكاء الاصطناعي: {integration_results['ai_fallbacks']}")
    
    async def test_performance(self):
        """اختبار الأداء"""
        print("\n⚡ اختبار الأداء...")
        
        start_time = datetime.now()
        
        # اختبار سرعة البحث
        test_game = "The Witcher 3"
        search_times = []
        
        for i in range(3):
            search_start = datetime.now()
            images = await licensed_image_manager.get_licensed_images_for_game(test_game, 1)
            search_end = datetime.now()
            
            search_time = (search_end - search_start).total_seconds()
            search_times.append(search_time)
            
            print(f"   🔍 البحث {i+1}: {search_time:.2f} ثانية")
        
        # حساب المتوسط
        avg_search_time = sum(search_times) / len(search_times)
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        # حفظ مقاييس الأداء
        self.test_results['performance_metrics'] = {
            'average_search_time': avg_search_time,
            'total_test_time': total_time,
            'searches_per_second': len(search_times) / total_time
        }
        
        print(f"\n📊 مقاييس الأداء:")
        print(f"   • متوسط وقت البحث: {avg_search_time:.2f} ثانية")
        print(f"   • إجمالي وقت الاختبار: {total_time:.2f} ثانية")
    
    def display_final_results(self):
        """عرض النتائج النهائية"""
        print("\n" + "=" * 60)
        print("📋 النتائج النهائية لاختبار نظام الصور المرخصة")
        print("=" * 60)
        
        # إحصائيات عامة
        success_rate = (self.test_results['tests_passed'] / max(self.test_results['tests_run'], 1)) * 100
        
        print(f"\n📊 الإحصائيات العامة:")
        print(f"   • إجمالي الاختبارات: {self.test_results['tests_run']}")
        print(f"   • الاختبارات الناجحة: {self.test_results['tests_passed']}")
        print(f"   • الاختبارات الفاشلة: {self.test_results['tests_failed']}")
        print(f"   • معدل النجاح: {success_rate:.1f}%")
        
        # نتائج الموفرين
        print(f"\n🔍 نتائج موفري الصور:")
        for provider, results in self.test_results['provider_results'].items():
            success_rate = (results['successful_searches'] / max(results['games_tested'], 1)) * 100
            print(f"   • {provider.upper()}: {success_rate:.1f}% نجاح، {results['images_found']} صورة")
        
        # نتائج التكامل
        if self.test_results['integration_results']:
            integration = self.test_results['integration_results']
            licensed_rate = (integration['licensed_images_used'] / max(integration['total_images_generated'], 1)) * 100
            print(f"\n🔗 نتائج التكامل:")
            print(f"   • معدل الصور المرخصة: {licensed_rate:.1f}%")
            print(f"   • إجمالي الصور المولدة: {integration['total_images_generated']}")
        
        # مقاييس الأداء
        if self.test_results['performance_metrics']:
            perf = self.test_results['performance_metrics']
            print(f"\n⚡ مقاييس الأداء:")
            print(f"   • متوسط وقت البحث: {perf['average_search_time']:.2f} ثانية")
            print(f"   • إجمالي وقت الاختبار: {perf['total_test_time']:.2f} ثانية")
        
        # توصيات
        print(f"\n💡 التوصيات:")
        if success_rate >= 80:
            print("   ✅ النظام يعمل بشكل ممتاز!")
        elif success_rate >= 60:
            print("   ⚠️ النظام يعمل بشكل جيد، لكن يحتاج تحسينات")
        else:
            print("   ❌ النظام يحتاج إلى مراجعة وإصلاحات")
        
        # حفظ النتائج في ملف
        import json
        results_file = f"licensed_image_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            print(f"\n💾 تم حفظ النتائج في: {results_file}")
        except Exception as e:
            print(f"\n⚠️ فشل في حفظ النتائج: {e}")

async def main():
    """الدالة الرئيسية"""
    try:
        tester = LicensedImageSystemTester()
        await tester.run_all_tests()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
