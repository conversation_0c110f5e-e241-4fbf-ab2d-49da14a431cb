#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد سريع لمفاتيح نظام البحث المتقدم
"""

import os
from pathlib import Path

def create_env_template():
    """إنشاء قالب ملف .env مع المفاتيح الجديدة"""
    
    env_template = """
# ===== مفاتيح نظام البحث المتقدم الجديد =====

# 1. Tavily API (الأولوية الأولى - بحث عميق مع AI)
# احصل على مفاتيح مجانية من: https://tavily.com
TAVILY_API_KEY_1=your_tavily_key_1_here
TAVILY_API_KEY_2=your_tavily_key_2_here

# 2. SerpAPI (الأولوية الثانية - بحث Google متقدم)
# احصل على مفاتيح مجانية من: https://serpapi.com
SERPAPI_KEY_1=your_serpapi_key_1_here
SERPAPI_KEY_2=your_serpapi_key_2_here
SERPAPI_KEY_3=your_serpapi_key_3_here

# 3. ScraperAPI (الأولوية الثالثة - استخراج متقدم)
# احصل على مفاتيح مجانية من: https://scraperapi.com
SCRAPERAPI_KEY_1=your_scraperapi_key_1_here
SCRAPERAPI_KEY_2=your_scraperapi_key_2_here

# 4. Zyte API (الأولوية الرابعة - استخراج موزع)
# احصل على مفاتيح مجانية من: https://zyte.com
ZYTE_API_KEY_1=your_zyte_key_1_here
ZYTE_API_KEY_2=your_zyte_key_2_here

# 5. ContextualWeb Search API (الأولوية الخامسة - بحث أخبار)
# احصل على مفاتيح مجانية من: https://rapidapi.com (ابحث عن ContextualWeb)
CONTEXTUALWEB_KEY_1=your_contextualweb_key_1_here
CONTEXTUALWEB_KEY_2=your_contextualweb_key_2_here

# 6. Serper.dev API (الأولوية السادسة - AI للبحث)
# احصل على مفاتيح مجانية من: https://serper.dev
SERPER_DEV_KEY_1=your_serper_dev_key_1_here
SERPER_DEV_KEY_2=your_serper_dev_key_2_here

# 7. Google Custom Search JSON API (احتياطي أخير)
# احصل على مفاتيح مجانية من: https://console.cloud.google.com
GOOGLE_CUSTOM_SEARCH_KEY_1=your_google_custom_key_1_here
GOOGLE_CUSTOM_SEARCH_KEY_2=your_google_custom_key_2_here
GOOGLE_CUSTOM_SEARCH_ENGINE_ID=your_search_engine_id_here

# ===== المفاتيح الموجودة (احتفظ بها) =====
# لا تحذف المفاتيح الموجودة، فقط أضف الجديدة أعلاه
"""
    
    return env_template

def setup_keys_interactive():
    """إعداد تفاعلي للمفاتيح"""
    
    print("🚀 مرحباً بك في إعداد نظام البحث المتقدم!")
    print("=" * 60)
    print()
    
    # فحص ملف .env الموجود
    env_file = Path(".env")
    if env_file.exists():
        print("✅ تم العثور على ملف .env موجود")
        
        choice = input("هل تريد إنشاء ملف .env.template جديد؟ (y/n): ").lower()
        if choice == 'y':
            template_file = Path(".env.template")
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(create_env_template())
            print(f"✅ تم إنشاء {template_file}")
            print("📝 انسخ المفاتيح من .env.template إلى .env")
        else:
            print("📝 أضف المفاتيح الجديدة يدوياً إلى ملف .env")
    else:
        print("⚠️ لم يتم العثور على ملف .env")
        
        choice = input("هل تريد إنشاء ملف .env جديد؟ (y/n): ").lower()
        if choice == 'y':
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(create_env_template())
            print(f"✅ تم إنشاء {env_file}")
            print("📝 املأ المفاتيح في الملف الجديد")
    
    print()
    print("🔗 روابط الحصول على المفاتيح:")
    print("-" * 40)
    print("1. Tavily: https://tavily.com")
    print("2. SerpAPI: https://serpapi.com")
    print("3. ScraperAPI: https://scraperapi.com")
    print("4. Zyte: https://zyte.com")
    print("5. ContextualWeb: https://rapidapi.com")
    print("6. Serper.dev: https://serper.dev")
    print("7. Google Custom: https://console.cloud.google.com")
    print()
    
    print("📋 خطوات سريعة:")
    print("1. اذهب إلى كل موقع واحصل على مفتاح مجاني")
    print("2. أضف المفاتيح إلى ملف .env")
    print("3. شغل: python test_advanced_search_system.py")
    print("4. تأكد من عمل النظام")
    print()
    
    print("💡 نصائح:")
    print("- احصل على مفاتيح متعددة لكل خدمة")
    print("- ابدأ بـ Tavily (الأهم)")
    print("- جميع الخدمات مجانية 100%")
    print("- النظام يعمل مع مفتاح واحد على الأقل")

def check_current_keys():
    """فحص المفاتيح الموجودة"""
    
    print("🔍 فحص المفاتيح الموجودة:")
    print("-" * 30)
    
    # قائمة المفاتيح للفحص
    keys_to_check = [
        ("TAVILY_API_KEY_1", "Tavily"),
        ("SERPAPI_KEY_1", "SerpAPI"),
        ("SCRAPERAPI_KEY_1", "ScraperAPI"),
        ("ZYTE_API_KEY_1", "Zyte"),
        ("CONTEXTUALWEB_KEY_1", "ContextualWeb"),
        ("SERPER_DEV_KEY_1", "Serper.dev"),
        ("GOOGLE_CUSTOM_SEARCH_KEY_1", "Google Custom"),
    ]
    
    found_keys = 0
    total_keys = len(keys_to_check)
    
    for key_name, service_name in keys_to_check:
        key_value = os.getenv(key_name, "")
        if key_value and key_value != f"your_{key_name.lower()}_here":
            print(f"✅ {service_name}: موجود")
            found_keys += 1
        else:
            print(f"❌ {service_name}: غير موجود")
    
    print()
    print(f"📊 الإحصائيات: {found_keys}/{total_keys} مفاتيح موجودة")
    
    if found_keys == 0:
        print("⚠️ لا توجد مفاتيح! يرجى إضافة مفاتيح للبدء")
    elif found_keys < 3:
        print("⚠️ عدد قليل من المفاتيح، أضف المزيد لموثوقية أفضل")
    else:
        print("✅ عدد جيد من المفاتيح!")
    
    return found_keys

def main():
    """الدالة الرئيسية"""
    
    print("🔧 أداة إعداد نظام البحث المتقدم")
    print("=" * 50)
    print()
    
    # فحص المفاتيح الموجودة
    found_keys = check_current_keys()
    print()
    
    # إعداد تفاعلي
    setup_keys_interactive()
    print()
    
    # اختبار سريع
    if found_keys > 0:
        choice = input("هل تريد اختبار النظام الآن؟ (y/n): ").lower()
        if choice == 'y':
            print("🧪 تشغيل اختبار النظام...")
            os.system("python test_advanced_search_system.py")
    
    print()
    print("🎉 انتهى الإعداد!")
    print("📖 راجع ADVANCED_SEARCH_SETUP_GUIDE.md للتفاصيل")

if __name__ == "__main__":
    main()
