#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لـ Pollinations.AI - الطريقة الأساسية الجديدة
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.smart_image_manager import SmartImageManager

async def quick_test():
    """اختبار سريع لـ Pollinations.AI"""
    print("🚀 اختبار سريع لـ Pollinations.AI...")
    print("🎯 الطريقة الأساسية الجديدة (مجاني 100%)")
    print("=" * 50)
    
    # إنشاء مدير الصور الذكي مع إعدادات اختبار
    from modules.smart_image_manager import ImageGenerationPolicy
    test_policy = ImageGenerationPolicy(
        max_images_per_article=1,
        max_daily_generations=10,
        min_article_quality_score=5.0,  # تخفيض الحد الأدنى للاختبار
        cache_duration_hours=1,
        reuse_similar_images=False
    )
    smart_manager = SmartImageManager(test_policy)
    
    # مقال اختبار سريع محسن
    test_article = {
        'title': 'PlayStation 6 Gaming Console Announcement - Revolutionary Features and 8K Gaming',
        'content': 'Sony has officially announced the highly anticipated PlayStation 6 gaming console, featuring revolutionary technology that promises to transform the gaming experience. The new console includes advanced 8K gaming support, cutting-edge ray tracing capabilities, ultra-fast SSD storage with lightning-quick loading times, and innovative haptic feedback technology. Gaming enthusiasts worldwide are excited about the unprecedented visual fidelity and immersive gameplay experiences that the PlayStation 6 will deliver. The console is expected to launch next year with exclusive titles and backward compatibility with previous PlayStation generations.',
        'keywords': ['PlayStation 6', 'gaming console', 'Sony', 'announcement', '8K gaming', 'ray tracing', 'SSD storage', 'haptic feedback']
    }
    
    try:
        print(f"📰 اختبار المقال: {test_article['title']}")
        
        # إنشاء صورة
        result = await smart_manager.generate_smart_image_for_article(test_article)
        
        if result:
            print(f"✅ نجح الاختبار!")
            print(f"   الرابط: {result.get('url', 'N/A')}")
            print(f"   المصدر: {result.get('source', 'Unknown')}")
            print(f"   API المستخدم: {result.get('api_used', 'Unknown')}")
            
            # عرض إحصائيات
            stats = smart_manager.get_daily_stats()
            print(f"\n📊 إحصائيات:")
            print(f"   Pollinations.AI: {stats['api_calls'].get('pollinations', 0)}")
            print(f"   إجمالي الصور: {stats['images_generated']}")
            
            return True
        else:
            print(f"❌ فشل الاختبار")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(quick_test())
        if success:
            print("\n🎉 Pollinations.AI يعمل بنجاح!")
            print("💡 يمكنك الآن الاستفادة من إنشاء الصور المجاني 100%")
        else:
            print("\n⚠️ يرجى مراجعة الإعدادات")
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        sys.exit(1)
