#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار متخصص لقدرة Gemini 2.5 Pro على تحليل صور الألعاب
وكشف العناصر البصرية لإنشاء صور حصرية مشابهة
"""

import asyncio
import sys
import os
import json
import aiohttp
import base64
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from config.settings import google_api_manager

@dataclass
class GameImageAnalysis:
    """نتيجة تحليل صورة لعبة"""
    image_path: str
    game_title: str
    visual_style: str
    color_palette: List[str]
    art_style: str
    ui_elements: List[str]
    characters: List[str]
    environment: str
    lighting_style: str
    composition: str
    genre_indicators: List[str]
    platform_hints: List[str]
    creation_prompt: str
    success: bool
    confidence_score: float
    error_message: str = None

class GeminiGameImageAnalyzer:
    """محلل متخصص لصور الألعاب باستخدام Gemini 2.5 Pro"""
    
    def __init__(self):
        self.enabled = bool(google_api_manager and google_api_manager.get_key())
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.model_name = "gemini-2.5-pro"
        
        # قوالب التحليل المتخصصة
        self.analysis_prompts = {
            'detailed_game_analysis': """
            Analyze this gaming image in extreme detail for creating similar exclusive images.

            Provide a comprehensive analysis in this EXACT format:

            GAME_TITLE: [Game name if visible, or "Unknown Game"]
            
            VISUAL_STYLE: [Describe the overall visual style - realistic, cartoon, pixel art, anime, etc.]
            
            COLOR_PALETTE: [List dominant colors separated by commas - e.g., "dark blue, bright orange, metallic silver"]
            
            ART_STYLE: [Detailed art style - photorealistic, stylized, minimalist, fantasy, sci-fi, etc.]
            
            UI_ELEMENTS: [List all visible UI elements - health bars, menus, buttons, HUD elements, etc.]
            
            CHARACTERS: [Describe any visible characters - appearance, clothing, weapons, etc.]
            
            ENVIRONMENT: [Describe the setting - indoor/outdoor, futuristic/medieval, urban/nature, etc.]
            
            LIGHTING: [Describe lighting style - dramatic shadows, bright lighting, neon glow, natural light, etc.]
            
            COMPOSITION: [Describe layout and composition - centered, dynamic angles, close-up, wide shot, etc.]
            
            GENRE_INDICATORS: [Visual clues about game genre - weapons for FPS, cars for racing, etc.]
            
            PLATFORM_HINTS: [Any visible platform indicators - controller buttons, PC interface, mobile UI, etc.]
            
            CREATION_PROMPT: [A detailed prompt for creating a similar image, including style, colors, elements, and mood]
            """,
            
            'style_extraction': """
            Focus specifically on the artistic and visual style elements of this gaming image.
            
            Analyze and describe:
            1. Art direction and visual theme
            2. Color grading and mood
            3. Texture and material styles
            4. Typography and font choices
            5. Icon and symbol design
            6. Overall aesthetic approach
            
            Provide specific details that would help recreate a similar visual style.
            """,
            
            'ui_ux_analysis': """
            Analyze the user interface and user experience elements in this gaming image.
            
            Focus on:
            1. Menu design and layout
            2. Button styles and interactions
            3. Information hierarchy
            4. Visual feedback elements
            5. Navigation patterns
            6. Accessibility features
            
            Describe how these elements contribute to the overall gaming experience.
            """
        }
        
        logger.info(f"🎮 تم تهيئة محلل صور الألعاب المتخصص - الحالة: {'مفعل' if self.enabled else 'معطل'}")
    
    async def analyze_game_image(self, image_path: str) -> GameImageAnalysis:
        """تحليل شامل ومتخصص لصورة لعبة"""
        
        if not self.enabled:
            logger.warning("⚠️ محلل صور الألعاب غير مفعل")
            return self._create_empty_analysis(image_path, "محلل الصور غير مفعل")
        
        if not os.path.exists(image_path):
            logger.error(f"❌ الصورة غير موجودة: {image_path}")
            return self._create_empty_analysis(image_path, "الصورة غير موجودة")
        
        logger.info(f"🎮 بدء تحليل صورة اللعبة: {os.path.basename(image_path)}")
        
        try:
            # تحويل الصورة إلى base64
            image_data = await self._encode_image_to_base64(image_path)
            if not image_data:
                raise Exception("فشل في قراءة الصورة")
            
            # تنفيذ التحليل المفصل
            analysis_result = await self._perform_detailed_analysis(image_data, image_path)
            
            if analysis_result['success']:
                # معالجة النتائج وإنشاء التقرير
                game_analysis = self._process_game_analysis(image_path, analysis_result['response'])
                logger.info(f"✅ تم تحليل صورة اللعبة بنجاح: {os.path.basename(image_path)}")
                return game_analysis
            else:
                logger.error(f"❌ فشل في تحليل صورة اللعبة: {analysis_result['error']}")
                return self._create_empty_analysis(image_path, analysis_result['error'])
                
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل صورة اللعبة {image_path}: {e}")
            return self._create_empty_analysis(image_path, str(e))
    
    async def _perform_detailed_analysis(self, image_data: str, image_path: str) -> Dict[str, Any]:
        """تنفيذ التحليل المفصل للصورة"""
        
        try:
            url = f"{self.base_url}/models/{self.model_name}:generateContent"
            
            headers = {
                'Content-Type': 'application/json'
            }
            
            params = {
                'key': google_api_manager.get_key()
            }
            
            # تحديد نوع الصورة
            image_format = 'image/png' if image_path.lower().endswith('.png') else 'image/jpeg'
            
            payload = {
                "contents": [{
                    "parts": [
                        {
                            "text": self.analysis_prompts['detailed_game_analysis']
                        },
                        {
                            "inlineData": {
                                "mimeType": image_format,
                                "data": image_data
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "temperature": 0.3,  # دقة عالية للتحليل
                    "topK": 32,
                    "topP": 0.8,
                    "maxOutputTokens": 2048,
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, params=params, json=payload, timeout=60) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'candidates' in data and data['candidates']:
                            content = data['candidates'][0].get('content', {})
                            parts = content.get('parts', [])
                            
                            if parts and 'text' in parts[0]:
                                response_text = parts[0]['text'].strip()
                                
                                return {
                                    'success': True,
                                    'response': response_text
                                }
                    
                    # في حالة فشل الطلب
                    error_text = await response.text()
                    return {
                        'success': False,
                        'error': f'HTTP {response.status}: {error_text[:200]}'
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _process_game_analysis(self, image_path: str, response_text: str) -> GameImageAnalysis:
        """معالجة نتائج التحليل وإنشاء التقرير المنظم"""
        
        # قيم افتراضية
        game_title = "Unknown Game"
        visual_style = "Unknown"
        color_palette = []
        art_style = "Unknown"
        ui_elements = []
        characters = []
        environment = "Unknown"
        lighting_style = "Unknown"
        composition = "Unknown"
        genre_indicators = []
        platform_hints = []
        creation_prompt = ""
        confidence_score = 0.5
        
        try:
            # تحليل الاستجابة المنظمة
            lines = response_text.split('\n')
            
            for line in lines:
                line = line.strip()
                
                if line.startswith('GAME_TITLE:'):
                    game_title = line.split(':', 1)[1].strip()
                    
                elif line.startswith('VISUAL_STYLE:'):
                    visual_style = line.split(':', 1)[1].strip()
                    
                elif line.startswith('COLOR_PALETTE:'):
                    colors_str = line.split(':', 1)[1].strip()
                    if colors_str and colors_str != "Unknown":
                        color_palette = [c.strip() for c in colors_str.split(',') if c.strip()]
                        
                elif line.startswith('ART_STYLE:'):
                    art_style = line.split(':', 1)[1].strip()
                    
                elif line.startswith('UI_ELEMENTS:'):
                    ui_str = line.split(':', 1)[1].strip()
                    if ui_str and ui_str != "None":
                        ui_elements = [u.strip() for u in ui_str.split(',') if u.strip()]
                        
                elif line.startswith('CHARACTERS:'):
                    char_str = line.split(':', 1)[1].strip()
                    if char_str and char_str != "None":
                        characters = [c.strip() for c in char_str.split(',') if c.strip()]
                        
                elif line.startswith('ENVIRONMENT:'):
                    environment = line.split(':', 1)[1].strip()
                    
                elif line.startswith('LIGHTING:'):
                    lighting_style = line.split(':', 1)[1].strip()
                    
                elif line.startswith('COMPOSITION:'):
                    composition = line.split(':', 1)[1].strip()
                    
                elif line.startswith('GENRE_INDICATORS:'):
                    genre_str = line.split(':', 1)[1].strip()
                    if genre_str and genre_str != "None":
                        genre_indicators = [g.strip() for g in genre_str.split(',') if g.strip()]
                        
                elif line.startswith('PLATFORM_HINTS:'):
                    platform_str = line.split(':', 1)[1].strip()
                    if platform_str and platform_str != "None":
                        platform_hints = [p.strip() for p in platform_str.split(',') if p.strip()]
                        
                elif line.startswith('CREATION_PROMPT:'):
                    creation_prompt = line.split(':', 1)[1].strip()
            
            # حساب نقاط الثقة بناءً على كمية المعلومات المستخرجة
            info_count = 0
            if game_title != "Unknown Game": info_count += 1
            if visual_style != "Unknown": info_count += 1
            if color_palette: info_count += 1
            if art_style != "Unknown": info_count += 1
            if ui_elements: info_count += 1
            if characters: info_count += 1
            if environment != "Unknown": info_count += 1
            if lighting_style != "Unknown": info_count += 1
            if composition != "Unknown": info_count += 1
            if genre_indicators: info_count += 1
            if platform_hints: info_count += 1
            if creation_prompt: info_count += 1
            
            confidence_score = min(info_count / 12.0, 1.0)
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في معالجة نتائج التحليل: {e}")
        
        return GameImageAnalysis(
            image_path=image_path,
            game_title=game_title,
            visual_style=visual_style,
            color_palette=color_palette,
            art_style=art_style,
            ui_elements=ui_elements,
            characters=characters,
            environment=environment,
            lighting_style=lighting_style,
            composition=composition,
            genre_indicators=genre_indicators,
            platform_hints=platform_hints,
            creation_prompt=creation_prompt,
            success=True,
            confidence_score=confidence_score
        )
    
    def _create_empty_analysis(self, image_path: str, error_message: str) -> GameImageAnalysis:
        """إنشاء تحليل فارغ في حالة الفشل"""
        return GameImageAnalysis(
            image_path=image_path,
            game_title="Unknown",
            visual_style="Unknown",
            color_palette=[],
            art_style="Unknown",
            ui_elements=[],
            characters=[],
            environment="Unknown",
            lighting_style="Unknown",
            composition="Unknown",
            genre_indicators=[],
            platform_hints=[],
            creation_prompt="",
            success=False,
            confidence_score=0.0,
            error_message=error_message
        )
    
    async def _encode_image_to_base64(self, image_path: str) -> str:
        """تحويل الصورة إلى base64"""
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                encoded_data = base64.b64encode(image_data).decode('utf-8')
                return encoded_data
        except Exception as e:
            logger.error(f"❌ فشل في تحويل الصورة إلى base64: {e}")
            return None

async def create_realistic_game_images():
    """إنشاء صور ألعاب واقعية للاختبار"""
    test_images = []
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # صورة 1: واجهة لعبة FPS
        img1 = Image.new('RGB', (800, 600), color='#1a1a1a')
        draw1 = ImageDraw.Draw(img1)
        
        # خلفية اللعبة
        draw1.rectangle([0, 0, 800, 400], fill='#2d4a3e')  # منطقة اللعب
        draw1.rectangle([0, 400, 800, 600], fill='#1a1a1a')  # منطقة UI
        
        # عناصر UI
        draw1.rectangle([20, 420, 200, 450], fill='#ff0000', outline='#ffffff', width=2)  # شريط الصحة
        draw1.text((25, 425), "Health: 85/100", fill='white')
        
        draw1.rectangle([20, 460, 150, 490], fill='#0066ff', outline='#ffffff', width=2)  # شريط الذخيرة
        draw1.text((25, 465), "Ammo: 24/30", fill='white')
        
        # خريطة صغيرة
        draw1.rectangle([650, 420, 780, 550], fill='#333333', outline='#ffffff', width=2)
        draw1.text((680, 525), "Mini Map", fill='white')
        
        # نقاط
        draw1.text((350, 425), "Score: 1,250", fill='#ffff00')
        draw1.text((350, 445), "Kills: 15", fill='#00ff00')
        
        img1_path = 'test_fps_game_ui.png'
        img1.save(img1_path, 'PNG')
        test_images.append(img1_path)
        
        # صورة 2: لعبة RPG فانتازيا
        img2 = Image.new('RGB', (800, 600), color='#2a1810')
        draw2 = ImageDraw.Draw(img2)
        
        # خلفية فانتازيا
        draw2.rectangle([0, 0, 800, 400], fill='#4a3728')  # منطقة اللعب
        draw2.ellipse([300, 150, 500, 250], fill='#8b4513', outline='#daa520', width=3)  # شخصية
        
        # واجهة RPG
        draw2.rectangle([0, 400, 800, 600], fill='#1a1a1a')
        
        # أشرطة الشخصية
        draw2.rectangle([50, 420, 300, 440], fill='#ff0000', outline='#ffffff', width=1)  # HP
        draw2.text((55, 422), "HP: 450/500", fill='white')
        
        draw2.rectangle([50, 450, 250, 470], fill='#0066ff', outline='#ffffff', width=1)  # MP
        draw2.text((55, 452), "MP: 120/200", fill='white')
        
        draw2.rectangle([50, 480, 200, 500], fill='#ffff00', outline='#ffffff', width=1)  # XP
        draw2.text((55, 482), "XP: 1,250/2,000", fill='white')
        
        # معلومات الشخصية
        draw2.text((400, 420), "Level: 15 Warrior", fill='#daa520')
        draw2.text((400, 440), "Location: Dark Forest", fill='#90ee90')
        draw2.text((400, 460), "Quest: Find the Crystal", fill='#87ceeb')
        
        img2_path = 'test_rpg_fantasy_game.png'
        img2.save(img2_path, 'PNG')
        test_images.append(img2_path)
        
        # صورة 3: لعبة سباق
        img3 = Image.new('RGB', (800, 600), color='#87ceeb')
        draw3 = ImageDraw.Draw(img3)
        
        # مضمار السباق
        draw3.rectangle([0, 200, 800, 400], fill='#696969')  # الطريق
        draw3.rectangle([350, 250, 450, 350], fill='#ff0000', outline='#000000', width=2)  # السيارة
        
        # واجهة السباق
        draw3.rectangle([0, 0, 800, 100], fill='#000000')  # شريط علوي
        draw3.rectangle([0, 500, 800, 600], fill='#000000')  # شريط سفلي
        
        # معلومات السباق
        draw3.text((50, 20), "Speed: 180 KM/H", fill='#00ff00')
        draw3.text((50, 40), "Lap: 2/3", fill='#ffffff')
        draw3.text((50, 60), "Position: 3rd", fill='#ffff00')
        
        draw3.text((600, 20), "Time: 02:45", fill='#ffffff')
        draw3.text((600, 40), "Best Lap: 01:23", fill='#00ff00')
        
        # عداد السرعة
        draw3.ellipse([650, 520, 750, 580], fill='#333333', outline='#ffffff', width=2)
        draw3.text((680, 545), "180", fill='#ff0000')
        
        img3_path = 'test_racing_game.png'
        img3.save(img3_path, 'PNG')
        test_images.append(img3_path)
        
        logger.info(f"✅ تم إنشاء {len(test_images)} صورة لعبة واقعية للاختبار")
        return test_images
        
    except ImportError:
        logger.warning("⚠️ PIL غير متوفر")
        return []
    except Exception as e:
        logger.error(f"❌ فشل في إنشاء صور الاختبار: {e}")
        return []

async def test_game_image_analysis():
    """اختبار تحليل صور الألعاب المتخصص"""
    
    print("\n" + "="*70)
    print("🎮 اختبار تحليل صور الألعاب المتخصص - Gemini 2.5 Pro")
    print("="*70)
    
    analyzer = GeminiGameImageAnalyzer()
    
    if not analyzer.enabled:
        print("❌ محلل صور الألعاب غير مفعل - تحقق من مفتاح API")
        return
    
    # إنشاء صور اختبار واقعية
    test_images = await create_realistic_game_images()
    
    if not test_images:
        print("❌ لا توجد صور للاختبار")
        return
    
    print(f"🎮 تحليل {len(test_images)} صورة لعبة...")
    
    results = []
    
    for i, image_path in enumerate(test_images, 1):
        print(f"\n🔍 {i}. تحليل: {os.path.basename(image_path)}")
        print("-" * 50)
        
        try:
            analysis = await analyzer.analyze_game_image(image_path)
            results.append(analysis)
            
            if analysis.success:
                print(f"✅ نجح التحليل - ثقة: {analysis.confidence_score:.2f}")
                print(f"🎮 اللعبة: {analysis.game_title}")
                print(f"🎨 النمط البصري: {analysis.visual_style}")
                print(f"🌈 الألوان: {', '.join(analysis.color_palette[:3])}...")
                print(f"🖼️ نمط الفن: {analysis.art_style}")
                
                if analysis.ui_elements:
                    print(f"🖥️ عناصر UI: {', '.join(analysis.ui_elements[:3])}...")
                
                if analysis.genre_indicators:
                    print(f"🎯 نوع اللعبة: {', '.join(analysis.genre_indicators[:2])}...")
                
                print(f"💡 prompt للإنشاء:")
                print(f"   {analysis.creation_prompt[:100]}...")
                
            else:
                print(f"❌ فشل التحليل: {analysis.error_message}")
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    # إحصائيات النتائج
    print(f"\n📊 ملخص النتائج:")
    print("=" * 50)
    
    successful = sum(1 for r in results if r.success)
    avg_confidence = sum(r.confidence_score for r in results if r.success) / max(successful, 1)
    
    print(f"نجح: {successful}/{len(results)}")
    print(f"متوسط الثقة: {avg_confidence:.2f}")
    
    if successful > 0:
        print(f"\n🎯 قدرات مؤكدة:")
        
        # فحص القدرات المختلفة
        games_detected = sum(1 for r in results if r.success and r.game_title != "Unknown")
        styles_detected = sum(1 for r in results if r.success and r.visual_style != "Unknown")
        colors_detected = sum(1 for r in results if r.success and r.color_palette)
        ui_detected = sum(1 for r in results if r.success and r.ui_elements)
        prompts_generated = sum(1 for r in results if r.success and r.creation_prompt)
        
        print(f"   🎮 كشف الألعاب: {games_detected}/{successful}")
        print(f"   🎨 تحليل النمط البصري: {styles_detected}/{successful}")
        print(f"   🌈 استخراج الألوان: {colors_detected}/{successful}")
        print(f"   🖥️ تحليل UI: {ui_detected}/{successful}")
        print(f"   💡 إنشاء prompts: {prompts_generated}/{successful}")
        
        print(f"\n💡 التوصيات:")
        if prompts_generated > 0:
            print("   ✅ ممتاز لإنشاء prompts للصور المشابهة")
        if colors_detected > 0:
            print("   ✅ ممتاز لاستخراج لوحات الألوان")
        if ui_detected > 0:
            print("   ✅ ممتاز لتحليل واجهات الألعاب")
        if styles_detected > 0:
            print("   ✅ ممتاز لتحديد الأنماط البصرية")
    
    # حفظ النتائج
    report = {
        'test_timestamp': datetime.now().isoformat(),
        'total_images': len(results),
        'successful_analyses': successful,
        'average_confidence': avg_confidence,
        'detailed_results': [
            {
                'image': os.path.basename(r.image_path),
                'success': r.success,
                'confidence': r.confidence_score,
                'game_title': r.game_title,
                'visual_style': r.visual_style,
                'color_palette': r.color_palette,
                'creation_prompt': r.creation_prompt[:200] if r.creation_prompt else ""
            }
            for r in results
        ]
    }
    
    report_file = f"game_image_analysis_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ التقرير في: {report_file}")
    
    # تنظيف الملفات
    for image_path in test_images:
        if os.path.exists(image_path):
            try:
                os.remove(image_path)
            except:
                pass

async def main():
    """الدالة الرئيسية"""
    try:
        await test_game_image_analysis()
        print("\n🎉 اكتمل اختبار تحليل صور الألعاب!")
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في التشغيل: {e}")

if __name__ == "__main__":
    asyncio.run(main())
