#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للوكيل الرئيسي
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_bot_initialization():
    """اختبار تهيئة الوكيل"""
    print("🤖 اختبار تهيئة الوكيل...")
    
    try:
        from main import GamingNewsBot
        
        # إنشاء مثيل من الوكيل
        bot = GamingNewsBot()
        print("✅ تم إنشاء مثيل الوكيل بنجاح")
        
        # اختبار التهيئة
        success = await bot.initialize()
        
        if success:
            print("✅ تم تهيئة الوكيل بنجاح")
            
            # اختبار جمع المحتوى (بدون نشر)
            print("📰 اختبار جمع المحتوى...")
            try:
                # محاولة جمع محتوى بسيط
                content = await bot._collect_content()
                print(f"✅ تم جمع {len(content)} عنصر محتوى")
                
                if content:
                    print(f"📰 أول عنصر: {content[0].get('title', 'بدون عنوان')[:50]}...")
                
            except Exception as e:
                print(f"⚠️ تحذير في جمع المحتوى: {e}")
            
            return True
        else:
            print("❌ فشل في تهيئة الوكيل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الوكيل: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_connection():
    """اختبار اتصال قاعدة البيانات"""
    print("\n💾 اختبار اتصال قاعدة البيانات...")
    
    try:
        from modules.database import db
        
        # اختبار الحصول على إحصائيات
        stats = db.get_stats_summary(1)
        print(f"✅ إحصائيات قاعدة البيانات: {stats}")
        
        # اختبار الحصول على المقالات
        articles = db.get_recent_articles(3)
        print(f"✅ تم العثور على {len(articles)} مقال حديث")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

async def test_content_generator():
    """اختبار مولد المحتوى"""
    print("\n✍️ اختبار مولد المحتوى...")
    
    try:
        from modules.content_generator import ContentGenerator
        
        generator = ContentGenerator()
        print("✅ تم إنشاء مولد المحتوى")
        
        # اختبار بسيط لتوليد محتوى
        test_source = {
            'title': 'أخبار ألعاب جديدة',
            'summary': 'تحديثات مثيرة في عالم الألعاب',
            'url': 'https://example.com',
            'keywords': ['ألعاب', 'أخبار']
        }
        
        # ملاحظة: لا نشغل التوليد الفعلي لتوفير الوقت والموارد
        print("✅ مولد المحتوى جاهز للعمل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مولد المحتوى: {e}")
        return False

async def main():
    """الاختبار الرئيسي"""
    print("🧪 اختبار سريع للوكيل الذكي")
    print("=" * 50)
    
    tests = [
        ("قاعدة البيانات", test_database_connection()),
        ("مولد المحتوى", test_content_generator()),
        ("تهيئة الوكيل", test_bot_initialization())
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_coro in tests:
        print(f"\n🔍 اختبار {test_name}...")
        try:
            result = await test_coro
            if result:
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print(f"\n📊 النتائج النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! الوكيل جاهز للعمل")
        print("\n🚀 يمكنك الآن تشغيل الوكيل باستخدام:")
        print("   python main.py")
    elif passed > 0:
        print("⚠️ بعض الاختبارات نجحت، الوكيل قد يعمل مع بعض القيود")
    else:
        print("❌ جميع الاختبارات فشلت، يرجى مراجعة التكوين")

if __name__ == "__main__":
    asyncio.run(main())
