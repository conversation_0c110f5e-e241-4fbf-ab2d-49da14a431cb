
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
لوحة مراقبة بسيطة لنظام أخبار الألعاب
"""

import sys
import os
from datetime import datetime, timedelta

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def show_system_status():
    """عرض حالة النظام"""
    print("="*60)
    print("📊 لوحة مراقبة نظام أخبار الألعاب")
    print("="*60)
    print(f"⏰ الوقت الحالي: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # فحص قاعدة البيانات
        import sqlite3
        db_path = "data/articles.db"
        
        if os.path.exists(db_path):
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # عدد المقالات
                cursor.execute("SELECT COUNT(*) FROM published_articles")
                article_count = cursor.fetchone()[0]
                print(f"📰 إجمالي المقالات: {article_count}")
                
                # آخر مقال
                cursor.execute("SELECT title, published_at FROM published_articles ORDER BY published_at DESC LIMIT 1")
                last_article = cursor.fetchone()
                if last_article:
                    print(f"📝 آخر مقال: {last_article[0]}")
                    print(f"📅 تاريخ النشر: {last_article[1]}")
                
                # إحصائيات اليوم
                today = datetime.now().date()
                cursor.execute("SELECT COUNT(*) FROM published_articles WHERE DATE(published_at) = ?", (today,))
                today_count = cursor.fetchone()[0]
                print(f"📈 مقالات اليوم: {today_count}")
                
        else:
            print("❌ قاعدة البيانات غير موجودة")
            
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
    
    # فحص الملفات المهمة
    important_files = [
        "main.py",
        "config/settings.py",
        ".env"
    ]
    
    print("\n📁 الملفات المهمة:")
    for file_path in important_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size:,} بايت)")
        else:
            print(f"❌ {file_path} (مفقود)")

if __name__ == "__main__":
    show_system_status()
