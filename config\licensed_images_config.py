#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات نظام الصور المرخصة
"""

import os
from typing import Dict, List

class LicensedImagesConfig:
    """إعدادات الصور المرخصة"""
    
    # مفاتيح APIs للصور المرخصة
    
    # IGDB API (يتطلب Twitch Client ID & Secret)
    TWITCH_CLIENT_ID = os.getenv('TWITCH_CLIENT_ID', '')
    TWITCH_CLIENT_SECRET = os.getenv('TWITCH_CLIENT_SECRET', '')
    
    # RAWG API
    RAWG_API_KEY = os.getenv('RAWG_API_KEY', '')
    
    # Steam API (اختياري - يمكن العمل بدونه)
    STEAM_API_KEY = os.getenv('STEAM_API_KEY', '')
    
    # إعدادات الأولوية
    PROVIDER_PRIORITY = [
        'press_kits',  # الأولوية الأولى - الأكثر أماناً قانونياً
        'igdb',        # الأولوية الثانية - قاعدة بيانات رسمية
        'rawg',        # الأولوية الثالثة - مجاني مع إشارة
        'steam'        # الأولوية الأخيرة - يتطلب حذر في الاستخدام
    ]
    
    # إعدادات التخزين المؤقت
    CACHE_DURATION_HOURS = 24
    PRESS_KIT_CACHE_DURATION_HOURS = 48  # مدة أطول للـ Press Kits
    
    # حدود الاستخدام
    MAX_IMAGES_PER_GAME = 3
    MAX_DAILY_REQUESTS = 100
    
    # إعدادات الأمان
    REQUIRE_ATTRIBUTION = True
    SAFE_FOR_ADSENSE_ONLY = True
    
    # قائمة مطوري الألعاب المدعومين في Press Kits
    SUPPORTED_DEVELOPERS = {
        'riot_games': {
            'name': 'Riot Games',
            'games': ['league of legends', 'valorant', 'teamfight tactics', 'legends of runeterra'],
            'press_kit_url': 'https://www.riotgames.com/en/press',
            'attribution_required': True,
            'safe_for_commercial': True
        },
        'ubisoft': {
            'name': 'Ubisoft',
            'games': ['assassins creed', 'far cry', 'watch dogs', 'rainbow six', 'ghost recon'],
            'press_kit_url': 'https://www.ubisoft.com/en-us/company/press',
            'attribution_required': True,
            'safe_for_commercial': True
        },
        'cd_projekt_red': {
            'name': 'CD Projekt Red',
            'games': ['cyberpunk 2077', 'the witcher', 'gwent'],
            'press_kit_url': 'https://www.cdprojektred.com/en/media',
            'attribution_required': True,
            'safe_for_commercial': True
        },
        'blizzard': {
            'name': 'Blizzard Entertainment',
            'games': ['world of warcraft', 'overwatch', 'diablo', 'starcraft', 'hearthstone'],
            'press_kit_url': 'https://blizzard.gamespress.com',
            'attribution_required': True,
            'safe_for_commercial': True
        },
        'epic_games': {
            'name': 'Epic Games',
            'games': ['fortnite', 'rocket league', 'fall guys', 'gears of war'],
            'press_kit_url': 'https://www.epicgames.com/site/en-US/news',
            'attribution_required': True,
            'safe_for_commercial': True
        },
        'valve': {
            'name': 'Valve Corporation',
            'games': ['half life', 'portal', 'counter strike', 'dota', 'team fortress'],
            'press_kit_url': 'https://www.valvesoftware.com/en/press',
            'attribution_required': True,
            'safe_for_commercial': True
        },
        'nintendo': {
            'name': 'Nintendo',
            'games': ['mario', 'zelda', 'pokemon', 'metroid', 'donkey kong'],
            'press_kit_url': 'https://press.nintendo.com',
            'attribution_required': True,
            'safe_for_commercial': False  # Nintendo أكثر تشدداً
        },
        'sony': {
            'name': 'Sony Interactive Entertainment',
            'games': ['god of war', 'last of us', 'horizon', 'spider man', 'ghost of tsushima'],
            'press_kit_url': 'https://www.sie.com/en/corporate/release.html',
            'attribution_required': True,
            'safe_for_commercial': False  # Sony أكثر تشدداً
        },
        'microsoft': {
            'name': 'Microsoft Game Studios',
            'games': ['halo', 'gears of war', 'forza', 'age of empires', 'minecraft'],
            'press_kit_url': 'https://news.xbox.com/en-us/press/',
            'attribution_required': True,
            'safe_for_commercial': True
        },
        'activision': {
            'name': 'Activision',
            'games': ['call of duty', 'crash bandicoot', 'spyro', 'tony hawk'],
            'press_kit_url': 'https://www.activision.com/newsroom',
            'attribution_required': True,
            'safe_for_commercial': True
        }
    }
    
    # قوالب الإشارة للمصادر
    ATTRIBUTION_TEMPLATES = {
        'igdb': 'Image provided by IGDB.com',
        'rawg': 'Image provided by RAWG.io',
        'steam': 'Image from Steam Store page for {game_name}',
        'press_kit': 'Official image from {developer} Press Kit',
        'riot_games': 'Official image courtesy of Riot Games',
        'ubisoft': 'Official image courtesy of Ubisoft',
        'cd_projekt_red': 'Official image courtesy of CD Projekt Red',
        'blizzard': 'Official image courtesy of Blizzard Entertainment',
        'epic_games': 'Official image courtesy of Epic Games',
        'valve': 'Official image courtesy of Valve Corporation',
        'nintendo': 'Official image courtesy of Nintendo',
        'sony': 'Official image courtesy of Sony Interactive Entertainment',
        'microsoft': 'Official image courtesy of Microsoft Game Studios',
        'activision': 'Official image courtesy of Activision'
    }
    
    # أنواع الصور المدعومة
    SUPPORTED_IMAGE_TYPES = [
        'screenshot',    # لقطات شاشة
        'cover',        # صور الغلاف
        'artwork',      # الأعمال الفنية
        'key_art',      # الفن الرئيسي
        'promotional',  # صور ترويجية
        'header',       # صور الرأس
        'banner',       # البانرات
        'logo'          # الشعارات
    ]
    
    # إعدادات جودة الصور
    IMAGE_QUALITY_SETTINGS = {
        'min_width': 800,
        'min_height': 600,
        'preferred_width': 1920,
        'preferred_height': 1080,
        'max_file_size_mb': 5,
        'supported_formats': ['jpg', 'jpeg', 'png', 'webp']
    }
    
    # إعدادات الأمان والامتثال
    COMPLIANCE_SETTINGS = {
        'require_safe_for_adsense': True,
        'require_attribution': True,
        'avoid_copyrighted_content': True,
        'prefer_creative_commons': True,
        'check_license_compatibility': True
    }
    
    @classmethod
    def validate_config(cls) -> bool:
        """التحقق من صحة الإعدادات"""
        issues = []
        
        # فحص المفاتيح المطلوبة
        if not cls.TWITCH_CLIENT_ID or not cls.TWITCH_CLIENT_SECRET:
            issues.append("⚠️ مفاتيح Twitch (IGDB) غير متوفرة")
        
        if not cls.RAWG_API_KEY:
            issues.append("⚠️ مفتاح RAWG API غير متوفر")
        
        # فحص الإعدادات
        if cls.MAX_IMAGES_PER_GAME < 1:
            issues.append("❌ MAX_IMAGES_PER_GAME يجب أن يكون أكبر من 0")
        
        if cls.CACHE_DURATION_HOURS < 1:
            issues.append("❌ CACHE_DURATION_HOURS يجب أن يكون أكبر من 0")
        
        # عرض المشاكل
        if issues:
            print("🔧 مشاكل في إعدادات الصور المرخصة:")
            for issue in issues:
                print(f"   {issue}")
            return False
        
        print("✅ إعدادات الصور المرخصة صحيحة")
        return True
    
    @classmethod
    def get_developer_info(cls, game_name: str) -> Dict:
        """الحصول على معلومات المطور بناءً على اسم اللعبة"""
        game_name_lower = game_name.lower()
        
        for dev_key, dev_info in cls.SUPPORTED_DEVELOPERS.items():
            for game in dev_info['games']:
                if game in game_name_lower:
                    return {
                        'developer_key': dev_key,
                        'developer_name': dev_info['name'],
                        'press_kit_url': dev_info['press_kit_url'],
                        'attribution_required': dev_info['attribution_required'],
                        'safe_for_commercial': dev_info['safe_for_commercial']
                    }
        
        return {}
    
    @classmethod
    def get_attribution_text(cls, source: str, game_name: str = '', developer: str = '') -> str:
        """الحصول على نص الإشارة المناسب"""
        template = cls.ATTRIBUTION_TEMPLATES.get(source, 'Image used with permission')
        
        # تخصيص النص حسب المصدر
        if '{game_name}' in template and game_name:
            template = template.format(game_name=game_name)
        
        if '{developer}' in template and developer:
            template = template.format(developer=developer)
        
        return template
    
    @classmethod
    def is_image_quality_acceptable(cls, width: int, height: int, file_size_mb: float = 0) -> bool:
        """فحص جودة الصورة"""
        settings = cls.IMAGE_QUALITY_SETTINGS
        
        # فحص الأبعاد الدنيا
        if width < settings['min_width'] or height < settings['min_height']:
            return False
        
        # فحص حجم الملف
        if file_size_mb > 0 and file_size_mb > settings['max_file_size_mb']:
            return False
        
        return True
    
    @classmethod
    def get_setup_instructions(cls) -> str:
        """الحصول على تعليمات الإعداد"""
        return """
🔧 تعليمات إعداد نظام الصور المرخصة:

1. الحصول على مفاتيح APIs:
   
   أ) IGDB API (مطلوب):
      - اذهب إلى: https://api.igdb.com/
      - سجل حساب Twitch Developer
      - احصل على Client ID و Client Secret
      - ضع المفاتيح في متغيرات البيئة:
        TWITCH_CLIENT_ID=your_client_id
        TWITCH_CLIENT_SECRET=your_client_secret
   
   ب) RAWG API (مطلوب):
      - اذهب إلى: https://rawg.io/apidocs
      - سجل حساب مجاني
      - احصل على API Key
      - ضع المفتاح في متغير البيئة:
        RAWG_API_KEY=your_api_key
   
   ج) Steam API (اختياري):
      - اذهب إلى: https://steamcommunity.com/dev/apikey
      - احصل على API Key
      - ضع المفتاح في متغير البيئة:
        STEAM_API_KEY=your_api_key

2. تفعيل النظام:
   - تأكد من وجود المفاتيح في ملف .env
   - شغل الاختبار: python test_licensed_image_system.py
   - تحقق من النتائج

3. الاستخدام:
   - النظام سيعطي أولوية للصور المرخصة تلقائياً
   - سيتم استخدام الذكاء الاصطناعي كخيار أخير فقط
   - جميع الصور ستكون آمنة لأدسنس

📋 ملاحظات مهمة:
- Press Kits لها الأولوية الأولى (الأكثر أماناً قانونياً)
- IGDB يوفر صور رسمية عالية الجودة
- RAWG مجاني ولكن يتطلب إشارة للمصدر
- Steam يتطلب حذر في الاستخدام التجاري
"""

# إنشاء مثيل للإعدادات
licensed_images_config = LicensedImagesConfig()
