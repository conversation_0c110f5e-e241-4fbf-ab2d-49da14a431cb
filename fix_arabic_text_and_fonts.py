#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة النصوص العربية والخطوط
Fix Arabic text and fonts issues
"""

import os
import shutil
import sys
from pathlib import Path

def copy_existing_fonts():
    """نسخ الخطوط الموجودة إلى المكان الصحيح"""
    
    print("🔤 نسخ الخطوط الموجودة...")
    
    # المسارات
    old_font_dir = "font"
    new_font_dir = "assets/fonts"
    
    # إنشاء المجلدات الجديدة
    os.makedirs(f"{new_font_dir}/arabic", exist_ok=True)
    os.makedirs(f"{new_font_dir}/english", exist_ok=True)
    
    copied_fonts = []
    
    # نسخ الخطوط العربية
    arabic_source = f"{old_font_dir}/arabic"
    arabic_dest = f"{new_font_dir}/arabic"
    
    if os.path.exists(arabic_source):
        for font_file in os.listdir(arabic_source):
            if font_file.lower().endswith(('.ttf', '.otf')):
                source_path = os.path.join(arabic_source, font_file)
                dest_path = os.path.join(arabic_dest, font_file)
                
                try:
                    shutil.copy2(source_path, dest_path)
                    copied_fonts.append(f"عربي: {font_file}")
                    print(f"✅ تم نسخ الخط العربي: {font_file}")
                except Exception as e:
                    print(f"❌ فشل في نسخ {font_file}: {e}")
    
    # نسخ الخطوط الإنجليزية
    english_source = f"{old_font_dir}/Engish"  # لاحظ الخطأ الإملائي في المجلد الأصلي
    english_dest = f"{new_font_dir}/english"
    
    if os.path.exists(english_source):
        for font_file in os.listdir(english_source):
            if font_file.lower().endswith(('.ttf', '.otf')):
                source_path = os.path.join(english_source, font_file)
                dest_path = os.path.join(english_dest, font_file)
                
                try:
                    shutil.copy2(source_path, dest_path)
                    copied_fonts.append(f"إنجليزي: {font_file}")
                    print(f"✅ تم نسخ الخط الإنجليزي: {font_file}")
                except Exception as e:
                    print(f"❌ فشل في نسخ {font_file}: {e}")
    
    print(f"\n📊 تم نسخ {len(copied_fonts)} خط:")
    for font in copied_fonts:
        print(f"  - {font}")
    
    return len(copied_fonts) > 0

def test_arabic_text_rendering():
    """اختبار عرض النصوص العربية"""
    
    print("\n🔤 اختبار عرض النصوص العربية...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        import arabic_reshaper
        from bidi.algorithm import get_display
        
        print("✅ تم تحميل مكتبات معالجة النصوص العربية")
        
        # نص اختبار
        test_text = "سلام عليكم ورحمة الله وبركاته"
        print(f"📝 النص الأصلي: {test_text}")
        
        # معالجة النص العربي
        reshaped_text = arabic_reshaper.reshape(test_text)
        bidi_text = get_display(reshaped_text)
        print(f"🔄 النص بعد المعالجة: {bidi_text}")
        
        return True
        
    except ImportError as e:
        print(f"❌ مكتبات معالجة النصوص العربية غير مثبتة: {e}")
        print("💡 يجب تثبيت: pip install arabic-reshaper python-bidi")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار النصوص العربية: {e}")
        return False

def install_arabic_libraries():
    """تثبيت مكتبات معالجة النصوص العربية"""
    
    print("\n📦 تثبيت مكتبات معالجة النصوص العربية...")
    
    import subprocess
    
    libraries = [
        "arabic-reshaper",
        "python-bidi"
    ]
    
    for lib in libraries:
        try:
            print(f"📦 تثبيت {lib}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", lib], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ تم تثبيت {lib} بنجاح")
            else:
                print(f"❌ فشل في تثبيت {lib}: {result.stderr}")
        except Exception as e:
            print(f"❌ خطأ في تثبيت {lib}: {e}")

def create_test_image_with_arabic():
    """إنشاء صورة اختبار مع نص عربي صحيح"""
    
    print("\n🎨 إنشاء صورة اختبار مع نص عربي...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        import arabic_reshaper
        from bidi.algorithm import get_display
        
        # إنشاء صورة اختبار
        img = Image.new('RGB', (800, 400), color=(70, 130, 180))
        draw = ImageDraw.Draw(img)
        
        # النص العربي للاختبار
        arabic_text = "مرحباً بكم في نظام إنشاء الصور المحسن"
        english_text = "Welcome to Enhanced Image Generation System"
        
        # معالجة النص العربي
        reshaped_text = arabic_reshaper.reshape(arabic_text)
        bidi_text = get_display(reshaped_text)
        
        # محاولة استخدام خط عربي
        font_path = None
        arabic_fonts = [
            "assets/fonts/arabic/AligarhArabicFREEPERSONALUSE-Black.otf",
            "assets/fonts/arabic/NaveidArabicDEMO-ExtraBold.otf",
            "C:/Windows/Fonts/tahoma.ttf",
            "C:/Windows/Fonts/arial.ttf"
        ]
        
        for font_file in arabic_fonts:
            if os.path.exists(font_file):
                font_path = font_file
                break
        
        if font_path:
            try:
                font_arabic = ImageFont.truetype(font_path, 32)
                font_english = ImageFont.truetype(font_path, 24)
                print(f"✅ تم تحميل الخط: {os.path.basename(font_path)}")
            except:
                font_arabic = ImageFont.load_default()
                font_english = ImageFont.load_default()
                print("⚠️ استخدام الخط الافتراضي")
        else:
            font_arabic = ImageFont.load_default()
            font_english = ImageFont.load_default()
            print("⚠️ لم يتم العثور على خطوط، استخدام الخط الافتراضي")
        
        # رسم النص العربي المعالج
        draw.text((50, 150), bidi_text, font=font_arabic, fill=(255, 255, 255))
        draw.text((50, 200), english_text, font=font_english, fill=(255, 255, 255))
        
        # حفظ الصورة
        test_image_path = "test_arabic_text.png"
        img.save(test_image_path)
        print(f"✅ تم حفظ صورة الاختبار: {test_image_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء صورة الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إصلاح مشكلة النصوص العربية والخطوط")
    print("=" * 60)
    
    # 1. نسخ الخطوط الموجودة
    fonts_copied = copy_existing_fonts()
    
    # 2. تثبيت مكتبات معالجة النصوص العربية
    install_arabic_libraries()
    
    # 3. اختبار عرض النصوص العربية
    arabic_test = test_arabic_text_rendering()
    
    # 4. إنشاء صورة اختبار
    if arabic_test:
        test_image = create_test_image_with_arabic()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print(f"✅ نسخ الخطوط: {'نجح' if fonts_copied else 'فشل'}")
    print(f"✅ مكتبات النصوص العربية: {'متاحة' if arabic_test else 'غير متاحة'}")
    
    if arabic_test and fonts_copied:
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("💡 الآن يمكن للنظام عرض النصوص العربية بشكل صحيح")
    else:
        print("\n⚠️ بعض المشاكل تحتاج إلى حل يدوي")
        if not arabic_test:
            print("   - قم بتثبيت: pip install arabic-reshaper python-bidi")
        if not fonts_copied:
            print("   - تأكد من وجود الخطوط في مجلد font/")

if __name__ == "__main__":
    main()
