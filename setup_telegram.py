#!/usr/bin/env python3
"""
دليل إعداد تيليجرام خطوة بخطوة
"""

import asyncio
from telegram import Bot
from config.settings import BotConfig

async def setup_telegram():
    """دليل إعداد تيليجرام خطوة بخطوة"""
    print("🚀 مرحباً بك في دليل إعداد تيليجرام!")
    print("=" * 60)
    
    # معلومات البوت
    print(f"\n📱 معلومات البوت الحالي:")
    print(f"   🤖 اسم البوت: @{BotConfig.TELEGRAM_BOT_USERNAME}")
    print(f"   🔗 رابط البوت: https://t.me/{BotConfig.TELEGRAM_BOT_USERNAME.replace('@', '')}")
    print(f"   📺 القناة: {BotConfig.TELEGRAM_CHANNEL_URL}")
    
    # اختبار البوت
    print(f"\n1️⃣ اختبار البوت...")
    try:
        bot = Bot(token=BotConfig.TELEGRAM_BOT_TOKEN)
        bot_info = await bot.get_me()
        print(f"✅ البوت متصل: @{bot_info.username}")
    except Exception as e:
        print(f"❌ مشكلة في البوت: {e}")
        print("💡 تحقق من TELEGRAM_BOT_TOKEN في config/settings.py")
        return
    
    # اختبار القناة
    print(f"\n2️⃣ اختبار القناة...")
    try:
        chat_info = await bot.get_chat(BotConfig.TELEGRAM_CHANNEL_ID)
        print(f"✅ القناة موجودة: {chat_info.title}")
        
        # اختبار صلاحيات البوت
        try:
            await bot.send_chat_action(chat_id=BotConfig.TELEGRAM_CHANNEL_ID, action="typing")
            print("✅ البوت لديه صلاحية النشر")
            channel_ready = True
        except Exception as perm_error:
            print(f"❌ البوت لا يملك صلاحية النشر")
            channel_ready = False
            
    except Exception as e:
        print(f"❌ مشكلة في القناة: {e}")
        channel_ready = False
    
    # إرشادات إصلاح القناة
    if not channel_ready:
        print(f"\n🔧 لإصلاح مشكلة القناة:")
        print(f"   1. اذهب إلى القناة: {BotConfig.TELEGRAM_CHANNEL_URL}")
        print(f"   2. اضغط على اسم القناة في الأعلى")
        print(f"   3. اختر 'Manage Channel' أو 'إدارة القناة'")
        print(f"   4. اختر 'Administrators' أو 'المديرين'")
        print(f"   5. اضغط 'Add Admin' أو 'إضافة مدير'")
        print(f"   6. ابحث عن: @{bot_info.username}")
        print(f"   7. أضف البوت وامنحه صلاحية 'Post Messages'")
        print(f"   8. احفظ التغييرات")
        
        input("\n⏸️ اضغط Enter بعد إضافة البوت للقناة...")
        
        # إعادة اختبار القناة
        print("\n🔄 إعادة اختبار القناة...")
        try:
            await bot.send_chat_action(chat_id=BotConfig.TELEGRAM_CHANNEL_ID, action="typing")
            print("✅ تم إصلاح مشكلة القناة!")
            channel_ready = True
        except:
            print("❌ لا تزال هناك مشكلة في القناة")
            print("💡 تأكد من إضافة البوت كمدير مع صلاحية النشر")
    
    # اختبار معرف المدير
    print(f"\n3️⃣ اختبار معرف المدير...")
    admin_id = BotConfig.TELEGRAM_ADMIN_ID
    print(f"   🆔 المعرف الحالي: {admin_id}")
    
    if admin_id.startswith('@'):
        print("⚠️ المعرف يبدأ بـ @ (اسم مستخدم)")
        print("💡 يُفضل استخدام المعرف الرقمي")
        
        print(f"\n🔧 لإصلاح معرف المدير:")
        print(f"   1. اذهب إلى البوت: https://t.me/{bot_info.username}")
        print(f"   2. أرسل أي رسالة للبوت (مثل: /start)")
        print(f"   3. شغل: python fix_admin_id.py")
        
        input("\n⏸️ اضغط Enter بعد إرسال رسالة للبوت...")
        
        # محاولة إصلاح المعرف
        print("\n🔄 محاولة إصلاح معرف المدير...")
        updates = await bot.get_updates(limit=10)
        
        if updates:
            for update in reversed(updates):
                if update.message and update.message.from_user:
                    chat_id = update.message.chat_id
                    username = update.message.from_user.username
                    
                    if username and f"@{username}" == admin_id:
                        print(f"✅ تم العثور على المعرف الرقمي: {chat_id}")
                        print(f"💡 أضف هذا السطر في config/settings.py:")
                        print(f"TELEGRAM_ADMIN_ID = \"{chat_id}\"")
                        break
        else:
            print("❌ لم يتم العثور على رسائل حديثة")
    else:
        # اختبار المعرف الرقمي
        try:
            await bot.send_chat_action(chat_id=admin_id, action="typing")
            print("✅ معرف المدير صالح")
        except Exception as admin_error:
            print(f"❌ مشكلة في معرف المدير: {admin_error}")
            print("💡 شغل: python get_admin_id.py للحصول على المعرف الصحيح")
    
    # اختبار نهائي
    if channel_ready:
        print(f"\n4️⃣ اختبار النشر...")
        try:
            test_message = "🧪 اختبار النظام - سيتم حذف هذه الرسالة"
            message = await bot.send_message(
                chat_id=BotConfig.TELEGRAM_CHANNEL_ID,
                text=test_message
            )
            print("✅ تم النشر بنجاح!")
            
            # حذف الرسالة
            await asyncio.sleep(5)
            await bot.delete_message(
                chat_id=BotConfig.TELEGRAM_CHANNEL_ID,
                message_id=message.message_id
            )
            print("✅ تم حذف رسالة الاختبار")
            
        except Exception as publish_error:
            print(f"❌ فشل في النشر: {publish_error}")
    
    # النتيجة النهائية
    print(f"\n" + "=" * 60)
    if channel_ready:
        print("🎉 تم إعداد تيليجرام بنجاح!")
        print("✅ النظام جاهز للعمل")
        print("🚀 يمكنك الآن تشغيل: python main.py")
    else:
        print("❌ يوجد مشاكل تحتاج إصلاح")
        print("🔧 اتبع الإرشادات أعلاه وأعد تشغيل هذا السكريبت")
    
    print(f"\n📚 للمزيد من المساعدة:")
    print(f"   📖 اقرأ: TELEGRAM_FIX_GUIDE.md")
    print(f"   🧪 شغل: python test_telegram_system.py")

if __name__ == "__main__":
    asyncio.run(setup_telegram())
