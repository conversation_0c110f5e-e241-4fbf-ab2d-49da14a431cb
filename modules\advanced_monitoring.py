#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المراقبة المتقدم للوكيل
"""

import asyncio
import sqlite3
import json
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import threading
from dataclasses import dataclass

from .logger import logger

@dataclass
class SystemMetrics:
    """مقاييس النظام"""
    cpu_percent: float
    memory_percent: float
    disk_usage: float
    network_io: Dict
    process_count: int
    uptime: float

@dataclass
class AgentMetrics:
    """مقاييس الوكيل"""
    articles_published_today: int
    articles_published_hour: int
    avg_processing_time: float
    success_rate: float
    error_count: int
    api_calls_count: int
    cache_hit_rate: float

class AdvancedMonitoring:
    """نظام المراقبة المتقدم"""
    
    def __init__(self):
        self.db_path = "data/articles.db"
        self.monitoring_active = False
        self.monitoring_thread = None
        self.metrics_history = []
        self.alerts = []
        self.start_time = time.time()
        
        # عتبات التنبيه
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage': 90.0,
            'error_rate': 10.0,
            'success_rate': 80.0,
            'response_time': 30.0
        }
        
        self._init_monitoring_tables()
    
    def _init_monitoring_tables(self):
        """إنشاء جداول المراقبة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول مقاييس النظام
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        cpu_percent REAL,
                        memory_percent REAL,
                        disk_usage REAL,
                        network_io TEXT,
                        process_count INTEGER,
                        uptime REAL
                    )
                ''')
                
                # جدول مقاييس الوكيل
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS agent_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        articles_published_today INTEGER,
                        articles_published_hour INTEGER,
                        avg_processing_time REAL,
                        success_rate REAL,
                        error_count INTEGER,
                        api_calls_count INTEGER,
                        cache_hit_rate REAL
                    )
                ''')
                
                # جدول التنبيهات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS monitoring_alerts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        alert_type TEXT,
                        severity TEXT,
                        message TEXT,
                        metric_value REAL,
                        threshold_value REAL,
                        resolved BOOLEAN DEFAULT FALSE,
                        resolved_at TIMESTAMP
                    )
                ''')
                
                conn.commit()
                logger.info("✅ تم إنشاء جداول المراقبة المتقدمة")
                
        except Exception as e:
            logger.error("❌ فشل في إنشاء جداول المراقبة", e)
    
    def start_monitoring(self):
        """بدء المراقبة"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            logger.info("🔍 تم بدء نظام المراقبة المتقدم")
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("⏹️ تم إيقاف نظام المراقبة")
    
    def _monitoring_loop(self):
        """حلقة المراقبة الرئيسية"""
        while self.monitoring_active:
            try:
                # جمع مقاييس النظام
                system_metrics = self._collect_system_metrics()
                
                # جمع مقاييس الوكيل
                agent_metrics = self._collect_agent_metrics()
                
                # حفظ المقاييس
                self._save_metrics(system_metrics, agent_metrics)
                
                # فحص التنبيهات
                self._check_alerts(system_metrics, agent_metrics)
                
                # تنظيف البيانات القديمة
                self._cleanup_old_data()
                
                # انتظار 60 ثانية قبل القياس التالي
                time.sleep(60)
                
            except Exception as e:
                logger.error("❌ خطأ في حلقة المراقبة", e)
                time.sleep(30)  # انتظار أقصر في حالة الخطأ
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """جمع مقاييس النظام"""
        try:
            # معلومات المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # معلومات الذاكرة
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # معلومات القرص
            disk = psutil.disk_usage('/')
            disk_usage = (disk.used / disk.total) * 100
            
            # معلومات الشبكة
            network = psutil.net_io_counters()
            network_io = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            # عدد العمليات
            process_count = len(psutil.pids())
            
            # وقت التشغيل
            uptime = time.time() - self.start_time
            
            return SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_usage=disk_usage,
                network_io=network_io,
                process_count=process_count,
                uptime=uptime
            )
            
        except Exception as e:
            logger.error("❌ فشل في جمع مقاييس النظام", e)
            return SystemMetrics(0, 0, 0, {}, 0, 0)
    
    def _collect_agent_metrics(self) -> AgentMetrics:
        """جمع مقاييس الوكيل"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # مقالات اليوم
                cursor.execute('''
                    SELECT COUNT(*) FROM published_articles 
                    WHERE published_at >= datetime('now', 'start of day')
                ''')
                articles_today = cursor.fetchone()[0]
                
                # مقالات الساعة الماضية
                cursor.execute('''
                    SELECT COUNT(*) FROM published_articles 
                    WHERE published_at >= datetime('now', '-1 hour')
                ''')
                articles_hour = cursor.fetchone()[0]
                
                # متوسط وقت المعالجة (محاكاة)
                avg_processing_time = 15.5  # ثانية
                
                # معدل النجاح
                cursor.execute('''
                    SELECT COUNT(*) FROM published_articles 
                    WHERE published_at >= datetime('now', '-24 hours')
                ''')
                successful_operations = cursor.fetchone()[0]
                
                # عدد الأخطاء (من جدول الأخطاء إذا كان موجوداً)
                error_count = 0
                try:
                    cursor.execute('''
                        SELECT COUNT(*) FROM error_logs 
                        WHERE timestamp >= datetime('now', '-24 hours')
                    ''')
                    error_count = cursor.fetchone()[0]
                except:
                    pass
                
                # حساب معدل النجاح
                total_operations = successful_operations + error_count
                success_rate = (successful_operations / total_operations * 100) if total_operations > 0 else 100
                
                # عدد استدعاءات API (محاكاة)
                api_calls_count = successful_operations * 3  # تقدير
                
                # معدل إصابة الكاش
                try:
                    from .performance_optimizer import performance_optimizer
                    stats = performance_optimizer.get_stats()
                    cache_hit_rate = stats.get('cache_hit_rate', 0)
                except:
                    cache_hit_rate = 0
                
                return AgentMetrics(
                    articles_published_today=articles_today,
                    articles_published_hour=articles_hour,
                    avg_processing_time=avg_processing_time,
                    success_rate=success_rate,
                    error_count=error_count,
                    api_calls_count=api_calls_count,
                    cache_hit_rate=cache_hit_rate
                )
                
        except Exception as e:
            logger.error("❌ فشل في جمع مقاييس الوكيل", e)
            return AgentMetrics(0, 0, 0, 0, 0, 0, 0)
    
    def _save_metrics(self, system_metrics: SystemMetrics, agent_metrics: AgentMetrics):
        """حفظ المقاييس في قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # حفظ مقاييس النظام
                cursor.execute('''
                    INSERT INTO system_metrics 
                    (cpu_percent, memory_percent, disk_usage, network_io, process_count, uptime)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    system_metrics.cpu_percent,
                    system_metrics.memory_percent,
                    system_metrics.disk_usage,
                    json.dumps(system_metrics.network_io),
                    system_metrics.process_count,
                    system_metrics.uptime
                ))
                
                # حفظ مقاييس الوكيل
                cursor.execute('''
                    INSERT INTO agent_metrics 
                    (articles_published_today, articles_published_hour, avg_processing_time,
                     success_rate, error_count, api_calls_count, cache_hit_rate)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    agent_metrics.articles_published_today,
                    agent_metrics.articles_published_hour,
                    agent_metrics.avg_processing_time,
                    agent_metrics.success_rate,
                    agent_metrics.error_count,
                    agent_metrics.api_calls_count,
                    agent_metrics.cache_hit_rate
                ))
                
                conn.commit()
                
        except Exception as e:
            logger.error("❌ فشل في حفظ المقاييس", e)
    
    def _check_alerts(self, system_metrics: SystemMetrics, agent_metrics: AgentMetrics):
        """فحص التنبيهات"""
        try:
            alerts_to_create = []
            
            # فحص تنبيهات النظام
            if system_metrics.cpu_percent > self.alert_thresholds['cpu_percent']:
                alerts_to_create.append({
                    'type': 'high_cpu',
                    'severity': 'warning',
                    'message': f'استخدام المعالج مرتفع: {system_metrics.cpu_percent:.1f}%',
                    'value': system_metrics.cpu_percent,
                    'threshold': self.alert_thresholds['cpu_percent']
                })
            
            if system_metrics.memory_percent > self.alert_thresholds['memory_percent']:
                alerts_to_create.append({
                    'type': 'high_memory',
                    'severity': 'warning',
                    'message': f'استخدام الذاكرة مرتفع: {system_metrics.memory_percent:.1f}%',
                    'value': system_metrics.memory_percent,
                    'threshold': self.alert_thresholds['memory_percent']
                })
            
            if system_metrics.disk_usage > self.alert_thresholds['disk_usage']:
                alerts_to_create.append({
                    'type': 'high_disk',
                    'severity': 'critical',
                    'message': f'مساحة القرص منخفضة: {system_metrics.disk_usage:.1f}%',
                    'value': system_metrics.disk_usage,
                    'threshold': self.alert_thresholds['disk_usage']
                })
            
            # فحص تنبيهات الوكيل
            if agent_metrics.success_rate < self.alert_thresholds['success_rate']:
                alerts_to_create.append({
                    'type': 'low_success_rate',
                    'severity': 'warning',
                    'message': f'معدل النجاح منخفض: {agent_metrics.success_rate:.1f}%',
                    'value': agent_metrics.success_rate,
                    'threshold': self.alert_thresholds['success_rate']
                })
            
            if agent_metrics.avg_processing_time > self.alert_thresholds['response_time']:
                alerts_to_create.append({
                    'type': 'slow_processing',
                    'severity': 'warning',
                    'message': f'وقت المعالجة بطيء: {agent_metrics.avg_processing_time:.1f}s',
                    'value': agent_metrics.avg_processing_time,
                    'threshold': self.alert_thresholds['response_time']
                })
            
            # إنشاء التنبيهات
            for alert in alerts_to_create:
                self._create_alert(alert)
                
        except Exception as e:
            logger.error("❌ فشل في فحص التنبيهات", e)
    
    def _create_alert(self, alert: Dict):
        """إنشاء تنبيه"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO monitoring_alerts 
                    (alert_type, severity, message, metric_value, threshold_value)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    alert['type'],
                    alert['severity'],
                    alert['message'],
                    alert['value'],
                    alert['threshold']
                ))
                
                conn.commit()
                
                # تسجيل التنبيه
                if alert['severity'] == 'critical':
                    logger.critical(f"🚨 تنبيه حرج: {alert['message']}")
                else:
                    logger.warning(f"⚠️ تنبيه: {alert['message']}")
                
        except Exception as e:
            logger.error("❌ فشل في إنشاء التنبيه", e)
    
    def _cleanup_old_data(self):
        """تنظيف البيانات القديمة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # حذف مقاييس النظام الأقدم من 7 أيام
                cursor.execute('''
                    DELETE FROM system_metrics 
                    WHERE timestamp < datetime('now', '-7 days')
                ''')
                
                # حذف مقاييس الوكيل الأقدم من 30 يوم
                cursor.execute('''
                    DELETE FROM agent_metrics 
                    WHERE timestamp < datetime('now', '-30 days')
                ''')
                
                # حذف التنبيهات المحلولة الأقدم من 7 أيام
                cursor.execute('''
                    DELETE FROM monitoring_alerts 
                    WHERE resolved = TRUE AND resolved_at < datetime('now', '-7 days')
                ''')
                
                conn.commit()
                
        except Exception as e:
            logger.error("❌ فشل في تنظيف البيانات القديمة", e)
    
    def get_current_status(self) -> Dict:
        """الحصول على الحالة الحالية"""
        try:
            system_metrics = self._collect_system_metrics()
            agent_metrics = self._collect_agent_metrics()
            
            # الحصول على التنبيهات النشطة
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT COUNT(*) FROM monitoring_alerts 
                    WHERE resolved = FALSE AND timestamp >= datetime('now', '-1 hour')
                ''')
                active_alerts = cursor.fetchone()[0]
            
            # تحديد الحالة العامة
            overall_status = "healthy"
            if active_alerts > 0:
                overall_status = "warning"
            if system_metrics.cpu_percent > 90 or system_metrics.memory_percent > 90:
                overall_status = "critical"
            
            return {
                'overall_status': overall_status,
                'system_metrics': {
                    'cpu_percent': system_metrics.cpu_percent,
                    'memory_percent': system_metrics.memory_percent,
                    'disk_usage': system_metrics.disk_usage,
                    'uptime': system_metrics.uptime
                },
                'agent_metrics': {
                    'articles_today': agent_metrics.articles_published_today,
                    'articles_hour': agent_metrics.articles_published_hour,
                    'success_rate': agent_metrics.success_rate,
                    'cache_hit_rate': agent_metrics.cache_hit_rate
                },
                'active_alerts': active_alerts,
                'monitoring_active': self.monitoring_active,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("❌ فشل في الحصول على الحالة الحالية", e)
            return {'error': str(e)}
    
    def generate_monitoring_report(self) -> str:
        """إنشاء تقرير مراقبة شامل"""
        try:
            status = self.get_current_status()
            
            report = f"""
🔍 تقرير المراقبة المتقدم - {datetime.now().strftime('%Y-%m-%d %H:%M')}
{'='*60}

🟢 الحالة العامة: {status.get('overall_status', 'unknown').upper()}

💻 مقاييس النظام:
   🔥 المعالج: {status.get('system_metrics', {}).get('cpu_percent', 0):.1f}%
   💾 الذاكرة: {status.get('system_metrics', {}).get('memory_percent', 0):.1f}%
   💿 القرص: {status.get('system_metrics', {}).get('disk_usage', 0):.1f}%
   ⏱️ وقت التشغيل: {status.get('system_metrics', {}).get('uptime', 0)/3600:.1f} ساعة

🤖 مقاييس الوكيل:
   📰 مقالات اليوم: {status.get('agent_metrics', {}).get('articles_today', 0)}
   📈 مقالات الساعة: {status.get('agent_metrics', {}).get('articles_hour', 0)}
   ✅ معدل النجاح: {status.get('agent_metrics', {}).get('success_rate', 0):.1f}%
   🎯 معدل إصابة الكاش: {status.get('agent_metrics', {}).get('cache_hit_rate', 0):.1f}%

🚨 التنبيهات النشطة: {status.get('active_alerts', 0)}
🔍 حالة المراقبة: {'نشط' if status.get('monitoring_active', False) else 'متوقف'}

{'='*60}
            """
            
            return report
            
        except Exception as e:
            logger.error("❌ فشل في إنشاء تقرير المراقبة", e)
            return f"❌ خطأ في إنشاء التقرير: {e}"

# إنشاء مثيل عام لنظام المراقبة المتقدم
advanced_monitoring = AdvancedMonitoring()
