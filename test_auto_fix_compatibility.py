#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الإصلاح التلقائي للتوافق بين العنوان والمحتوى
"""

import sys
import os
import json
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.content_generator import ContentGenerator

class AutoFixCompatibilityTest:
    """اختبار نظام الإصلاح التلقائي"""
    
    def __init__(self):
        self.content_generator = ContentGenerator()
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': [],
            'summary': {}
        }
    
    def run_comprehensive_test(self):
        """تشغيل اختبار شامل لنظام الإصلاح التلقائي"""
        logger.info("🧪 بدء اختبار نظام الإصلاح التلقائي للتوافق...")
        
        # حالات اختبار مختلفة
        test_cases = [
            {
                'name': 'مفاهيم مفقودة في المحتوى',
                'title': 'دليل المبتدئين الشامل لألعاب الفيديو',
                'content': 'الألعاب ممتعة ومسلية. يمكن للجميع الاستمتاع بها.',
                'expected_fix': 'enhance_content'
            },
            {
                'name': 'عنوان طويل مع مفاهيم غير موجودة',
                'title': 'مراجعة شاملة ومفصلة للمبتدئين والمحترفين لأفضل ألعاب الاستراتيجية المجانية',
                'content': 'هذه لعبة رائعة تحتوي على عناصر أكشن مثيرة. القتال ممتع والرسوميات جميلة.',
                'expected_fix': 'adjust_title'
            },
            {
                'name': 'عدم تطابق كامل',
                'title': 'نصائح للمبتدئين في Minecraft',
                'content': 'Call of Duty هي لعبة إطلاق نار رائعة. تحتوي على أسلحة متنوعة ومعارك مثيرة.',
                'expected_fix': 'both'
            },
            {
                'name': 'تطابق جزئي يحتاج تحسين',
                'title': 'مراجعة لعبة Stardew Valley',
                'content': 'Stardew Valley لعبة جميلة. الزراعة ممتعة.',
                'expected_fix': 'enhance_content'
            },
            {
                'name': 'مقال جيد لا يحتاج إصلاح',
                'title': 'مراجعة شاملة للعبة Cyberpunk 2077',
                'content': 'Cyberpunk 2077 هي لعبة رائعة تقدم تجربة مراجعة شاملة. هذه مراجعة مفصلة للعبة مع تحليل جميع الجوانب.',
                'expected_fix': 'none'
            }
        ]
        
        successful_fixes = 0
        total_tests = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"🔍 اختبار {i}/{total_tests}: {test_case['name']}")
            
            result = self.test_single_case(test_case)
            self.test_results['tests'].append(result)
            
            if result['success']:
                successful_fixes += 1
                logger.info(f"✅ نجح الاختبار: {result['fix_type']}")
            else:
                logger.warning(f"❌ فشل الاختبار: {result.get('error', 'غير محدد')}")
        
        # حساب الإحصائيات
        success_rate = successful_fixes / total_tests
        self.test_results['summary'] = {
            'total_tests': total_tests,
            'successful_fixes': successful_fixes,
            'success_rate': success_rate,
            'overall_success': success_rate >= 0.8
        }
        
        # عرض النتائج
        self.display_results()
        
        # حفظ النتائج
        self.save_results()
        
        return self.test_results
    
    def test_single_case(self, test_case):
        """اختبار حالة واحدة"""
        try:
            title = test_case['title']
            content = test_case['content']
            expected_fix = test_case['expected_fix']
            
            # فحص التوافق الأولي
            initial_analysis = self.content_generator._analyze_title_content_match(title, content)
            
            # تطبيق الإصلاح التلقائي
            source_content = {'keywords': [], 'summary': content}
            fix_result = self.content_generator._ensure_title_content_compatibility(
                title, content, source_content
            )
            
            # فحص التوافق بعد الإصلاح
            final_analysis = self.content_generator._analyze_title_content_match(
                fix_result['title'], fix_result['content']
            )
            
            # تقييم النتيجة
            success = self.evaluate_fix_result(fix_result, expected_fix, initial_analysis, final_analysis)
            
            return {
                'test_name': test_case['name'],
                'original_title': title,
                'original_content': content[:100] + "...",
                'expected_fix': expected_fix,
                'fix_type': fix_result.get('fix_type', 'none'),
                'was_fixed': fix_result.get('was_fixed', False),
                'final_title': fix_result['title'],
                'final_content': fix_result['content'][:100] + "...",
                'initial_match_ratio': initial_analysis.get('match_ratio', 0),
                'final_match_ratio': final_analysis.get('match_ratio', 0),
                'improvement': final_analysis.get('match_ratio', 0) - initial_analysis.get('match_ratio', 0),
                'success': success
            }
            
        except Exception as e:
            return {
                'test_name': test_case['name'],
                'success': False,
                'error': str(e)
            }
    
    def evaluate_fix_result(self, fix_result, expected_fix, initial_analysis, final_analysis):
        """تقييم نتيجة الإصلاح"""
        try:
            # فحص إذا كان الإصلاح مطلوب أصلاً
            if expected_fix == 'none':
                return not fix_result.get('was_fixed', False) or final_analysis.get('match_ratio', 0) >= 0.8
            
            # فحص إذا تم تحسين التوافق
            improvement = final_analysis.get('match_ratio', 0) - initial_analysis.get('match_ratio', 0)
            
            # فحص إذا كان نوع الإصلاح مناسب
            fix_type = fix_result.get('fix_type', 'none')
            appropriate_fix = (
                fix_type == expected_fix or 
                fix_type == 'both' or 
                (expected_fix in ['enhance_content', 'adjust_title'] and fix_type in ['enhance_content', 'adjust_title'])
            )
            
            # معايير النجاح
            return (
                fix_result.get('was_fixed', False) and  # تم الإصلاح
                improvement > 0.1 and  # تحسن ملحوظ
                final_analysis.get('match_ratio', 0) >= 0.6 and  # توافق مقبول
                appropriate_fix  # نوع إصلاح مناسب
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في تقييم الإصلاح: {e}")
            return False
    
    def display_results(self):
        """عرض النتائج"""
        print("\n" + "="*70)
        print("📊 نتائج اختبار نظام الإصلاح التلقائي للتوافق")
        print("="*70)
        
        summary = self.test_results['summary']
        print(f"🎯 معدل النجاح: {summary['success_rate']:.1%}")
        print(f"✅ الاختبارات الناجحة: {summary['successful_fixes']}/{summary['total_tests']}")
        
        if summary['overall_success']:
            print("🎉 النتيجة الإجمالية: نظام الإصلاح يعمل بكفاءة!")
        else:
            print("⚠️ النتيجة الإجمالية: نظام الإصلاح يحتاج تحسين")
        
        print("\n📋 تفاصيل الاختبارات:")
        for test in self.test_results['tests']:
            status = "✅" if test['success'] else "❌"
            improvement = test.get('improvement', 0)
            fix_type = test.get('fix_type', 'none')
            
            print(f"  {status} {test['test_name']}")
            print(f"     نوع الإصلاح: {fix_type}")
            print(f"     التحسن: {improvement:+.2f}")
            print(f"     التوافق النهائي: {test.get('final_match_ratio', 0):.1%}")
            
            if not test['success'] and 'error' in test:
                print(f"     خطأ: {test['error']}")
            print()
        
        print("="*70)
    
    def save_results(self):
        """حفظ النتائج"""
        try:
            filename = f"auto_fix_compatibility_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False, default=str)
            logger.info(f"💾 تم حفظ نتائج الاختبار في: {filename}")
        except Exception as e:
            logger.error(f"❌ فشل في حفظ النتائج: {e}")

def main():
    """الدالة الرئيسية"""
    tester = AutoFixCompatibilityTest()
    results = tester.run_comprehensive_test()
    
    if results['summary']['overall_success']:
        print("\n🎉 نظام الإصلاح التلقائي يعمل بكفاءة عالية!")
        return 0
    else:
        print("\n⚠️ نظام الإصلاح التلقائي يحتاج تحسينات.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
