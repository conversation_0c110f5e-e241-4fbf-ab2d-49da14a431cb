#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النصوص المختلطة (عربي + إنجليزي)
Test Mixed Language Text (Arabic + English)
"""

import asyncio
import os
import sys
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.manual_image_generator import ManualImageGenerator
from modules.logger import logger

class MixedLanguageTextTester:
    """فئة اختبار النصوص المختلطة"""
    
    def __init__(self):
        self.generator = ManualImageGenerator("اختبار النصوص المختلطة")
        self.output_dir = "test_results/mixed_language_text"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # مقالات اختبار مع نصوص مختلطة
        self.test_articles = [
            {
                'title': 'مراجعة شاملة للعبة Call of Duty Modern Warfare',
                'content': 'لعبة أكشن رائعة مع جرافيك مذهل',
                'expected_mixed': True,
                'expected_primary': 'arabic'
            },
            {
                'title': 'PlayStation 5 Pro يحصل على تحديث جديد',
                'content': 'سوني تطلق تحديث جديد للجهاز',
                'expected_mixed': True,
                'expected_primary': 'arabic'
            },
            {
                'title': 'Nintendo Switch 2 Announcement مؤتمر الإعلان',
                'content': 'نينتندو تعلن عن الجيل الجديد',
                'expected_mixed': True,
                'expected_primary': 'english'
            },
            {
                'title': 'FIFA 24 Ultimate Team أفضل الفرق',
                'content': 'دليل شامل لبناء أقوى الفرق',
                'expected_mixed': True,
                'expected_primary': 'arabic'
            },
            {
                'title': 'Forza Horizon 5 سباق السيارات المثير',
                'content': 'تجربة سباق لا تُنسى',
                'expected_mixed': True,
                'expected_primary': 'arabic'
            },
            {
                'title': 'أفضل ألعاب الكمبيوتر لعام 2024',
                'content': 'قائمة شاملة بأروع الألعاب',
                'expected_mixed': False,
                'expected_primary': 'arabic'
            },
            {
                'title': 'Best PC Games of 2024',
                'content': 'Complete list of amazing games',
                'expected_mixed': False,
                'expected_primary': 'english'
            },
            {
                'title': 'Cyberpunk 2077 Phantom Liberty DLC مراجعة التوسعة',
                'content': 'توسعة رائعة تضيف محتوى جديد',
                'expected_mixed': True,
                'expected_primary': 'arabic'
            },
            {
                'title': 'League of Legends World Championship بطولة العالم',
                'content': 'أكبر بطولة للرياضات الإلكترونية',
                'expected_mixed': True,
                'expected_primary': 'arabic'
            },
            {
                'title': 'Steam Deck OLED الجيل الجديد من أجهزة المحمولة',
                'content': 'جهاز محمول متطور للألعاب',
                'expected_mixed': True,
                'expected_primary': 'arabic'
            }
        ]
    
    async def test_mixed_language_detection(self):
        """اختبار تحديد النصوص المختلطة"""
        logger.info("🔍 اختبار تحديد النصوص المختلطة...")
        
        results = {
            'total_tests': len(self.test_articles),
            'correct_detections': 0,
            'incorrect_detections': 0,
            'details': []
        }
        
        for article in self.test_articles:
            title = article['title']
            expected_mixed = article['expected_mixed']
            expected_primary = article['expected_primary']
            
            # تحليل النص
            analysis = self.generator.detect_mixed_language_text(title)
            
            # فحص النتائج
            mixed_correct = analysis['is_mixed'] == expected_mixed
            primary_correct = analysis['primary_language'] == expected_primary
            is_correct = mixed_correct and primary_correct
            
            if is_correct:
                results['correct_detections'] += 1
            else:
                results['incorrect_detections'] += 1
            
            results['details'].append({
                'title': title,
                'expected_mixed': expected_mixed,
                'detected_mixed': analysis['is_mixed'],
                'expected_primary': expected_primary,
                'detected_primary': analysis['primary_language'],
                'arabic_text': analysis['arabic_text'],
                'english_text': analysis['english_text'],
                'correct': is_correct
            })
            
            status = '✅' if is_correct else '❌'
            logger.info(f"{status} {title[:40]}...")
            logger.info(f"   مختلط: {analysis['is_mixed']} (متوقع: {expected_mixed})")
            logger.info(f"   أساسي: {analysis['primary_language']} (متوقع: {expected_primary})")
            if analysis['is_mixed']:
                logger.info(f"   عربي: {analysis['arabic_text'][:30]}...")
                logger.info(f"   إنجليزي: {analysis['english_text'][:30]}...")
        
        accuracy = (results['correct_detections'] / results['total_tests']) * 100
        logger.info(f"🎯 دقة تحديد النصوص المختلطة: {accuracy:.1f}%")
        
        return results
    
    async def test_mixed_language_image_generation(self):
        """اختبار إنشاء صور مع نصوص مختلطة"""
        logger.info("🎨 اختبار إنشاء صور مع نصوص مختلطة...")
        
        results = {
            'total_tests': len(self.test_articles),
            'successful_generations': 0,
            'failed_generations': 0,
            'mixed_text_count': 0,
            'single_text_count': 0,
            'details': []
        }
        
        for i, article in enumerate(self.test_articles):
            logger.info(f"🎨 اختبار {i+1}/{len(self.test_articles)}: {article['title'][:40]}...")
            
            try:
                # إنشاء الصورة
                image_result = await self.generator.generate_manual_image(article)
                
                if image_result:
                    results['successful_generations'] += 1
                    
                    # تحليل النص لمعرفة النوع
                    analysis = self.generator.detect_mixed_language_text(article['title'])
                    if analysis['is_mixed']:
                        results['mixed_text_count'] += 1
                    else:
                        results['single_text_count'] += 1
                    
                    # فحص الملف المُنشأ
                    file_exists = os.path.exists(image_result['local_path'])
                    file_size = os.path.getsize(image_result['local_path']) if file_exists else 0
                    
                    results['details'].append({
                        'article_title': article['title'],
                        'success': True,
                        'filename': image_result['filename'],
                        'file_exists': file_exists,
                        'file_size_kb': round(file_size / 1024, 2),
                        'is_mixed': analysis['is_mixed'],
                        'primary_language': analysis['primary_language'],
                        'theme': image_result.get('theme', 'unknown')
                    })
                    
                    text_type = "مختلط" if analysis['is_mixed'] else "مفرد"
                    logger.info(f"✅ تم إنشاء الصورة ({text_type}): {image_result['filename']} ({file_size/1024:.1f} KB)")
                    
                else:
                    results['failed_generations'] += 1
                    results['details'].append({
                        'article_title': article['title'],
                        'success': False,
                        'error': 'No image result returned'
                    })
                    logger.error(f"❌ فشل في إنشاء الصورة للمقال: {article['title']}")
                    
            except Exception as e:
                results['failed_generations'] += 1
                results['details'].append({
                    'article_title': article['title'],
                    'success': False,
                    'error': str(e)
                })
                logger.error(f"❌ خطأ في إنشاء الصورة: {e}")
        
        success_rate = (results['successful_generations'] / results['total_tests']) * 100
        logger.info(f"🎨 معدل نجاح إنشاء الصور المختلطة: {success_rate:.1f}%")
        logger.info(f"📊 صور بنصوص مختلطة: {results['mixed_text_count']}")
        logger.info(f"📊 صور بنصوص مفردة: {results['single_text_count']}")
        
        return results
    
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل للنصوص المختلطة"""
        logger.info("🚀 بدء اختبار شامل للنصوص المختلطة...")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        # تشغيل جميع الاختبارات
        test_results = {
            'start_time': start_time.isoformat(),
            'mixed_language_detection': await self.test_mixed_language_detection(),
            'mixed_language_image_generation': await self.test_mixed_language_image_generation()
        }
        
        end_time = datetime.now()
        test_results['end_time'] = end_time.isoformat()
        test_results['duration_seconds'] = (end_time - start_time).total_seconds()
        
        # حساب الإحصائيات العامة
        total_tests = (
            test_results['mixed_language_detection']['total_tests'] +
            test_results['mixed_language_image_generation']['total_tests']
        )
        
        successful_tests = (
            test_results['mixed_language_detection']['correct_detections'] +
            test_results['mixed_language_image_generation']['successful_generations']
        )
        
        overall_success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        test_results['summary'] = {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'failed_tests': total_tests - successful_tests,
            'overall_success_rate': round(overall_success_rate, 2),
            'duration_minutes': round(test_results['duration_seconds'] / 60, 2),
            'mixed_language_support': {
                'detection_accuracy': round(
                    (test_results['mixed_language_detection']['correct_detections'] / 
                     test_results['mixed_language_detection']['total_tests']) * 100, 1
                ),
                'image_generation_success_rate': round(
                    (test_results['mixed_language_image_generation']['successful_generations'] /
                     test_results['mixed_language_image_generation']['total_tests']) * 100, 1
                ),
                'mixed_text_images': test_results['mixed_language_image_generation']['mixed_text_count'],
                'single_text_images': test_results['mixed_language_image_generation']['single_text_count']
            }
        }
        
        # طباعة الملخص
        logger.info("=" * 60)
        logger.info("📊 ملخص نتائج اختبار النصوص المختلطة:")
        logger.info(f"⏱️ المدة الإجمالية: {test_results['summary']['duration_minutes']} دقيقة")
        logger.info(f"📈 إجمالي الاختبارات: {test_results['summary']['total_tests']}")
        logger.info(f"✅ الاختبارات الناجحة: {test_results['summary']['successful_tests']}")
        logger.info(f"❌ الاختبارات الفاشلة: {test_results['summary']['failed_tests']}")
        logger.info(f"🎯 معدل النجاح الإجمالي: {test_results['summary']['overall_success_rate']}%")
        logger.info("")
        logger.info("🔧 حالة دعم النصوص المختلطة:")
        logger.info(f"🔍 دقة تحديد النصوص المختلطة: {test_results['summary']['mixed_language_support']['detection_accuracy']}%")
        logger.info(f"🎨 معدل نجاح إنشاء الصور: {test_results['summary']['mixed_language_support']['image_generation_success_rate']}%")
        logger.info(f"📊 صور بنصوص مختلطة: {test_results['summary']['mixed_language_support']['mixed_text_images']}")
        logger.info(f"📊 صور بنصوص مفردة: {test_results['summary']['mixed_language_support']['single_text_images']}")
        logger.info("=" * 60)
        
        return test_results

async def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🔤 بدء اختبار النصوص المختلطة (عربي + إنجليزي)")
        
        tester = MixedLanguageTextTester()
        results = await tester.run_comprehensive_test()
        
        logger.info("🎉 تم الانتهاء من اختبار النصوص المختلطة بنجاح!")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الاختبار: {e}")
        return None

if __name__ == "__main__":
    # تشغيل الاختبار
    results = asyncio.run(main())
