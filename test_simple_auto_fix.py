#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لنظام الإصلاح التلقائي
"""

import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_auto_fix():
    """اختبار بسيط للإصلاح التلقائي"""
    try:
        from modules.content_generator import ContentGenerator
        
        print("🧪 اختبار نظام الإصلاح التلقائي...")
        
        # إنشاء مولد المحتوى
        content_generator = ContentGenerator()
        
        # حالة اختبار بسيطة
        title = "دليل المبتدئين الشامل لألعاب الفيديو"
        content = "الألعاب ممتعة ومسلية. يمكن للجميع الاستمتاع بها."
        source_content = {'keywords': [], 'summary': content}
        
        print(f"📝 العنوان الأصلي: {title}")
        print(f"📄 المحتوى الأصلي: {content}")
        
        # فحص التوافق الأولي
        initial_analysis = content_generator._analyze_title_content_match(title, content)
        print(f"📊 التوافق الأولي: {initial_analysis['match_ratio']:.1%}")
        print(f"❌ المفاهيم المفقودة: {initial_analysis.get('missing_concepts', [])}")
        
        # تطبيق الإصلاح التلقائي
        print("\n🔧 تطبيق الإصلاح التلقائي...")
        fix_result = content_generator._ensure_title_content_compatibility(
            title, content, source_content
        )
        
        print(f"✅ تم الإصلاح: {fix_result.get('was_fixed', False)}")
        print(f"🔧 نوع الإصلاح: {fix_result.get('fix_type', 'none')}")
        print(f"📝 العنوان النهائي: {fix_result['title']}")
        print(f"📄 المحتوى النهائي: {fix_result['content'][:200]}...")
        
        # فحص التوافق النهائي
        final_analysis = content_generator._analyze_title_content_match(
            fix_result['title'], fix_result['content']
        )
        print(f"📊 التوافق النهائي: {final_analysis['match_ratio']:.1%}")
        
        # تقييم النجاح
        improvement = final_analysis['match_ratio'] - initial_analysis['match_ratio']
        print(f"📈 التحسن: {improvement:+.1%}")
        
        if improvement > 0.1 and final_analysis['match_ratio'] >= 0.6:
            print("🎉 نجح الإصلاح التلقائي!")
            return True
        else:
            print("⚠️ الإصلاح التلقائي يحتاج تحسين")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_title_adjustment():
    """اختبار تعديل العنوان"""
    try:
        from modules.content_generator import ContentGenerator
        
        print("\n🧪 اختبار تعديل العنوان...")
        
        content_generator = ContentGenerator()
        
        # حالة اختبار لتعديل العنوان
        title = "مراجعة شاملة للمبتدئين والمحترفين لأفضل ألعاب الاستراتيجية المجانية"
        content = "هذه لعبة أكشن رائعة. القتال ممتع والرسوميات جميلة."
        
        print(f"📝 العنوان الأصلي: {title}")
        print(f"📄 المحتوى: {content}")
        
        # تعديل العنوان
        missing_concepts = ['مراجعة', 'شاملة', 'مبتدئين', 'استراتيجية', 'مجانية']
        adjusted_title = content_generator._adjust_title_to_match_content(
            title, content, missing_concepts
        )
        
        print(f"📝 العنوان المعدل: {adjusted_title}")
        
        if len(adjusted_title) < len(title) and adjusted_title != title:
            print("✅ تم تعديل العنوان بنجاح!")
            return True
        else:
            print("⚠️ تعديل العنوان لم يعمل كما متوقع")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تعديل العنوان: {e}")
        return False

def test_content_enhancement():
    """اختبار تحسين المحتوى"""
    try:
        from modules.content_generator import ContentGenerator
        
        print("\n🧪 اختبار تحسين المحتوى...")
        
        content_generator = ContentGenerator()
        
        # حالة اختبار لتحسين المحتوى
        content = "الألعاب ممتعة."
        missing_concepts = ['مبتدئين', 'دليل', 'نصائح']
        source_content = {'keywords': missing_concepts}
        
        print(f"📄 المحتوى الأصلي: {content}")
        print(f"❌ المفاهيم المفقودة: {missing_concepts}")
        
        # تحسين المحتوى
        enhanced_content = content_generator._enhance_content_with_missing_concepts(
            content, missing_concepts, source_content
        )
        
        print(f"📄 المحتوى المحسن: {enhanced_content[:300]}...")
        
        if len(enhanced_content) > len(content) and enhanced_content != content:
            print("✅ تم تحسين المحتوى بنجاح!")
            return True
        else:
            print("⚠️ تحسين المحتوى لم يعمل كما متوقع")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تحسين المحتوى: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار نظام الإصلاح التلقائي البسيط...")
    
    tests = [
        ("الإصلاح التلقائي العام", test_auto_fix),
        ("تعديل العنوان", test_title_adjustment),
        ("تحسين المحتوى", test_content_enhancement)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🔍 اختبار: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ نجح اختبار: {test_name}")
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"💥 خطأ في اختبار {test_name}: {e}")
    
    print(f"\n{'='*50}")
    print("📊 ملخص النتائج")
    print('='*50)
    print(f"🎯 معدل النجاح: {passed_tests/total_tests:.1%}")
    print(f"✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! نظام الإصلاح التلقائي يعمل بكفاءة.")
        return 0
    elif passed_tests >= total_tests * 0.7:
        print("✅ معظم الاختبارات نجحت. نظام الإصلاح التلقائي يعمل بشكل جيد.")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت. نظام الإصلاح التلقائي يحتاج مراجعة.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
