#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لنظام Whisper المحسن مع واجهة الويب
Quick Start for Enhanced Whisper System with Web Interface
"""

import asyncio
import sys
import os
import threading
import time
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from integrate_enhanced_whisper import WhisperIntegrationManager


def start_web_interface():
    """تشغيل واجهة الويب في thread منفصل"""
    try:
        logger.info("🌐 بدء تشغيل واجهة الويب...")
        
        # استيراد واجهة الويب
        import whisper_web_interface
        
        # تشغيل الخادم
        whisper_web_interface.app.run(
            host='0.0.0.0', 
            port=5001, 
            debug=False,
            use_reloader=False
        )
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل واجهة الويب: {e}")


async def setup_and_test_system():
    """إعداد واختبار النظام"""
    logger.info("🔧 إعداد نظام Whisper المحسن...")
    
    try:
        # إنشاء مدير التكامل
        integration_manager = WhisperIntegrationManager()
        
        # فحص الحالة الحالية
        logger.info("📊 فحص حالة النظام...")
        status = integration_manager.get_integration_status()
        
        logger.info("📋 حالة النظام:")
        logger.info(f"   ✅ النظام المحسن متوفر: {status.get('enhanced_whisper_available', False)}")
        logger.info(f"   🔗 التكامل مع YouTube: {status.get('youtube_integration_active', False)}")
        logger.info(f"   ⚙️ الإعدادات صحيحة: {status.get('configuration_valid', False)}")
        logger.info(f"   📊 عدد التحويلات: {status.get('transcription_history_count', 0)}")
        
        # إعداد التكامل
        logger.info("\n🔗 إعداد التكامل...")
        integration_success = await integration_manager.integrate_with_youtube_analyzer()
        
        if integration_success:
            logger.info("✅ تم إعداد التكامل بنجاح")
        else:
            logger.warning("⚠️ فشل في إعداد التكامل")
        
        # اختبار سريع
        logger.info("\n🧪 اختبار سريع للنظام...")
        test_success = await integration_manager.test_integration()
        
        if test_success:
            logger.info("✅ نجح الاختبار السريع")
        else:
            logger.warning("⚠️ فشل الاختبار السريع")
        
        return integration_success and test_success
        
    except Exception as e:
        logger.error(f"❌ خطأ في إعداد النظام: {e}")
        return False


def display_system_info():
    """عرض معلومات النظام"""
    logger.info("\n" + "🎤" * 60)
    logger.info("🎤 نظام Whisper المحسن - معلومات النظام")
    logger.info("🎤" * 60)
    
    logger.info("📋 الميزات المتاحة:")
    logger.info("   🎯 تحويل صوت إلى نص محسن")
    logger.info("   🔄 طرق رفع متعددة للاستضافة المجانية")
    logger.info("   📊 إحصائيات مفصلة")
    logger.info("   🌐 واجهة ويب تفاعلية")
    logger.info("   📤 تصدير النتائج (CSV/JSON)")
    logger.info("   🔗 تكامل مع محلل YouTube")
    
    logger.info("\n🌐 الواجهات المتاحة:")
    logger.info("   📱 واجهة الويب: http://localhost:5001")
    logger.info("   🔧 API للتطوير: http://localhost:5001/api/")
    
    logger.info("\n📚 الاستخدام:")
    logger.info("   1. افتح واجهة الويب في المتصفح")
    logger.info("   2. راقب نتائج التحويل في الوقت الفعلي")
    logger.info("   3. صدّر البيانات عند الحاجة")
    
    logger.info("\n🎤" * 60)


def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء تشغيل نظام Whisper المحسن...")
    logger.info(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # عرض معلومات النظام
        display_system_info()
        
        # إعداد واختبار النظام
        logger.info("\n🔧 إعداد النظام...")
        setup_success = asyncio.run(setup_and_test_system())
        
        if not setup_success:
            logger.warning("⚠️ فشل في إعداد النظام، لكن سيتم المتابعة...")
        
        # تشغيل واجهة الويب في thread منفصل
        logger.info("\n🌐 تشغيل واجهة الويب...")
        web_thread = threading.Thread(target=start_web_interface, daemon=True)
        web_thread.start()
        
        # انتظار قليل لبدء الخادم
        time.sleep(3)
        
        # فحص حالة الخادم
        try:
            import requests
            response = requests.get('http://localhost:5001', timeout=5)
            if response.status_code == 200:
                logger.info("✅ واجهة الويب تعمل بنجاح")
            else:
                logger.warning(f"⚠️ مشكلة في واجهة الويب: {response.status_code}")
        except Exception as web_check_error:
            logger.warning(f"⚠️ لا يمكن فحص واجهة الويب: {web_check_error}")
        
        # عرض الروابط
        logger.info("\n🎉 النظام جاهز للاستخدام!")
        logger.info("🌐 افتح المتصفح وانتقل إلى: http://localhost:5001")
        logger.info("📱 أو استخدم: http://127.0.0.1:5001")
        
        # إبقاء البرنامج يعمل
        logger.info("\n⏳ النظام يعمل... اضغط Ctrl+C للإيقاف")
        
        try:
            while True:
                time.sleep(60)  # فحص كل دقيقة
                
                # فحص حالة النظام
                if not web_thread.is_alive():
                    logger.warning("⚠️ توقفت واجهة الويب، إعادة تشغيل...")
                    web_thread = threading.Thread(target=start_web_interface, daemon=True)
                    web_thread.start()
                
        except KeyboardInterrupt:
            logger.info("\n🛑 تم إيقاف النظام بواسطة المستخدم")
            
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل النظام: {e}")
        return False
    
    logger.info("👋 تم إنهاء النظام")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
