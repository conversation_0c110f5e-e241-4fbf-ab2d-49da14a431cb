#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحسينات على استخراج المحتوى من YouTube
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_test_header(test_name):
    """طباعة رأس الاختبار"""
    print(f"\n{'='*60}")
    print(f"🧪 اختبار: {test_name}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """طباعة نتيجة الاختبار"""
    status = "✅ نجح" if success else "❌ فشل"
    print(f"{status} {test_name}")
    if details:
        print(f"   📝 {details}")

async def test_youtube_analyzer_improvements():
    """اختبار التحسينات على محلل YouTube"""
    print_test_header("تحسينات محلل YouTube")
    
    try:
        from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
        
        analyzer = AdvancedYouTubeAnalyzer()
        print_result("إنشاء AdvancedYouTubeAnalyzer", True)
        
        # اختبار تحليل النص المحسن
        test_text = """
        أعلنت شركة Microsoft عن تحديث جديد للعبة Minecraft يتضمن ميزات مثيرة.
        اللعبة ستحصل على تحسينات في الرسوميات وإضافة مناطق جديدة للاستكشاف.
        يمكن للاعبين الآن بناء هياكل أكثر تعقيداً باستخدام الأدوات الجديدة.
        """
        
        analysis_result = analyzer.analyze_transcript_for_gaming_news(test_text, 'ar')
        
        # فحص النتائج
        has_news = analysis_result.get('news_count', 0) > 0
        has_info = analysis_result.get('info_count', 0) > 0
        total_content = has_news or has_info
        
        print_result("تحليل النص المحسن", total_content, 
                    f"أخبار: {analysis_result.get('news_count', 0)}, معلومات: {analysis_result.get('info_count', 0)}")
        
        # اختبار استخراج الكلمات المفتاحية
        if analysis_result.get('main_news'):
            first_news = analysis_result['main_news'][0]
            has_topics = len(first_news.get('topics', [])) > 0
            print_result("استخراج المواضيع", has_topics,
                        f"المواضيع: {first_news.get('topics', [])}")
        
        # اختبار إنشاء المحتوى البديل
        fallback_content = await analyzer._get_video_metadata_as_content("test_video_id")
        has_fallback = fallback_content and len(fallback_content) > 100
        print_result("إنشاء المحتوى البديل", has_fallback,
                    f"طول المحتوى: {len(fallback_content) if fallback_content else 0} حرف")
        
        return total_content and has_fallback
        
    except Exception as e:
        print_result("محلل YouTube", False, f"خطأ: {str(e)}")
        return False

async def test_main_improvements():
    """اختبار التحسينات في main.py"""
    print_test_header("تحسينات main.py")
    
    try:
        from main import GamingNewsBot
        
        bot = GamingNewsBot()
        print_result("إنشاء GamingNewsBot", True)
        
        # اختبار دالة استخراج الكلمات المفتاحية
        test_text = "هذا نص عن لعبة Minecraft الجديدة مع تحديث Steam"
        keywords = bot._extract_keywords_from_text(test_text)
        
        has_keywords = len(keywords) > 0
        print_result("استخراج الكلمات المفتاحية", has_keywords,
                    f"الكلمات: {keywords}")
        
        return has_keywords
        
    except Exception as e:
        print_result("تحسينات main.py", False, f"خطأ: {str(e)}")
        return False

async def test_classification_improvements():
    """اختبار تحسينات التصنيف"""
    print_test_header("تحسينات التصنيف")
    
    try:
        from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
        
        analyzer = AdvancedYouTubeAnalyzer()
        
        # اختبار جمل مختلفة
        test_sentences = [
            "أعلنت شركة Sony عن لعبة جديدة",  # خبر واضح
            "اللعبة متوفرة الآن على Steam",      # خبر مع كلمات الألعاب
            "يمكن للاعبين بناء منازل في Minecraft",  # معلومة مع كلمات الألعاب
            "هذا فيديو عن الألعاب",              # محتوى عام عن الألعاب
        ]
        
        results = []
        for sentence in test_sentences:
            classification = analyzer._classify_sentence(sentence, 'ar')
            is_gaming_related = (
                classification['type'] == 'news' or 
                len(classification['topics']) > 0 or
                classification['importance'] > 0
            )
            results.append(is_gaming_related)
            print_result(f"تصنيف: '{sentence[:30]}...'", is_gaming_related,
                        f"نوع: {classification['type']}, أهمية: {classification['importance']}")
        
        # يجب أن تكون معظم الجمل مصنفة بشكل صحيح
        success_rate = sum(results) / len(results)
        overall_success = success_rate >= 0.75  # 75% نجاح على الأقل
        
        print_result("معدل نجاح التصنيف", overall_success,
                    f"{success_rate*100:.1f}% من الجمل تم تصنيفها بشكل صحيح")
        
        return overall_success
        
    except Exception as e:
        print_result("تحسينات التصنيف", False, f"خطأ: {str(e)}")
        return False

async def test_content_acceptance():
    """اختبار معايير قبول المحتوى المحسنة"""
    print_test_header("معايير قبول المحتوى")
    
    try:
        from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
        
        analyzer = AdvancedYouTubeAnalyzer()
        
        # محاكاة محتوى مختلط (أخبار + معلومات)
        mixed_content = """
        تحديث جديد للعبة Fortnite يتضمن خريطة جديدة.
        يمكن للاعبين الآن استخدام أسلحة جديدة في المعارك.
        اللعبة متوفرة على جميع المنصات بما في ذلك PlayStation وXbox.
        """
        
        analysis = analyzer.analyze_transcript_for_gaming_news(mixed_content, 'ar')
        
        # فحص إذا كان سيتم قبول هذا المحتوى
        total_items = len(analysis.get('main_news', [])) + len(analysis.get('additional_info', []))
        content_acceptable = total_items > 0
        
        print_result("قبول المحتوى المختلط", content_acceptable,
                    f"إجمالي العناصر: {total_items}")
        
        # اختبار محتوى ضعيف
        weak_content = "هذا فيديو عن شيء ما"
        weak_analysis = analyzer.analyze_transcript_for_gaming_news(weak_content, 'ar')
        weak_items = len(weak_analysis.get('main_news', [])) + len(weak_analysis.get('additional_info', []))
        
        print_result("رفض المحتوى الضعيف", weak_items == 0,
                    f"عناصر المحتوى الضعيف: {weak_items}")
        
        return content_acceptable and weak_items == 0
        
    except Exception as e:
        print_result("معايير قبول المحتوى", False, f"خطأ: {str(e)}")
        return False

async def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار تحسينات YouTube...")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("تحسينات محلل YouTube", test_youtube_analyzer_improvements),
        ("تحسينات main.py", test_main_improvements),
        ("تحسينات التصنيف", test_classification_improvements),
        ("معايير قبول المحتوى", test_content_acceptance),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print_result(test_name, False, f"خطأ غير متوقع: {str(e)}")
            results.append((test_name, False))
    
    # تقرير النتائج النهائية
    print_test_header("تقرير النتائج النهائية")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        print_result(test_name, result)
    
    print(f"\n📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع التحسينات تعمل بشكل صحيح!")
        print("✅ الوكيل سيستخرج محتوى أكثر من YouTube")
        print("🔄 البحث على الويب سيبقى كخطة بديلة ممتازة")
    else:
        print("⚠️ بعض التحسينات تحتاج مراجعة")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ حرج: {e}")
        sys.exit(1)
