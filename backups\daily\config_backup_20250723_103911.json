{"config/settings.py": "# إعدادات الوكيل البرمجي لأخبار الألعاب\nimport os\nfrom typing import List, Dict, Any\nfrom dotenv import load_dotenv\nfrom modules.api_key_manager import ApiKeyManager\n\nload_dotenv()\n\n# --- Get the absolute path of the project root ---\nPROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))\n\n# --- مدير مفاتيح Google API المركزي ---\n# يقرأ قائمة المفاتيح من متغير البيئة ويزيل أي مسافات بيضاء\ngoogle_api_keys_str = os.getenv(\"GOOGLE_API_KEYS_LIST\", \"\")\ngoogle_api_keys = [key.strip() for key in google_api_keys_str.split(',') if key.strip()]\n\n# إضافة المفتاح الرئيسي (GEMINI_API_KEY) إلى بداية القائمة إذا كان موجودًا\nmain_gemini_key = os.getenv(\"GEMINI_API_KEY\")\nif main_gemini_key and main_gemini_key not in google_api_keys:\n    google_api_keys.insert(0, main_gemini_key)\n\n# إضافة مفتاح YouTube الجديد في المقدمة (أولوية عالية)\nyoutube_api_key = \"AIzaSyDopjKq-bRb2QuPICFlkR3WUsSb4_3vqPk\"\nif youtube_api_key not in google_api_keys:\n    google_api_keys.insert(0, youtube_api_key)\n\n# إضافة مفاتيح Google Search الجديدة إلى القائمة\nnew_google_search_keys = [\n    # مفتاح YouTube API الجديد (مضاف من المستخدم)\n    \"AIzaSyDopjKq-bRb2QuPICFlkR3WUsSb4_3vqPk\",\n\n    # المجموعة الأولى\n    \"AIzaSyAEnnXE2U5ypT-XgxyZUMt8JiMFoa9bgU4\",\n    \"AIzaSyBTjOvcIm_qDBx00U_3FqSlYEQI7kpOIlk\",\n    \"AIzaSyDxvu1Z-DH7HnF_Z0y6OP13astfqeZMdkQ\",\n    \"AIzaSyDfLuY9QV-Gg2LNTzWw7L8olw3sR3_sFDQ\",\n    \"AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk\",\n\n    # المجموعة الثانية (الجديدة)\n    \"AIzaSyCpWpYWqYfG0XiaMvlJlnYGIeNvxVa-gfU\",\n    \"AIzaSyBiq4VsF9RG0v5EDFiQ2YaDnsObx5JK3Ek\",\n    \"AIzaSyDZ4WEJqhlGQZ0EFMt01awZB1rabEFphfU\",\n    \"AIzaSyBYH-8POlNTPdsWwvHQfw6meyz6ikqEjRk\",\n    \"AIzaSyALFblO8iqAWIMTVdiG0EHdy7mU8WkJk9o\"\n]\n\n# إضافة المفاتيح الجديدة إلى القائمة إذا لم تكن موجودة\nfor key in new_google_search_keys:\n    if key not in google_api_keys:\n        google_api_keys.append(key)\n\n# تحسينات مدير مفاتيح Gemini API\ndef create_enhanced_google_api_manager():\n    \"\"\"إنشاء مدير مفاتيح محسن مع معالجة أفضل للأخطاء\"\"\"\n    try:\n        # مفاتيح إضافية للاحتياط\n        additional_keys = [\n            \"AIzaSyDjjgZIkGyC0i8RVM9IA14WFJBqmEQHhHE\",  # المفتاح الجديد\n            # يمكن إضافة المزيد هنا\n        ]\n        \n        # دمج المفاتيح\n        all_keys = google_api_keys.copy()\n        for key in additional_keys:\n            if key and key not in all_keys:\n                all_keys.append(key)\n        \n        if all_keys:\n            enhanced_manager = ApiKeyManager(\n                api_keys=all_keys, \n                service_name=\"Google Enhanced\",\n                auto_recovery_minutes=30,  # استرداد أسرع\n                load_balancing=True\n            )\n            return enhanced_manager\n        else:\n            return None\n            \n    except Exception as e:\n        print(f\"❌ خطأ في إنشاء مدير المفاتيح المحسن: {e}\")\n        return None\n\n# تهيئة مدير المفاتيح فقط إذا كانت هناك مفاتيح متاحة\nif google_api_keys:\n    # محاولة استخدام المدير المحسن أولاً\n    google_api_manager_enhanced = create_enhanced_google_api_manager()\n    if google_api_manager_enhanced:\n        google_api_manager = google_api_manager_enhanced\n    else:\n        # العودة للمدير الأساسي\n        google_api_manager = ApiKeyManager(api_keys=google_api_keys, service_name=\"Google\")\nelse:\n    google_api_manager = None\n\n# سيتم تهيئة مدير مفاتيح Google Search بعد تعريف BotConfig\ngoogle_search_api_manager = None\n\n# مدير Google Search المتقدم (سيتم تهيئته عند الحاجة)\ngoogle_search_manager_advanced = None\n\ndef get_google_search_manager():\n    \"\"\"الحصول على مدير Google Search المتقدم\"\"\"\n    global google_search_manager_advanced\n\n    if google_search_manager_advanced is None:\n        try:\n            from modules.google_search_manager import GoogleSearchManager\n\n            # استخدام جميع المفاتيح المتاحة\n            search_keys = [\n                # المجموعة الأولى\n                \"AIzaSyAEnnXE2U5ypT-XgxyZUMt8JiMFoa9bgU4\",\n                \"AIzaSyBTjOvcIm_qDBx00U_3FqSlYEQI7kpOIlk\",\n                \"AIzaSyDxvu1Z-DH7HnF_Z0y6OP13astfqeZMdkQ\",\n                \"AIzaSyDfLuY9QV-Gg2LNTzWw7L8olw3sR3_sFDQ\",\n                \"AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk\",\n\n                # المجموعة الثانية (الجديدة)\n                \"AIzaSyCpWpYWqYfG0XiaMvlJlnYGIeNvxVa-gfU\",\n                \"AIzaSyBiq4VsF9RG0v5EDFiQ2YaDnsObx5JK3Ek\",\n                \"AIzaSyDZ4WEJqhlGQZ0EFMt01awZB1rabEFphfU\",\n                \"AIzaSyBYH-8POlNTPdsWwvHQfw6meyz6ikqEjRk\",\n                \"AIzaSyALFblO8iqAWIMTVdiG0EHdy7mU8WkJk9o\"\n            ]\n\n            search_engine_id = os.getenv(\"GOOGLE_SEARCH_ENGINE_ID\", \"\")\n\n            if search_keys and search_engine_id:\n                google_search_manager_advanced = GoogleSearchManager(\n                    api_keys=search_keys,\n                    search_engine_id=search_engine_id\n                )\n                print(f\"🔍 تم تهيئة مدير Google Search المتقدم بنجاح\")\n            else:\n                print(\"⚠️ لا يمكن تهيئة مدير Google Search المتقدم - مفاتيح أو معرف محرك البحث مفقود\")\n\n        except Exception as e:\n            print(f\"❌ خطأ في تهيئة مدير Google Search المتقدم: {e}\")\n\n    return google_search_manager_advanced\n\n\n# إعدادات PageSpeed Insights\nPAGESPEED_INSIGHTS_API_KEY = \"AIzaSyDjjgZIkGyC0i8RVM9IA14WFJBqmEQHhHE\"  # يمكن استخدام نفس مفتاح Gemini\n\n# إعدادات SEO محسنة\nclass EnhancedSEOConfig:\n    \"\"\"إعدادات SEO محسنة\"\"\"\n    \n    # حدود العنوان المحسنة\n    TITLE_LENGTH_MIN = 30\n    TITLE_LENGTH_MAX = 60\n    \n    # حدود الوصف\n    META_DESCRIPTION_MIN = 120\n    META_DESCRIPTION_MAX = 160\n    \n    # حدود المحتوى\n    CONTENT_MIN_WORDS = 300\n    CONTENT_OPTIMAL_WORDS = 800\n    \n    # كثافة الكلمات المفتاحية\n    KEYWORD_DENSITY_MIN = 0.5  # 0.5%\n    KEYWORD_DENSITY_MAX = 3.0  # 3%\n    \n    # نقاط SEO المستهدفة\n    TARGET_SEO_SCORE = 80\n    MINIMUM_SEO_SCORE = 60\n    \n    # Core Web Vitals المستهدفة\n    TARGET_LCP = 2.5  # Largest Contentful Paint (ثواني)\n    TARGET_FID = 100  # First Input Delay (ميلي ثانية)\n    TARGET_CLS = 0.1  # Cumulative Layout Shift\n    \n    # أوزان حساب النقاط\n    SCORE_WEIGHTS = {\n        'title_optimization': 0.20,\n        'content_quality': 0.25,\n        'keyword_optimization': 0.20,\n        'technical_seo': 0.15,\n        'user_experience': 0.10,\n        'mobile_optimization': 0.10\n    }\n\n# تطبيق الإعدادات المحسنة\nSEOConfig = EnhancedSEOConfig()\n\nclass BotConfig:\n    \"\"\"تكوين البوت الأساسي\"\"\"\n    \n    # تم إزالة معلومات بوت تيليجرام - الآن يعتمد على الواجهة الويب فقط\n    # TELEGRAM_BOT_TOKEN = \"\"  # تم إزالة Telegram\n    # TELEGRAM_BOT_USERNAME = \"\"  # تم إزالة Telegram\n    # TELEGRAM_CHANNEL_URL = \"\"  # تم إزالة Telegram\n    # TELEGRAM_CHANNEL_ID = \"\"  # تم إزالة Telegram\n    # TELEGRAM_ADMIN_ID = \"\"  # تم إزالة Telegram - الموافقة الآن عبر الواجهة الويب\n    \n    # مفاتيح API (يتم إدارتها الآن عبر google_api_manager)\n    GEMINI_API_KEY = main_gemini_key # احتفاظ بالمرجع الرئيسي\n    BLOGGER_CLIENT_ID = os.getenv(\"BLOGGER_CLIENT_ID\", \"\")\n    BLOGGER_CLIENT_SECRET = os.getenv(\"BLOGGER_CLIENT_SECRET\", \"\")\n    BLOGGER_BLOG_ID = os.getenv(\"BLOGGER_BLOG_ID\", \"\")\n    BLOGGER_CLIENT_SECRET_FILE = os.path.join(PROJECT_ROOT, os.getenv(\"BLOGGER_CLIENT_SECRET_FILE\", \"client_secret.json\"))\n    # إعدادات Txtify المحسنة مع روابط احتياطية\n    TXTIFY_API_URLS = ['https://nanami34-ai55.hf.space', 'https://txtify-api.hf.space', 'http://localhost:8000']\n    TXTIFY_API_URL = TXTIFY_API_URLS[0]  # الرابط الأساسي\n    TXTIFY_FALLBACK_ENABLED = True  # تفعيل النظام الاحتياطي\n    TXTIFY_TIMEOUT_SECONDS = 300  # مهلة 5 دقائق\n    GOOGLE_SEARCH_ENGINE_ID = os.getenv(\"GOOGLE_SEARCH_ENGINE_ID\", \"\")\n\n    # إعدادات الموقع والعلامة المائية\n    WEBSITE_NAME = os.getenv(\"WEBSITE_NAME\", \"Gaming News\")  # اسم الموقع للعلامة المائية\n\n    # مفاتيح APIs للأخبار المتقدمة - نظام جديد قوي!\n    NEWSAPI_KEY = os.getenv(\"NEWSAPI_KEY\", \"\")  # NewsAPI.org\n    NEWSDATA_KEY = os.getenv(\"NEWSDATA_KEY\", \"pub_6a04788f4edc429a8fb798dc3af6a6fb\")  # NewsData.io\n    THENEWSAPI_KEY = os.getenv(\"THENEWSAPI_KEY\", \"\")  # TheNewsAPI.com\n    GNEWS_KEY = os.getenv(\"GNEWS_KEY\", \"\")  # GNews.io\n\n    # مفاتيح APIs للبحث المتقدم\n    BRAVE_SEARCH_KEY = os.getenv(\"BRAVE_SEARCH_KEY\", \"\")  # Brave Search API\n\n    # مفاتيح APIs البديلة لـ Google Search\n    SERPAPI_KEY = os.getenv(\"SERPAPI_KEY\", \"8b221d23f3aa037d438db307927f904933ae3037\")  # SerpAPI Key الجديد الأول\n    RAPIDAPI_KEY = os.getenv(\"RAPIDAPI_KEY\", \"**************************************************\")  # RapidAPI Key (احتياطي)\n    SERPAPI_RAPIDAPI_HOST = \"serpapi.p.rapidapi.com\"  # SerpAPI عبر RapidAPI\n    BING_SEARCH_KEY = os.getenv(\"BING_SEARCH_KEY\", \"\")  # Bing Web Search API\n    ZENSERP_KEY = os.getenv(\"ZENSERP_KEY\", \"\")  # Zenserp API\n\n    # مفاتيح APIs للصور المرخصة - نظام جديد آمن قانونياً!\n    TWITCH_CLIENT_ID = os.getenv(\"TWITCH_CLIENT_ID\", \"\")  # IGDB API (Twitch)\n    TWITCH_CLIENT_SECRET = os.getenv(\"TWITCH_CLIENT_SECRET\", \"\")  # IGDB API Secret\n    RAWG_API_KEY = os.getenv(\"RAWG_API_KEY\", \"\")  # RAWG.io API\n    STEAM_API_KEY = os.getenv(\"STEAM_API_KEY\", \"\")  # Steam Web API (اختياري)\n\n\n\n    # مفاتيح Google Search متعددة للتوزيع والاحتياط\n    GOOGLE_SEARCH_KEYS = [\n        # المجموعة الأولى (المفاتيح السابقة)\n        \"AIzaSyAEnnXE2U5ypT-XgxyZUMt8JiMFoa9bgU4\",\n        \"AIzaSyBTjOvcIm_qDBx00U_3FqSlYEQI7kpOIlk\",\n        \"AIzaSyDxvu1Z-DH7HnF_Z0y6OP13astfqeZMdkQ\",\n        \"AIzaSyDfLuY9QV-Gg2LNTzWw7L8olw3sR3_sFDQ\",\n        \"AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk\",\n\n        # المجموعة الثانية (المفاتيح الجديدة)\n        \"AIzaSyCpWpYWqYfG0XiaMvlJlnYGIeNvxVa-gfU\",\n        \"AIzaSyBiq4VsF9RG0v5EDFiQ2YaDnsObx5JK3Ek\",\n        \"AIzaSyDZ4WEJqhlGQZ0EFMt01awZB1rabEFphfU\",\n        \"AIzaSyBYH-8POlNTPdsWwvHQfw6meyz6ikqEjRk\",\n        \"AIzaSyALFblO8iqAWIMTVdiG0EHdy7mU8WkJk9o\"\n    ]\n\n    # المفتاح الافتراضي (للتوافق مع الكود الموجود)\n    GOOGLE_SEARCH_KEY = os.getenv(\"GOOGLE_SEARCH_KEY\", GOOGLE_SEARCH_KEYS[0])  # Google Custom Search\n\n    # مفاتيح APIs للبحث العميق باستخدام MCP وخدمات AI مجانية\n    PERPLEXITY_API_KEY = os.getenv(\"PERPLEXITY_API_KEY\", \"\")  # Perplexity AI (5 استعلامات/يوم مجاناً)\n    YOU_COM_API_KEY = os.getenv(\"YOU_COM_API_KEY\", \"\")  # You.com (100 استعلام/يوم مجاناً)\n    SERPER_API_KEY = os.getenv(\"SERPER_API_KEY\", \"\")  # Serper (2500 استعلام/شهر مجاناً)\n\n    # مفاتيح Tavily API للبحث العميق مع الذكاء الاصطناعي\n    TAVILY_API_KEYS = [\n        os.getenv(\"TAVILY_API_KEY_1\", \"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\"),  # المفتاح الأول\n        os.getenv(\"TAVILY_API_KEY_2\", \"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\"),  # المفتاح الثاني\n    ]\n    TAVILY_API_KEY = TAVILY_API_KEYS[0]  # المفتاح الافتراضي للتوافق مع الكود الموجود\n\n    # مفاتيح APIs البديلة الجديدة للبحث المتقدم (مجانية 100%)\n    # 1. SerpAPI - مفاتيح متعددة للتوزيع\n    SERPAPI_KEYS = [\n        os.getenv(\"SERPAPI_KEY_1\", \"\"),  # المفتاح الأول\n        os.getenv(\"SERPAPI_KEY_2\", \"\"),  # المفتاح الثاني\n        os.getenv(\"SERPAPI_KEY_3\", \"\"),  # المفتاح الثالث\n    ]\n\n    # 2. ScraperAPI - للاستخراج المتقدم\n    SCRAPERAPI_KEYS = [\n        os.getenv(\"SCRAPERAPI_KEY_1\", \"\"),  # المفتاح الأول\n        os.getenv(\"SCRAPERAPI_KEY_2\", \"\"),  # المفتاح الثاني\n    ]\n\n    # 3. Zyte (Scrapinghub) - للاستخراج الموزع\n    ZYTE_API_KEYS = [\n        os.getenv(\"ZYTE_API_KEY_1\", \"\"),  # المفتاح الأول\n        os.getenv(\"ZYTE_API_KEY_2\", \"\"),  # المفتاح الثاني\n    ]\n\n    # 4. ContextualWeb Search API - للبحث العام\n    CONTEXTUALWEB_KEYS = [\n        os.getenv(\"CONTEXTUALWEB_KEY_1\", \"\"),  # المفتاح الأول\n        os.getenv(\"CONTEXTUALWEB_KEY_2\", \"\"),  # المفتاح الثاني\n    ]\n\n    # 5. Serper.dev - للذكاء الاصطناعي\n    SERPER_DEV_KEYS = [\n        os.getenv(\"SERPER_DEV_KEY_1\", \"\"),  # المفتاح الأول\n        os.getenv(\"SERPER_DEV_KEY_2\", \"\"),  # المفتاح الثاني\n    ]\n\n    # 6. Google Custom Search JSON API - احتياطي\n    GOOGLE_CUSTOM_SEARCH_KEYS = [\n        os.getenv(\"GOOGLE_CUSTOM_SEARCH_KEY_1\", \"\"),  # المفتاح الأول\n        os.getenv(\"GOOGLE_CUSTOM_SEARCH_KEY_2\", \"\"),  # المفتاح الثاني\n    ]\n    GOOGLE_CUSTOM_SEARCH_ENGINE_ID = os.getenv(\"GOOGLE_CUSTOM_SEARCH_ENGINE_ID\", \"\")\n\n    # إعدادات نظام YouTube المتقدم مع Whisper\n    YOUTUBE_API_KEY = os.getenv(\"YOUTUBE_API_KEY\", GOOGLE_SEARCH_KEYS[0] if GOOGLE_SEARCH_KEYS else \"\")  # مفتاح YouTube Data API\n    WHISPER_API_URL = os.getenv(\"WHISPER_API_URL\", \"https://nanami34-ai55.hf.space/api/transcribe\")\n    WHISPER_API_KEY = os.getenv(\"WHISPER_API_KEY\", \"whisper-hf-spaces-2025\")\n    HF_TOKEN = os.getenv(\"HF_TOKEN\", \"*************************************\")\n\n    # مفاتيح النماذج الاحتياطية للبحث العميق - تم التحديث لاستخدام Gemini 2.5 Pro فقط\n    # GEMINI_2_FLASH_API_KEY = os.getenv(\"GEMINI_2_FLASH_API_KEY\", \"\")  # تم إلغاؤه - نستخدم 2.5 Pro\n    DEEPSEEK_API_KEY = os.getenv(\"DEEPSEEK_API_KEY\", \"\")  # DeepSeek R1 - تفكير عميق + بحث\n    GROQ_API_KEY = os.getenv(\"GROQ_API_KEY\", \"\")  # Groq API - سرعة عالية\n    # GEMINI_1_5_FLASH_API_KEY = os.getenv(\"GEMINI_1_5_FLASH_API_KEY\", \"\")  # تم إلغاؤه - نستخدم 2.5 Pro\n\n    # إعدادات فلترة الفيديوهات\n    MAX_VIDEO_DURATION_MINUTES = int(os.getenv(\"MAX_VIDEO_DURATION_MINUTES\", \"30\"))  # 30 دقيقة كحد أقصى\n    MAX_VIDEO_AGE_DAYS = int(os.getenv(\"MAX_VIDEO_AGE_DAYS\", \"60\"))  # شهرين كحد أقصى\n\n    # إعدادات نظام الموافقة\n    APPROVAL_TIMEOUT_MINUTES = int(os.getenv(\"APPROVAL_TIMEOUT_MINUTES\", \"5\"))  # 5 دقائق للموافقة\n    AUTO_APPROVE_ON_TIMEOUT = os.getenv(\"AUTO_APPROVE_ON_TIMEOUT\", \"true\").lower() == \"true\"\n\n    # مفاتيح APIs للصور الآمنة - ImageGuard Pro\n    PEXELS_API_KEY = os.getenv(\"PEXELS_API_KEY\", \"\")  # مفتاح Pexels API (مجاني - 200 طلب/ساعة)\n    PIXABAY_API_KEY = os.getenv(\"PIXABAY_API_KEY\", \"\")  # مفتاح Pixabay API (مجاني - 5000 طلب/شهر)\n    UNSPLASH_ACCESS_KEY = os.getenv(\"UNSPLASH_ACCESS_KEY\", \"\")  # مفتاح Unsplash API (مجاني - 50 طلب/ساعة)\n\n    # مفاتيح APIs لإنشاء الصور بالذكاء الاصطناعي - AI Image Generation\n    # 🎯 Pollinations.AI - الطريقة الأساسية الجديدة (مجاني 100% - لا يحتاج مفتاح!)\n    POLLINATIONS_AI_URL = \"https://image.pollinations.ai/prompt/\"  # URL أساسي لـ Pollinations.AI\n    POLLINATIONS_AI_ENABLED = True  # تفعيل Pollinations.AI كطريقة أساسية\n\n    # APIs احتياطية (تستخدم عند فشل Pollinations.AI)\n    FREEPIK_API_KEY = os.getenv(\"FREEPIK_API_KEY\", \"FPSX1ee910637a8ec349e6d8c7f17a57740b\")  # مفتاح Freepik API المحدث\n    FLUXAI_API_KEY = os.getenv(\"FLUXAI_API_KEY\", \"b6863038ac459a1f8cd9e30d82cdd989\")  # مفتاح FluxAI API\n    LEONARDO_AI_API_KEY = os.getenv(\"LEONARDO_AI_API_KEY\", \"\")  # مفتاح Leonardo AI (اختياري)\n    MIDJOURNEY_API_KEY = os.getenv(\"MIDJOURNEY_API_KEY\", \"\")  # مفتاح Midjourney (اختياري)\n\n    # إعدادات التشغيل\n    SEARCH_INTERVAL_HOURS = 2  # البحث كل ساعتين\n    MAX_RETRIES = 3  # عدد محاولات إعادة التنفيذ\n    RETRY_DELAY = 30  # تأخير بين المحاولات (ثانية)\n    \n    # إعدادات قاعدة البيانات\n    DATABASE_PATH = \"data/articles.db\"\n    \n    # إعدادات التسجيل\n    LOG_LEVEL = \"INFO\"\n    LOG_FILE = \"logs/bot.log\"\n    \n    # حدود API\n    GEMINI_RATE_LIMIT = 60  # طلبات في الدقيقة\n    # TELEGRAM_RATE_LIMIT = 30  # تم إزالة Telegram\n    BLOGGER_RATE_LIMIT = 100  # طلبات في اليوم\n\n    # حدود APIs الصور\n    PEXELS_RATE_LIMIT = 200  # طلبات في الساعة\n    PIXABAY_RATE_LIMIT = 5000  # طلبات في الشهر\n    UNSPLASH_RATE_LIMIT = 50  # طلبات في الساعة\n\n    # حدود APIs إنشاء الصور بالذكاء الاصطناعي\n    POLLINATIONS_AI_RATE_LIMIT = 999999  # لا توجد حدود (مجاني بالكامل)\n    FREEPIK_RATE_LIMIT = 100  # طلبات في اليوم (حسب الخطة)\n    FLUXAI_RATE_LIMIT = 1000  # طلبات في اليوم (مجاني)\n    LEONARDO_AI_RATE_LIMIT = 150  # طلبات في اليوم\n    MIDJOURNEY_RATE_LIMIT = 25  # طلبات في الساعة\n\n    # حدود APIs الأخبار المتقدمة\n    NEWSAPI_RATE_LIMIT = 1000  # طلبات في اليوم (خطة مجانية)\n    NEWSDATA_RATE_LIMIT = 200  # طلبات في اليوم (خطة مجانية)\n    THENEWSAPI_RATE_LIMIT = 100  # طلبات في اليوم\n    GNEWS_RATE_LIMIT = 100  # طلبات في اليوم\n\n    # حدود APIs البحث المتقدم\n    BRAVE_SEARCH_RATE_LIMIT = 2000  # طلبات في الشهر (خطة مجانية)\n    GOOGLE_SEARCH_RATE_LIMIT = 100  # طلبات في اليوم (خطة مجانية)\n    \n    @classmethod\n    def validate_config(cls) -> bool:\n        \"\"\"التحقق من صحة التكوين\"\"\"\n        required_fields = [\n            \"GEMINI_API_KEY\",\n            \"BLOGGER_CLIENT_ID\",\n            \"BLOGGER_CLIENT_SECRET\",\n            \"BLOGGER_BLOG_ID\"\n        ]\n\n        for field in required_fields:\n            if not getattr(cls, field):\n                print(f\"⚠️ حقل مطلوب غير موجود: {field}\")\n                return False\n        return True\n\n# تهيئة مدير مفاتيح Google Search بعد تعريف BotConfig\ntry:\n    google_search_keys = BotConfig.GOOGLE_SEARCH_KEYS\n\n    if google_search_keys:\n        google_search_api_manager = ApiKeyManager(\n            api_keys=google_search_keys,\n            service_name=\"Google Search\",\n            auto_recovery_minutes=30,  # إعادة تفعيل أسرع للبحث\n            load_balancing=True\n        )\n        print(f\"🔑 تم تهيئة مدير مفاتيح Google Search مع {len(google_search_keys)} مفتاح\")\nexcept Exception as e:\n    print(f\"⚠️ خطأ في تهيئة مدير مفاتيح Google Search: {e}\")\n    google_search_api_manager = None\n\nclass SourcesConfig:\n    \"\"\"تكوين مصادر البيانات\"\"\"\n\n    # المواقع الرسمية\n    OFFICIAL_SOURCES = [\n        \"https://www.gamespot.com\",\n        \"https://www.polygon.com\",\n        \"https://www.kotaku.com\",\n        \"https://www.eurogamer.net\",\n        \"https://www.gamesindustry.biz\",\n        \"https://www.gamedeveloper.com\"\n    ]\n\n    # مواقع الألعاب المتخصصة\n    GAMING_SITES = [\n        \"https://ign.com\",\n        \"https://commonsensemedia.org\",\n        \"https://pcgamesn.com\",\n        \"https://www.gamesradar.com\",\n        \"https://www.destructoid.com\",\n        \"https://www.rockpapershotgun.com\",\n        \"https://www.pcgamer.com\",\n        \"https://www.gameinformer.com\",\n        \"https://www.theverge.com/games\",\n        \"https://arstechnica.com/gaming\"\n    ]\n\n    # المواقع العربية\n    ARABIC_SITES = [\n        \"https://vga4a.com\",\n        \"https://www.true-gaming.net\",\n        \"https://saudigamer.com\",\n        # \"https://www.arageek.com/tech/gaming\",  # معطل - 404\n        # \"https://www.tech-wd.com/wd/category/games\",  # معطل - 404\n        \"https://www.i3lam.com/category/games\"\n    ]\n\n    # مواقع المراجعات\n    REVIEW_SITES = [\n        \"https://www.ign.com/reviews/games\",\n        \"https://www.gamespot.com/reviews/\",\n        \"https://www.metacritic.com/game\",\n        \"https://opencritic.com\",\n        \"https://www.giantbomb.com/reviews\"\n    ]\n\n    # المنتديات\n    FORUM_SITES = [\n        # \"https://www.resetera.com/forums/gaming-forum.4/\",  # معطل - 404\n        \"https://www.reddit.com/r/gamingnews/\",\n        # \"https://www.neogaf.com/forums/gaming.2/\",  # معطل - 403 Forbidden\n        \"https://www.reddit.com/r/Games/\",\n        \"https://www.reddit.com/r/gaming/\"\n    ]\n\n    # مصادر الأخبار السريعة\n    NEWS_AGGREGATORS = [\n        \"https://news.google.com/topics/CAAqJggKIiBDQkFTRWdvSUwyMHZNREZqY0hsNUVnVnVaWGR6S0FBUAE\",\n        \"https://www.gamedev.net/news\",\n        \"https://www.gamasutra.com/news\"\n    ]\n\n    # مصادر التحديثات والإعلانات\n    ANNOUNCEMENT_SOURCES = [\n        \"https://blog.playstation.com\",\n        \"https://news.xbox.com\",\n        \"https://www.nintendo.com/us/whatsnew\",\n        \"https://store.steampowered.com/news\",\n        \"https://blog.epicgames.com\"\n    ]\n    \n    # قنوات يوتيوب واستعلامات البحث المحسنة\n    YOUTUBE_CHANNELS = [\n        \"new game trailers 2025\",\n        \"upcoming games 2025\",\n        \"video game news today\",\n        \"أخبار ألعاب الفيديو\",\n        \"game reviews 2025\",\n        \"مراجعات ألعاب جديدة\",\n        \"gameplay footage new\",\n        \"new game releases this week\",\n        \"gaming announcements\",\n        \"indie games 2025\",\n        \"AAA games news\",\n        \"mobile games updates\",\n        \"PC gaming news\",\n        \"console gaming updates\",\n        \"VR games 2025\",\n        \"esports news\",\n        \"gaming industry news\",\n        \"game development updates\",\n        \"sandbox games updates\",\n        \"battle royale news\",\n        \"fps games updates\",\n        \"sports games 2025\",\n        \"open world games news\",\n        \"rpg games updates\",\n        \"indie games news\",\n        \"mobile gaming updates\"\n    ]\n    \n    # حسابات تويتر\n    TWITTER_ACCOUNTS = [\n        \"@IGN\",\n        \"@GameSpot\",\n        \"@PlayStation\",\n        \"@Xbox\"\n    ]\n    \n    # متاجر التطبيقات\n    APP_STORES = [\n        \"https://play.google.com\",\n        \"https://store.steampowered.com\"\n    ]\n\nclass ContentConfig:\n    \"\"\"تكوين المحتوى\"\"\"\n    \n    # أنواع المحتوى\n    CONTENT_TYPES = [\n        \"أخبار_الألعاب\",\n        \"تحديثات_الألعاب\",\n        \"مراجعات_جديدة\",\n        \"عروض_خاصة\",\n        \"مقالات_رأي\"\n    ]\n    \n    # اللهجات المدعومة\n    DIALECTS = {\n        \"standard\": \"العربية الفصحى\",\n        \"egyptian\": \"مصرية\",\n        \"saudi\": \"سعودية\"\n    }\n    DEFAULT_DIALECT = \"standard\"  # اللهجة الافتراضية\n    \n    # تنسيقات النشر على تيليجرام\n    TELEGRAM_FORMATS = [\n        \"ملخص_قصير\",\n        \"نقاط_رئيسية\", \n        \"سؤال_وجواب\",\n        \"اقتباس\",\n        \"صورة_مع_نص\"\n    ]\n    \n    # كلمات مفتاحية أساسية محسنة\n    BASE_KEYWORDS = [\n        \"ألعاب الفيديو\",\n        \"video games\",\n        \"أخبار الألعاب\",\n        \"gaming news\",\n        \"تحديثات الألعاب\",\n        \"game updates\",\n        \"إصدارات جديدة\",\n        \"new releases\",\n        \"مراجعات الألعاب\",\n        \"game reviews\",\n        \"ألعاب 2025\",\n        \"games 2025\",\n        \"ألعاب الكمبيوتر\",\n        \"PC games\",\n        \"ألعاب الموبايل\",\n        \"mobile games\",\n        \"ألعاب الكونسول\",\n        \"console games\",\n        \"ألعاب مجانية\",\n        \"free games\",\n        \"ألعاب مدفوعة\",\n        \"paid games\",\n        \"ألعاب أونلاين\",\n        \"online games\",\n        \"ألعاب أوفلاين\",\n        \"offline games\"\n    ]\n    \n    # فئات المقالات\n    ARTICLE_CATEGORIES = [\n        \"أخبار الألعاب\",\n        \"تحديثات الألعاب\",\n        \"مراجعات وتحليلات\",\n        \"مقالات رأي\",\n        \"أخبار الشركات\"\n    ]\n\nclass SEOConfig:\n    \"\"\"تكوين تحسين محركات البحث المحسن\"\"\"\n\n    # طول العنوان المثالي - تم زيادة الحد الأقصى لحل مشكلة قطع العناوين\n    TITLE_LENGTH_MIN = 30\n    TITLE_LENGTH_MAX = 120  # زيادة من 60 إلى 120 حرف للعناوين العربية\n\n    # طول الوصف التعريفي\n    META_DESCRIPTION_LENGTH = 155\n\n    # كثافة الكلمات المفتاحية\n    KEYWORD_DENSITY = 2.0  # نسبة مئوية محسنة\n\n    # عدد الكلمات المفتاحية لكل مقال\n    MAX_KEYWORDS_PER_ARTICLE = 15\n\n    # قوالب العناوين الجذابة والمحسنة لـ SEO\n    TITLE_TEMPLATES = [\n        \"🔥 خبر عاجل: {content} - أحدث أخبار الألعاب\",\n        \"⚡ دليل شامل: {content} - كل ما تحتاج معرفته\",\n        \"🎮 مراجعة حصرية: {content} - تقييم مفصل\",\n        \"🚀 تحديث جديد: {content} - آخر التطورات\",\n        \"💎 اكتشف: {content} - أفضل الألعاب الجديدة\",\n        \"🏆 أفضل: {content} - قائمة محدثة 2025\",\n        \"📱 أخبار: {content} - تحديثات يومية\",\n        \"🎯 تحليل: {content} - رؤية عميقة\",\n        \"⭐ مميز: {content} - محتوى حصري\",\n        \"🔍 استكشف: {content} - دليل المبتدئين\"\n    ]\n\n    # كلمات مفتاحية عالية الأداء\n    HIGH_PERFORMANCE_KEYWORDS = [\n        \"أفضل الألعاب\",\n        \"best games\",\n        \"مراجعة لعبة\",\n        \"game review\",\n        \"تحديث جديد\",\n        \"new update\",\n        \"ألعاب مجانية\",\n        \"free games\",\n        \"نصائح وحيل\",\n        \"tips and tricks\",\n        \"دليل اللعبة\",\n        \"game guide\"\n    ]\n\n    # عبارات دعوة للعمل\n    CALL_TO_ACTION_PHRASES = [\n        \"شاركنا رأيك في التعليقات\",\n        \"ما رأيكم في هذا التحديث؟\",\n        \"أخبرونا عن تجربتكم مع اللعبة\",\n        \"هل جربتم هذه اللعبة من قبل؟\",\n        \"ما هي لعبتكم المفضلة؟\",\n        \"انتظروا المزيد من المراجعات\",\n        \"تابعونا للحصول على آخر الأخبار\",\n        \"لا تفوتوا أحدث التحديثات\"\n    ]\n\nclass ImageSafetyConfig:\n    \"\"\"تكوين أمان الصور - ImageGuard Pro\"\"\"\n\n    # كلمات آمنة للبحث عن الصور\n    SAFE_KEYWORDS = [\n        'gaming', 'controller', 'console', 'computer', 'technology',\n        'esports', 'digital', 'modern', 'setup', 'workspace', 'keyboard',\n        'mouse', 'headset', 'monitor', 'screen', 'device', 'electronic'\n    ]\n\n    # كلمات محظورة لضمان الامتثال لـ AdSense\n    FORBIDDEN_KEYWORDS = [\n        'violence', 'blood', 'weapon', 'gun', 'fight', 'war', 'battle',\n        'alcohol', 'beer', 'wine', 'cigarette', 'smoking', 'tobacco',\n        'gambling', 'casino', 'poker', 'bet', 'adult', 'sexy', 'nude'\n    ]\n\n    # أولوية مصادر الصور (من الأفضل للأقل)\n    SOURCE_PRIORITY = ['Pexels', 'Pixabay', 'Unsplash']\n\n    # الحد الأدنى لجودة الصور\n    MIN_IMAGE_WIDTH = 400\n    MIN_IMAGE_HEIGHT = 300\n\n    # الرخص المسموحة\n    ALLOWED_LICENSES = [\n        'Pexels License',\n        'Pixabay License',\n        'Unsplash License',\n        'Creative Commons CC0'\n    ]\n\n    # صور احتياطية آمنة (URLs ثابتة)\n    FALLBACK_IMAGES = [\n        {\n            'url': 'https://images.pexels.com/photos/442576/pexels-photo-442576.jpeg',\n            'description': 'Gaming controller on dark background',\n            'license': 'Pexels License',\n            'attribution': 'Photo by Lucie Liz from Pexels'\n        },\n        {\n            'url': 'https://images.pexels.com/photos/1174746/pexels-photo-1174746.jpeg',\n            'description': 'Modern gaming setup with RGB lighting',\n            'license': 'Pexels License',\n            'attribution': 'Photo by FOX from Pexels'\n        },\n        {\n            'url': 'https://images.pexels.com/photos/735911/pexels-photo-735911.jpeg',\n            'description': 'Retro gaming console and controller',\n            'license': 'Pexels License',\n            'attribution': 'Photo by Garrett Morrow from Pexels'\n        },\n        {\n            'url': 'https://images.pexels.com/photos/194511/pexels-photo-194511.jpeg',\n            'description': 'Gaming keyboard with colorful backlighting',\n            'license': 'Pexels License',\n            'attribution': 'Photo by Lukas from Pexels'\n        }\n    ]\n", ".env": "GEMINI_API_KEY=AIzaSyBiMajBlgp6rC6tTvkAUa6s34EG4VeYZxk\nTELEGRAM_BOT_TOKEN=**********:AAGcFo_eJrU0oNQPVWkKe0f9ZMQQUp4F0ss\nTELEGRAM_CHANNEL_ID=@Football_news136\nBLOGGER_CLIENT_ID=261038268830-ud061kj9pv22d4ilbtk74d0p4jnqlhin.apps.googleusercontent.com\nBLOGGER_CLIENT_SECRET=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\nBLOGGER_BLOG_ID=529472572913575745\nSEARCH_INTERVAL_HOURS=2\nTXTIFY_API_URL=https://sidivall-ai-extact.hf.space\nGOOGLE_SEARCH_API_KEY=AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk\nGOOGLE_SEARCH_ENGINE_ID=d73dcbe59d5ca4500\nGOOGLE_API_KEYS_LIST=AIzaSyAxLImn6q9_UOIS14BQ1qu537uohDlT16Y,AIzaSyDjjgZIkGyC0i8RVM9IA14WFJBqmEQHhHE,AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk\nFREEPIK_API_KEY=FPSX1ee910637a8ec349e6d8c7f17a57740b\nFLUXAI_API_KEY=b6863038ac459a1f8cd9e30d82cdd989\nGOOGLE_SEARCH_KEY=AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk\nRAWG_API_KEY=********************************", "config/api_config.py": "# إعدادات APIs المتقدمة\nimport os\nfrom typing import Dict, Optional\n\nclass APIConfig:\n    \"\"\"إعدادات APIs المتقدمة\"\"\"\n    \n    # APIs أساسية (موجودة)\n    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY', '')\n    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', '')\n    YOUTUBE_API_KEY = os.getenv('YOUTUBE_API_KEY', '')\n\n    # APIs متقدمة للـ SEO والتحليل (مفاتيح حقيقية متوفرة!)\n    GOOGLE_SEARCH_CONSOLE_KEY = os.getenv('GOOGLE_SEARCH_CONSOLE_KEY', 'AIzaSyCMRxWg6Egl7_nv6sdQvcXJkgHUNoQM_gY')\n    GOOGLE_ANALYTICS_KEY = os.getenv('GOOGLE_ANALYTICS_KEY', 'AIzaSyBLpimSmuTirR3kLGyaxB1BntcXJQZa12w')\n    GOOGLE_PAGESPEED_KEY = os.getenv('GOOGLE_PAGESPEED_KEY', 'AIzaSyDb18JC4vCNcsjCbOUPR2iqzOVQ-Oa_OUY')\n\n    # Microsoft Clarity (مجاني)\n    MICROSOFT_CLARITY_ID = os.getenv('MICROSOFT_CLARITY_ID', 'sedxi61jhb')\n\n    # Ubersuggest (مجاني جزئياً)\n    UBERSUGGEST_API_KEY = os.getenv('UBERSUGGEST_API_KEY', '24d5c97abaee9b5c8230d1bb50f796de8dd3c629')\n    \n    # APIs أدوات SEO المدفوعة\n    SEMRUSH_API_KEY = os.getenv('SEMRUSH_API_KEY', '')\n    AHREFS_API_KEY = os.getenv('AHREFS_API_KEY', '')\n    MOZ_API_KEY = os.getenv('MOZ_API_KEY', '')\n    \n    # APIs التحليل والمراقبة\n    HOTJAR_API_KEY = os.getenv('HOTJAR_API_KEY', '')\n    MIXPANEL_API_KEY = os.getenv('MIXPANEL_API_KEY', '')\n    FACEBOOK_PIXEL_ID = os.getenv('FACEBOOK_PIXEL_ID', '')\n    \n    # APIs وسائل التواصل الاجتماعي\n    TWITTER_API_KEY = os.getenv('TWITTER_API_KEY', '')\n    TWITTER_API_SECRET = os.getenv('TWITTER_API_SECRET', '')\n    FACEBOOK_API_KEY = os.getenv('FACEBOOK_API_KEY', '')\n    INSTAGRAM_API_KEY = os.getenv('INSTAGRAM_API_KEY', '')\n    \n    # APIs المحتوى والتحليل\n    BUZZSUMO_API_KEY = os.getenv('BUZZSUMO_API_KEY', '')\n    CLEARBIT_API_KEY = os.getenv('CLEARBIT_API_KEY', '')\n    HUNTER_API_KEY = os.getenv('HUNTER_API_KEY', '')\n    \n    # إعدادات Core Web Vitals\n    CORE_WEB_VITALS_CONFIG = {\n        'lcp_threshold_good': 2.5,      # ثانية\n        'lcp_threshold_poor': 4.0,      # ثانية\n        'fid_threshold_good': 100,      # مللي ثانية\n        'fid_threshold_poor': 300,      # مللي ثانية\n        'cls_threshold_good': 0.1,      # نقاط\n        'cls_threshold_poor': 0.25,     # نقاط\n        'monitoring_interval': 3600,    # ثانية (ساعة واحدة)\n        'alert_threshold': 60           # نقاط أقل من 60 = تنبيه\n    }\n    \n    # إعدادات بحث الكلمات المفتاحية\n    KEYWORD_RESEARCH_CONFIG = {\n        'max_keywords_per_request': 100,\n        'min_search_volume': 100,\n        'max_keyword_difficulty': 80,\n        'target_languages': ['ar', 'en'],\n        'competitor_domains': [\n            'gamespot.com',\n            'ign.com',\n            'polygon.com',\n            'kotaku.com',\n            'eurogamer.net',\n            'pcgamer.com',\n            'gamesradar.com'\n        ]\n    }\n    \n    # إعدادات مراقبة الأداء\n    PERFORMANCE_MONITORING_CONFIG = {\n        'monitoring_enabled': True,\n        'monitoring_interval': 3600,    # ساعة واحدة\n        'alert_email': '',              # بريد إلكتروني للتنبيهات\n        'telegram_bot_token': '',       # توكن بوت تيليجرام للتنبيهات\n        'telegram_chat_id': '',         # معرف المحادثة\n        'website_url': 'https://your-gaming-website.com',  # URL موقعك\n        'important_pages': [\n            '/latest-news',\n            '/game-reviews',\n            '/guides',\n            '/trending'\n        ]\n    }\n    \n    # حدود معدل الطلبات\n    RATE_LIMITS = {\n        'google_apis': {\n            'requests_per_minute': 100,\n            'requests_per_day': 10000\n        },\n        'semrush': {\n            'requests_per_minute': 10,\n            'requests_per_day': 1000\n        },\n        'ahrefs': {\n            'requests_per_minute': 20,\n            'requests_per_day': 2000\n        },\n        'social_apis': {\n            'requests_per_minute': 50,\n            'requests_per_day': 5000\n        },\n        'analytics_apis': {\n            'requests_per_minute': 30,\n            'requests_per_day': 3000\n        }\n    }\n    \n    # URLs APIs\n    API_ENDPOINTS = {\n        'google_pagespeed': 'https://www.googleapis.com/pagespeedonline/v5/runPagespeed',\n        'google_search_console': 'https://www.googleapis.com/webmasters/v3',\n        'google_analytics': 'https://analyticsreporting.googleapis.com/v4/reports:batchGet',\n        'semrush_overview': 'https://api.semrush.com/',\n        'ahrefs_overview': 'https://apiv2.ahrefs.com',\n        'buzzsumo_content': 'https://api.buzzsumo.com/search/content',\n        'twitter_api': 'https://api.twitter.com/2',\n        'facebook_graph': 'https://graph.facebook.com/v18.0'\n    }\n    \n    @classmethod\n    def get_api_key(cls, api_name: str) -> Optional[str]:\n        \"\"\"الحصول على مفتاح API\"\"\"\n        api_keys = {\n            'google': cls.GOOGLE_API_KEY,\n            'gemini': cls.GEMINI_API_KEY,\n            'youtube': cls.YOUTUBE_API_KEY,\n            'google_search_console': cls.GOOGLE_SEARCH_CONSOLE_KEY,\n            'google_analytics': cls.GOOGLE_ANALYTICS_KEY,\n            'google_pagespeed': cls.GOOGLE_PAGESPEED_KEY,\n            'semrush': cls.SEMRUSH_API_KEY,\n            'ahrefs': cls.AHREFS_API_KEY,\n            'moz': cls.MOZ_API_KEY,\n            'hotjar': cls.HOTJAR_API_KEY,\n            'mixpanel': cls.MIXPANEL_API_KEY,\n            'twitter': cls.TWITTER_API_KEY,\n            'facebook': cls.FACEBOOK_API_KEY,\n            'instagram': cls.INSTAGRAM_API_KEY,\n            'buzzsumo': cls.BUZZSUMO_API_KEY,\n            'clearbit': cls.CLEARBIT_API_KEY,\n            'hunter': cls.HUNTER_API_KEY\n        }\n        \n        return api_keys.get(api_name)\n    \n    @classmethod\n    def is_api_available(cls, api_name: str) -> bool:\n        \"\"\"فحص توفر API\"\"\"\n        api_key = cls.get_api_key(api_name)\n        return bool(api_key and api_key.strip())\n    \n    @classmethod\n    def get_available_apis(cls) -> Dict[str, bool]:\n        \"\"\"الحصول على قائمة APIs المتوفرة\"\"\"\n        apis = [\n            'google', 'gemini', 'youtube', 'google_search_console',\n            'google_analytics', 'google_pagespeed', 'semrush', 'ahrefs',\n            'moz', 'hotjar', 'mixpanel', 'twitter', 'facebook',\n            'instagram', 'buzzsumo', 'clearbit', 'hunter'\n        ]\n        \n        return {api: cls.is_api_available(api) for api in apis}\n    \n    @classmethod\n    def get_rate_limit(cls, api_category: str) -> Dict:\n        \"\"\"الحصول على حدود معدل الطلبات\"\"\"\n        return cls.RATE_LIMITS.get(api_category, {\n            'requests_per_minute': 10,\n            'requests_per_day': 1000\n        })\n    \n    @classmethod\n    def get_endpoint_url(cls, api_name: str) -> Optional[str]:\n        \"\"\"الحصول على URL نقطة النهاية\"\"\"\n        return cls.API_ENDPOINTS.get(api_name)\n    \n    @classmethod\n    def validate_configuration(cls) -> Dict:\n        \"\"\"التحقق من صحة الإعدادات\"\"\"\n        validation_results = {\n            'valid': True,\n            'warnings': [],\n            'errors': [],\n            'available_apis': 0,\n            'total_apis': 0\n        }\n        \n        # فحص APIs الأساسية\n        essential_apis = ['google', 'gemini']\n        for api in essential_apis:\n            cls.total_apis += 1\n            if cls.is_api_available(api):\n                validation_results['available_apis'] += 1\n            else:\n                validation_results['errors'].append(f\"مفتاح {api} API مطلوب ولكنه غير متوفر\")\n                validation_results['valid'] = False\n        \n        # فحص APIs الاختيارية\n        optional_apis = ['semrush', 'ahrefs', 'google_pagespeed']\n        for api in optional_apis:\n            validation_results['total_apis'] += 1\n            if cls.is_api_available(api):\n                validation_results['available_apis'] += 1\n            else:\n                validation_results['warnings'].append(f\"مفتاح {api} API غير متوفر - سيتم استخدام بيانات محاكاة\")\n        \n        # فحص إعدادات المراقبة\n        if not cls.PERFORMANCE_MONITORING_CONFIG['website_url'].startswith('http'):\n            validation_results['warnings'].append(\"URL الموقع غير صحيح في إعدادات المراقبة\")\n        \n        return validation_results\n\n# دالة مساعدة لإعداد متغيرات البيئة\ndef setup_environment_variables():\n    \"\"\"إعداد متغيرات البيئة للـ APIs\"\"\"\n    env_template = \"\"\"\n# APIs أساسية\nGOOGLE_API_KEY=your_google_api_key_here\nGEMINI_API_KEY=your_gemini_api_key_here\nYOUTUBE_API_KEY=your_youtube_api_key_here\n\n# APIs متقدمة للـ SEO\nGOOGLE_SEARCH_CONSOLE_KEY=your_search_console_key_here\nGOOGLE_ANALYTICS_KEY=your_analytics_key_here\nGOOGLE_PAGESPEED_KEY=your_pagespeed_key_here\n\n# APIs أدوات SEO المدفوعة\nSEMRUSH_API_KEY=your_semrush_key_here\nAHREFS_API_KEY=your_ahrefs_key_here\nMOZ_API_KEY=your_moz_key_here\n\n# APIs التحليل والمراقبة\nHOTJAR_API_KEY=your_hotjar_key_here\nMIXPANEL_API_KEY=your_mixpanel_key_here\nFACEBOOK_PIXEL_ID=your_facebook_pixel_id_here\n\n# APIs وسائل التواصل\nTWITTER_API_KEY=your_twitter_key_here\nTWITTER_API_SECRET=your_twitter_secret_here\nFACEBOOK_API_KEY=your_facebook_key_here\nINSTAGRAM_API_KEY=your_instagram_key_here\n\n# APIs المحتوى والتحليل\nBUZZSUMO_API_KEY=your_buzzsumo_key_here\nCLEARBIT_API_KEY=your_clearbit_key_here\nHUNTER_API_KEY=your_hunter_key_here\n\"\"\"\n    \n    # إنشاء ملف .env إذا لم يكن موجوداً\n    if not os.path.exists('.env'):\n        with open('.env', 'w', encoding='utf-8') as f:\n            f.write(env_template)\n        print(\"✅ تم إنشاء ملف .env - يرجى إضافة مفاتيح APIs الخاصة بك\")\n    else:\n        print(\"⚠️ ملف .env موجود بالفعل\")\n\ndef print_api_status():\n    \"\"\"طباعة حالة APIs\"\"\"\n    print(\"📊 حالة APIs المتوفرة:\")\n    print(\"=\"*50)\n    \n    available_apis = APIConfig.get_available_apis()\n    \n    for api_name, is_available in available_apis.items():\n        status = \"✅ متوفر\" if is_available else \"❌ غير متوفر\"\n        print(f\"  • {api_name}: {status}\")\n    \n    total_available = sum(available_apis.values())\n    total_apis = len(available_apis)\n    \n    print(f\"\\n📈 الإحصائيات:\")\n    print(f\"  • APIs متوفرة: {total_available}/{total_apis}\")\n    print(f\"  • نسبة التوفر: {(total_available/total_apis)*100:.1f}%\")\n    \n    # التحقق من الإعدادات\n    validation = APIConfig.validate_configuration()\n    \n    if validation['errors']:\n        print(f\"\\n❌ أخطاء:\")\n        for error in validation['errors']:\n            print(f\"  • {error}\")\n    \n    if validation['warnings']:\n        print(f\"\\n⚠️ تحذيرات:\")\n        for warning in validation['warnings']:\n            print(f\"  • {warning}\")\n\nif __name__ == \"__main__\":\n    # إعداد متغيرات البيئة\n    setup_environment_variables()\n    \n    # طباعة حالة APIs\n    print_api_status()\n"}