#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار قدرات Gemini 2.5 Pro في الرؤية وتحليل الصور
"""

import asyncio
import sys
import os
import json
import aiohttp
import base64
from datetime import datetime
from typing import Dict, Any

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from config.settings import google_api_manager

class GeminiVisionTester:
    """فئة اختبار قدرات الرؤية في Gemini 2.5 Pro"""
    
    def __init__(self):
        self.enabled = bool(google_api_manager and google_api_manager.get_key())
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.model_name = "gemini-2.5-pro"
        
        # نتائج الاختبار
        self.test_results = {
            'vision_supported': False,
            'image_analysis_available': False,
            'image_description_available': False,
            'ocr_available': False,
            'gaming_content_detection': False,
            'supported_image_formats': [],
            'error_messages': [],
            'test_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"👁️ تم تهيئة اختبار الرؤية Gemini 2.5 Pro - الحالة: {'مفعل' if self.enabled else 'معطل'}")
    
    async def test_vision_capabilities(self) -> Dict[str, Any]:
        """اختبار قدرات الرؤية والتحليل"""
        
        if not self.enabled:
            logger.warning("⚠️ نظام Gemini غير مفعل")
            return self.test_results
        
        print("\n" + "="*60)
        print("👁️ اختبار قدرات الرؤية في Gemini 2.5 Pro")
        print("="*60)
        
        # إنشاء صورة اختبار بسيطة
        test_image_path = await self._create_test_image()
        
        if not test_image_path:
            print("❌ فشل في إنشاء صورة الاختبار")
            return self.test_results
        
        # اختبارات متعددة
        test_cases = [
            {
                'name': 'اختبار تحليل الصورة الأساسي',
                'prompt': 'Describe what you see in this image',
                'image_path': test_image_path
            },
            {
                'name': 'اختبار كشف المحتوى المتعلق بالألعاب',
                'prompt': 'Is this image related to gaming? Describe any gaming elements you can see.',
                'image_path': test_image_path
            },
            {
                'name': 'اختبار OCR (قراءة النص)',
                'prompt': 'Extract any text you can see in this image',
                'image_path': test_image_path
            },
            {
                'name': 'اختبار التحليل المفصل',
                'prompt': 'Provide a detailed analysis of this image including colors, objects, and composition',
                'image_path': test_image_path
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n👁️ {i}. {test_case['name']}")
            print("-" * 40)
            
            try:
                result = await self._test_image_analysis(test_case)
                
                if result['success']:
                    print(f"   ✅ نجح: {result.get('response_preview', 'تم تحليل الصورة')[:100]}...")
                    
                    # تحديث النتائج بناءً على نوع الاختبار
                    if 'تحليل الصورة الأساسي' in test_case['name']:
                        self.test_results['vision_supported'] = True
                        self.test_results['image_analysis_available'] = True
                    elif 'كشف المحتوى' in test_case['name']:
                        self.test_results['gaming_content_detection'] = True
                    elif 'OCR' in test_case['name']:
                        self.test_results['ocr_available'] = True
                    elif 'التحليل المفصل' in test_case['name']:
                        self.test_results['image_description_available'] = True
                        
                else:
                    print(f"   ❌ فشل: {result.get('error', 'خطأ غير معروف')}")
                    self.test_results['error_messages'].append(f"{test_case['name']}: {result.get('error', 'خطأ غير معروف')}")
                    
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                self.test_results['error_messages'].append(f"{test_case['name']}: {str(e)}")
        
        # تنظيف ملف الاختبار
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
        
        # عرض النتائج النهائية
        self._display_vision_results()
        
        return self.test_results
    
    async def _create_test_image(self) -> str:
        """إنشاء صورة اختبار بسيطة"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # إنشاء صورة بسيطة
            width, height = 400, 300
            image = Image.new('RGB', (width, height), color='#1a1a2e')
            draw = ImageDraw.Draw(image)
            
            # رسم شكل بسيط
            draw.rectangle([50, 50, 350, 250], fill='#16213e', outline='#0f3460', width=3)
            
            # إضافة نص
            try:
                # محاولة استخدام خط افتراضي
                font = ImageFont.load_default()
            except:
                font = None
            
            text = "Gaming Test Image"
            if font:
                draw.text((width//2 - 70, height//2 - 10), text, fill='white', font=font)
            else:
                draw.text((width//2 - 70, height//2 - 10), text, fill='white')
            
            # رسم دائرة (تمثل زر تحكم)
            draw.ellipse([320, 80, 340, 100], fill='#e94560', outline='white', width=2)
            
            # حفظ الصورة
            test_image_path = 'test_gaming_image.png'
            image.save(test_image_path, 'PNG')
            
            logger.info(f"✅ تم إنشاء صورة الاختبار: {test_image_path}")
            return test_image_path
            
        except ImportError:
            logger.warning("⚠️ PIL غير متوفر، سيتم استخدام صورة افتراضية")
            # إنشاء صورة بسيطة بدون PIL
            return await self._create_simple_test_image()
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء صورة الاختبار: {e}")
            return None
    
    async def _create_simple_test_image(self) -> str:
        """إنشاء صورة اختبار بسيطة بدون PIL"""
        try:
            # إنشاء صورة SVG بسيطة وتحويلها إلى PNG
            svg_content = '''
            <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <rect width="400" height="300" fill="#1a1a2e"/>
                <rect x="50" y="50" width="300" height="200" fill="#16213e" stroke="#0f3460" stroke-width="3"/>
                <text x="200" y="160" text-anchor="middle" fill="white" font-family="Arial" font-size="20">Gaming Test</text>
                <circle cx="330" cy="90" r="10" fill="#e94560" stroke="white" stroke-width="2"/>
            </svg>
            '''
            
            # حفظ كملف SVG مؤقت
            svg_path = 'test_gaming_image.svg'
            with open(svg_path, 'w') as f:
                f.write(svg_content)
            
            logger.info(f"✅ تم إنشاء صورة SVG للاختبار: {svg_path}")
            return svg_path
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء صورة SVG: {e}")
            return None
    
    async def _test_image_analysis(self, test_case: Dict) -> Dict[str, Any]:
        """اختبار تحليل صورة محددة"""
        
        try:
            # قراءة الصورة وتحويلها إلى base64
            image_data = await self._encode_image_to_base64(test_case['image_path'])
            
            if not image_data:
                return {
                    'success': False,
                    'error': 'فشل في قراءة الصورة'
                }
            
            # إعداد الطلب
            url = f"{self.base_url}/models/{self.model_name}:generateContent"
            
            headers = {
                'Content-Type': 'application/json'
            }
            
            params = {
                'key': google_api_manager.get_key()
            }
            
            # تحديد نوع الصورة
            image_format = 'image/png' if test_case['image_path'].endswith('.png') else 'image/svg+xml'
            
            payload = {
                "contents": [{
                    "parts": [
                        {
                            "text": test_case['prompt']
                        },
                        {
                            "inlineData": {
                                "mimeType": image_format,
                                "data": image_data
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "temperature": 0.4,
                    "topK": 32,
                    "topP": 0.8,
                    "maxOutputTokens": 2048,
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, params=params, json=payload, timeout=60) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'candidates' in data and data['candidates']:
                            content = data['candidates'][0].get('content', {})
                            parts = content.get('parts', [])
                            
                            if parts and 'text' in parts[0]:
                                response_text = parts[0]['text']
                                
                                return {
                                    'success': True,
                                    'response_text': response_text,
                                    'response_preview': response_text[:200],
                                    'has_vision_analysis': True
                                }
                    
                    elif response.status == 400:
                        error_data = await response.json()
                        error_message = error_data.get('error', {}).get('message', 'خطأ في الطلب')
                        
                        return {
                            'success': False,
                            'error': f'خطأ في API: {error_message}',
                            'status_code': response.status
                        }
                    
                    else:
                        error_text = await response.text()
                        return {
                            'success': False,
                            'error': f'خطأ HTTP {response.status}',
                            'details': error_text[:200]
                        }
                        
        except Exception as e:
            return {
                'success': False,
                'error': f'خطأ في الاتصال: {str(e)}'
            }
    
    async def _encode_image_to_base64(self, image_path: str) -> str:
        """تحويل الصورة إلى base64"""
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                encoded_data = base64.b64encode(image_data).decode('utf-8')
                return encoded_data
        except Exception as e:
            logger.error(f"❌ فشل في تحويل الصورة إلى base64: {e}")
            return None
    
    def _display_vision_results(self):
        """عرض نتائج اختبار الرؤية"""
        
        print(f"\n📈 ملخص نتائج اختبار الرؤية:")
        print("=" * 50)
        
        print(f"👁️ دعم الرؤية: {'✅ نعم' if self.test_results['vision_supported'] else '❌ لا'}")
        print(f"🔍 تحليل الصور: {'✅ نعم' if self.test_results['image_analysis_available'] else '❌ لا'}")
        print(f"📝 وصف الصور: {'✅ نعم' if self.test_results['image_description_available'] else '❌ لا'}")
        print(f"📖 قراءة النص (OCR): {'✅ نعم' if self.test_results['ocr_available'] else '❌ لا'}")
        print(f"🎮 كشف محتوى الألعاب: {'✅ نعم' if self.test_results['gaming_content_detection'] else '❌ لا'}")
        
        if self.test_results['error_messages']:
            print(f"\n❌ الأخطاء المكتشفة:")
            for i, error in enumerate(self.test_results['error_messages'], 1):
                print(f"   {i}. {error}")
        
        # التوصيات
        print(f"\n💡 التوصيات:")
        if self.test_results['vision_supported']:
            print("   • ✅ Gemini 2.5 Pro يدعم تحليل الصور بشكل ممتاز")
            print("   • يمكن دمج قدرات الرؤية مع نظام إدارة الصور الحالي")
            print("   • مفيد لتحليل صور الألعاب وكشف المحتوى تلقائياً")
            if self.test_results['gaming_content_detection']:
                print("   • قدرة ممتازة على كشف محتوى الألعاب في الصور")
        else:
            print("   • ❌ قدرات الرؤية محدودة أو غير متاحة")
            print("   • يُنصح بالاعتماد على النظام الحالي لمعالجة الصور")

async def main():
    """الدالة الرئيسية"""
    try:
        tester = GeminiVisionTester()
        
        if not tester.enabled:
            print("❌ لا يمكن تشغيل الاختبار - تحقق من مفتاح API")
            return
        
        # تشغيل اختبار الرؤية
        results = await tester.test_vision_capabilities()
        
        # حفظ النتائج
        report_file = f"gemini_vision_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ التقرير في: {report_file}")
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في التشغيل: {e}")

if __name__ == "__main__":
    asyncio.run(main())
