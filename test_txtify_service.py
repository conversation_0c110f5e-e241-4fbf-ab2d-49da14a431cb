#!/usr/bin/env python3
"""
اختبار خدمة Txtify مع الروابط المختلفة
"""

import requests
import sys
import json
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from config.settings import BotConfig

def test_txtify_endpoints():
    """اختبار جميع endpoints المتاحة لـ Txtify"""
    test_video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    
    # قائمة الروابط للاختبار
    base_urls = [
        "https://nanami34-ai55.hf.space",
        "https://txtify-api.hf.space", 
        "http://localhost:8000",
    ]
    
    endpoints = [
        "/transcribe_youtube",
        "/api/transcribe_youtube",
        "/transcribe",
        "/api/transcribe"
    ]
    
    for base_url in base_urls:
        print(f"\n🔍 اختبار {base_url}...")
        
        for endpoint in endpoints:
            full_url = f"{base_url.rstrip('/')}{endpoint}"
            print(f"  📝 اختبار {full_url}...")
            
            try:
                # تجربة طرق مختلفة
                methods = [
                    {'data': {'youtube_url': test_video_url}},
                    {'json': {'youtube_url': test_video_url}},
                    {'data': {'url': test_video_url}},
                ]
                
                for method in methods:
                    try:
                        response = requests.post(full_url, timeout=30, **method)
                        
                        if response.status_code == 200:
                            data = response.json()
                            transcript = data.get("transcript") or data.get("text") or data.get("result")
                            
                            if transcript:
                                print(f"    ✅ نجح! طول النص: {len(transcript)} حرف")
                                print(f"    📄 عينة من النص: {transcript[:100]}...")
                                return full_url, method
                        else:
                            print(f"    ⚠️ كود الاستجابة: {response.status_code}")
                            
                    except Exception as method_error:
                        print(f"    🔄 فشلت طريقة: {method_error}")
                        continue
                        
            except Exception as e:
                print(f"    ❌ فشل الاتصال: {e}")
                continue
    
    print("\n❌ فشلت جميع المحاولات")
    return None, None

if __name__ == "__main__":
    print("🚀 بدء اختبار خدمة Txtify...")
    working_url, working_method = test_txtify_endpoints()
    
    if working_url:
        print(f"\n✅ تم العثور على endpoint يعمل: {working_url}")
        print(f"📋 الطريقة الناجحة: {working_method}")
    else:
        print("\n❌ لم يتم العثور على endpoint يعمل")
