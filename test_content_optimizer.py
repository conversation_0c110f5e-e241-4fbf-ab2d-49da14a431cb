#!/usr/bin/env python3
"""
أداة اختبار محسن المحتوى
"""

import sys
import json
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

def test_content_optimizer():
    """اختبار محسن المحتوى"""
    print("🧪 بدء اختبار محسن المحتوى...")
    
    try:
        from modules.content_optimizer import ContentOptimizer
        
        # إنشاء محسن المحتوى
        optimizer = ContentOptimizer()
        print("✅ تم إنشاء محسن المحتوى بنجاح")
        
        # بيانات اختبار
        test_article_data = {
            'article_id': 'test_123',
            'title': 'اختبار تحسين المحتوى',
            'performance_metrics': type('obj', (object,), {
                'ctr': 1.0,  # منخفض
                'avg_read_time': 30.0,  # منخفض
                'seo_score': 40.0  # منخفض
            })()
        }
        
        # اختبار الدالة الآمنة إذا كانت متوفرة
        if hasattr(optimizer, 'safe_optimize_article_automatically'):
            print("✅ استخدام الدالة الآمنة للتحسين")
            import asyncio
            result = asyncio.run(optimizer.safe_optimize_article_automatically(test_article_data))
        else:
            print("⚠️ الدالة الآمنة غير متوفرة، استخدام الدالة التقليدية")
            import asyncio
            result = asyncio.run(optimizer.optimize_article_automatically(test_article_data))
        
        print(f"\n📊 نتائج التحسين:")
        if result:
            print(f"عدد العناصر المحسنة: {len(result)}")
            for key, value in result.items():
                print(f"  - {key}: {value.get('improvement_type', 'غير محدد')}")
        else:
            print("لم يتم تحسين أي عناصر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار محسن المحتوى: {e}")
        return False

if __name__ == "__main__":
    success = test_content_optimizer()
    if success:
        print("\n✅ اختبار محسن المحتوى نجح!")
    else:
        print("\n❌ اختبار محسن المحتوى فشل!")
