#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل قاعدة البيانات المتعلقة بـ Whisper
"""

import sqlite3
import json
from datetime import datetime
from modules.database import db
from modules.logger import logger

def diagnose_whisper_table():
    """تشخيص مشاكل جدول whisper_quality_logs"""
    
    print("🔍 تشخيص جدول whisper_quality_logs...")
    print("=" * 50)
    
    try:
        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()
            
            # فحص وجود الجدول
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='whisper_quality_logs'
            """)
            
            table_exists = cursor.fetchone()
            
            if not table_exists:
                print("❌ الجدول غير موجود")
                return False
            
            print("✅ الجدول موجود")
            
            # فحص بنية الجدول
            cursor.execute("PRAGMA table_info(whisper_quality_logs)")
            columns = cursor.fetchall()
            
            print("\n📋 بنية الجدول:")
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                not_null = "NOT NULL" if col[3] else "NULL"
                default_val = f"DEFAULT {col[4]}" if col[4] else "NO DEFAULT"
                print(f"   {col_name}: {col_type} {not_null} {default_val}")
            
            # فحص عدد السجلات
            cursor.execute("SELECT COUNT(*) FROM whisper_quality_logs")
            count = cursor.fetchone()[0]
            print(f"\n📊 عدد السجلات: {count}")
            
            # فحص السجلات التي تحتوي على قيم NULL
            cursor.execute("""
                SELECT COUNT(*) FROM whisper_quality_logs 
                WHERE quality_score IS NULL OR quality_level IS NULL OR is_acceptable IS NULL
            """)
            null_count = cursor.fetchone()[0]
            
            if null_count > 0:
                print(f"⚠️ يوجد {null_count} سجل يحتوي على قيم NULL")
                return False
            else:
                print("✅ جميع السجلات صحيحة")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        return False

def fix_whisper_table():
    """إصلاح جدول whisper_quality_logs"""
    
    print("\n🔧 إصلاح جدول whisper_quality_logs...")
    print("=" * 50)
    
    try:
        # استخدام دالة الإصلاح في قاعدة البيانات
        db._fix_whisper_quality_table()
        print("✅ تم إصلاح الجدول بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ فشل في الإصلاح: {e}")
        return False

def test_whisper_logging():
    """اختبار تسجيل بيانات Whisper"""
    
    print("\n🧪 اختبار تسجيل بيانات Whisper...")
    print("=" * 50)
    
    # بيانات اختبار
    test_data = {
        'timestamp': datetime.now().isoformat(),
        'quality_score': 85.5,
        'quality_level': 'جيد',
        'is_acceptable': True,
        'video_id': 'test_video_123',
        'video_title': 'فيديو اختبار',
        'analysis_type': 'test'
    }
    
    try:
        # محاولة التسجيل
        result = db.log_whisper_quality_check(test_data)
        
        if result:
            print("✅ تم تسجيل البيانات بنجاح")
            
            # التحقق من التسجيل
            with sqlite3.connect(db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM whisper_quality_logs 
                    WHERE video_id = ? 
                    ORDER BY id DESC LIMIT 1
                """, (test_data['video_id'],))
                
                record = cursor.fetchone()
                if record:
                    print("✅ تم التحقق من وجود السجل في قاعدة البيانات")
                    print(f"   ID: {record[0]}")
                    print(f"   النقاط: {record[2]}")
                    print(f"   المستوى: {record[3]}")
                    print(f"   مقبول: {'نعم' if record[4] else 'لا'}")
                else:
                    print("❌ لم يتم العثور على السجل")
                    return False
        else:
            print("❌ فشل في تسجيل البيانات")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_edge_cases():
    """اختبار الحالات الحدية"""
    
    print("\n🎯 اختبار الحالات الحدية...")
    print("=" * 50)
    
    test_cases = [
        {
            'name': 'بيانات فارغة',
            'data': {}
        },
        {
            'name': 'قيم None',
            'data': {
                'quality_score': None,
                'quality_level': None,
                'is_acceptable': None
            }
        },
        {
            'name': 'قيم غير صحيحة',
            'data': {
                'quality_score': 'invalid',
                'quality_level': '',
                'is_acceptable': 'maybe'
            }
        },
        {
            'name': 'بيانات ناقصة',
            'data': {
                'video_id': 'test_incomplete'
            }
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n   {i}. {test_case['name']}:")
        
        try:
            result = db.log_whisper_quality_check(test_case['data'])
            if result:
                print("      ✅ تم التعامل مع الحالة بنجاح")
                success_count += 1
            else:
                print("      ❌ فشل في التعامل مع الحالة")
        except Exception as e:
            print(f"      ❌ خطأ: {e}")
    
    print(f"\n📊 النتائج: {success_count}/{len(test_cases)} حالة نجحت")
    return success_count == len(test_cases)

def clean_old_records():
    """تنظيف السجلات القديمة"""
    
    print("\n🧹 تنظيف السجلات القديمة...")
    print("=" * 50)
    
    try:
        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()
            
            # حذف السجلات الأقدم من 30 يوم
            cursor.execute("""
                DELETE FROM whisper_quality_logs 
                WHERE DATE(timestamp) < DATE('now', '-30 days')
            """)
            
            deleted_count = cursor.rowcount
            conn.commit()
            
            print(f"✅ تم حذف {deleted_count} سجل قديم")
            
            # عرض الإحصائيات الحالية
            cursor.execute("SELECT COUNT(*) FROM whisper_quality_logs")
            remaining_count = cursor.fetchone()[0]
            print(f"📊 السجلات المتبقية: {remaining_count}")
            
            return True
            
    except Exception as e:
        print(f"❌ فشل في التنظيف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 أداة إصلاح مشاكل قاعدة بيانات Whisper")
    print("=" * 60)
    
    # 1. تشخيص المشكلة
    diagnosis_ok = diagnose_whisper_table()
    
    # 2. إصلاح الجدول إذا لزم الأمر
    if not diagnosis_ok:
        print("\n⚠️ تم اكتشاف مشاكل، بدء الإصلاح...")
        fix_ok = fix_whisper_table()
        
        if fix_ok:
            # إعادة التشخيص
            diagnosis_ok = diagnose_whisper_table()
    
    # 3. اختبار التسجيل
    if diagnosis_ok:
        test_ok = test_whisper_logging()
        
        if test_ok:
            # 4. اختبار الحالات الحدية
            edge_cases_ok = test_edge_cases()
            
            # 5. تنظيف السجلات القديمة
            clean_old_records()
            
            if edge_cases_ok:
                print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
                print("✅ قاعدة البيانات جاهزة للاستخدام")
            else:
                print("\n⚠️ بعض الحالات الحدية لا تزال تحتاج عمل")
        else:
            print("\n❌ فشل في اختبار التسجيل")
    else:
        print("\n❌ لا يزال هناك مشاكل في الجدول")
    
    print("\n" + "=" * 60)
    print("انتهى الفحص والإصلاح")

if __name__ == "__main__":
    main()
