#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل جميع الإصلاحات المطلوبة
"""

import os
import sys
import subprocess
from datetime import datetime

def run_script(script_name, description):
    """تشغيل سكريبت إصلاح"""
    print(f"\n{'='*60}")
    print(f"🔧 {description}")
    print(f"{'='*60}")
    
    try:
        if os.path.exists(script_name):
            result = subprocess.run([sys.executable, script_name], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print(f"✅ نجح تشغيل {script_name}")
                if result.stdout:
                    print("📋 المخرجات:")
                    print(result.stdout)
                return True
            else:
                print(f"❌ فشل تشغيل {script_name}")
                if result.stderr:
                    print("🚨 الأخطاء:")
                    print(result.stderr)
                return False
        else:
            print(f"⚠️ الملف غير موجود: {script_name}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل {script_name}: {e}")
        return False

def check_system_status():
    """فحص حالة النظام قبل الإصلاح"""
    print("🔍 فحص حالة النظام...")
    
    # فحص وجود مجلد البيانات
    data_dir = "data"
    if not os.path.exists(data_dir):
        print(f"📁 إنشاء مجلد البيانات: {data_dir}")
        os.makedirs(data_dir, exist_ok=True)
    else:
        print(f"✅ مجلد البيانات موجود: {data_dir}")
    
    # فحص وجود قاعدة البيانات
    db_path = os.path.join(data_dir, "articles.db")
    if os.path.exists(db_path):
        print(f"✅ قاعدة البيانات موجودة: {db_path}")
        # فحص حجم قاعدة البيانات
        size = os.path.getsize(db_path)
        print(f"📊 حجم قاعدة البيانات: {size:,} بايت")
    else:
        print(f"⚠️ قاعدة البيانات غير موجودة: {db_path}")
    
    # فحص وجود ملفات الإعدادات
    config_files = [
        "config/settings.py",
        ".env"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ ملف الإعدادات موجود: {config_file}")
        else:
            print(f"⚠️ ملف الإعدادات مفقود: {config_file}")

def create_backup():
    """إنشاء نسخة احتياطية قبل الإصلاح"""
    print("\n💾 إنشاء نسخة احتياطية...")
    
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        # نسخ قاعدة البيانات
        db_path = "data/articles.db"
        if os.path.exists(db_path):
            import shutil
            backup_db_path = os.path.join(backup_dir, "articles.db")
            shutil.copy2(db_path, backup_db_path)
            print(f"✅ تم نسخ قاعدة البيانات إلى: {backup_db_path}")
        
        # نسخ ملفات الإعدادات
        config_files = [".env", "config/settings.py"]
        for config_file in config_files:
            if os.path.exists(config_file):
                import shutil
                backup_config_path = os.path.join(backup_dir, os.path.basename(config_file))
                shutil.copy2(config_file, backup_config_path)
                print(f"✅ تم نسخ {config_file} إلى: {backup_config_path}")
        
        print(f"✅ تم إنشاء النسخة الاحتياطية في: {backup_dir}")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
        return False

def test_system_after_fixes():
    """اختبار النظام بعد الإصلاحات"""
    print("\n🧪 اختبار النظام بعد الإصلاحات...")
    
    try:
        # اختبار استيراد الوحدات الأساسية
        print("📦 اختبار استيراد الوحدات...")
        
        modules_to_test = [
            "modules.database",
            "modules.logger",
            "modules.content_scraper",
            "modules.content_generator"
        ]
        
        for module_name in modules_to_test:
            try:
                __import__(module_name)
                print(f"✅ {module_name}")
            except ImportError as e:
                print(f"❌ {module_name}: {e}")
        
        # اختبار قاعدة البيانات
        print("\n💾 اختبار قاعدة البيانات...")
        try:
            import sqlite3
            db_path = "data/articles.db"
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # فحص الجداول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [row[0] for row in cursor.fetchall()]
                print(f"📋 الجداول الموجودة: {len(tables)} جدول")
                
                # فحص الفهارس
                cursor.execute("SELECT name FROM sqlite_master WHERE type='index';")
                indexes = [row[0] for row in cursor.fetchall()]
                print(f"📋 الفهارس الموجودة: {len(indexes)} فهرس")
                
                print("✅ قاعدة البيانات تعمل بشكل صحيح")
                
        except Exception as e:
            print(f"❌ مشكلة في قاعدة البيانات: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل جميع الإصلاحات")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # فحص حالة النظام
    check_system_status()
    
    # إنشاء نسخة احتياطية
    backup_success = create_backup()
    if not backup_success:
        print("⚠️ تحذير: لم يتم إنشاء نسخة احتياطية")
    
    # قائمة الإصلاحات
    fixes = [
        ("fix_all_database_issues.py", "إصلاح مشاكل قاعدة البيانات"),
        ("fix_missing_libraries.py", "إصلاح المكتبات المفقودة"),
    ]
    
    # تشغيل الإصلاحات
    results = []
    for script, description in fixes:
        success = run_script(script, description)
        results.append((script, success))
    
    # عرض النتائج
    print(f"\n{'='*60}")
    print("📊 ملخص النتائج")
    print(f"{'='*60}")
    
    success_count = 0
    for script, success in results:
        status = "✅ نجح" if success else "❌ فشل"
        print(f"{status} {script}")
        if success:
            success_count += 1
    
    print(f"\n📈 معدل النجاح: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    # اختبار النظام
    test_system_after_fixes()
    
    if success_count == len(results):
        print("\n🎉 تم إكمال جميع الإصلاحات بنجاح!")
        print("✅ يمكنك الآن تشغيل البرنامج الرئيسي")
        print("🔄 تشغيل: python main.py")
    else:
        print(f"\n⚠️ تم إكمال {success_count} من {len(results)} إصلاحات")
        print("🔍 يرجى مراجعة الأخطاء أعلاه")
    
    print(f"\n⏰ انتهى في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
