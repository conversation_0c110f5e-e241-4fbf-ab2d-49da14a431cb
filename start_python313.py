#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت بدء تشغيل متوافق مع Python 3.13
"""

import os
import sys
import warnings

# إخفاء جميع التحذيرات
warnings.filterwarnings("ignore")

# إضافة وحدات التوافق
compat_dir = os.path.join(os.path.dirname(__file__), "compat_modules")
if os.path.exists(compat_dir):
    sys.path.insert(0, compat_dir)

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# تطبيق إصلاحات التوافق
try:
    # إنشاء وحدة cgi مؤقتة إذا لم تكن موجودة
    try:
        import cgi
    except ImportError:
        # إنشاء وحدة cgi بديلة
        import types
        cgi_module = types.ModuleType('cgi')
        
        # إضافة الدوال الأساسية
        import urllib.parse
        import html
        
        cgi_module.parse_qs = urllib.parse.parse_qs
        cgi_module.parse_qsl = urllib.parse.parse_qsl
        cgi_module.escape = html.escape
        cgi_module.unescape = html.unescape
        
        # إضافة كلاس FieldStorage بديل
        class FieldStorage:
            def __init__(self, *args, **kwargs):
                self.list = []
                self.file = None
                self.filename = None
                self.name = None
                self.value = None
            
            def getvalue(self, key, default=None):
                return default
                
            def getlist(self, key):
                return []
        
        cgi_module.FieldStorage = FieldStorage
        cgi_module.maxlen = 0
        
        # تسجيل الوحدة
        sys.modules['cgi'] = cgi_module
        print("✅ تم إنشاء وحدة cgi بديلة")

except Exception as e:
    print(f"⚠️ تحذير في إصلاحات التوافق: {e}")

# تشغيل البرنامج الرئيسي
if __name__ == "__main__":
    try:
        # استيراد وتشغيل main
        import main
        
        # إنشاء حلقة أحداث جديدة
        import asyncio
        
        # تشغيل البوت
        bot = main.GamingNewsBot()
        asyncio.run(bot.run())
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        import traceback
        traceback.print_exc()
