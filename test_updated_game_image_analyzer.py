#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تحليل صور الألعاب المحدث باستخدام Gemini 2.5 Pro
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.game_image_analyzer import game_image_analyzer
from modules.logger import logger

async def test_article_analysis():
    """اختبار تحليل المقالات لإنشاء الصور"""
    
    print("\n" + "="*80)
    print("🎮 اختبار نظام تحليل صور الألعاب المحدث - Gemini 2.5 Pro")
    print("="*80)
    
    # مقالات اختبار متنوعة
    test_articles = [
        {
            'title': 'Call of Duty: Modern Warfare III - مراجعة شاملة للعبة',
            'content': 'تعتبر لعبة Call of Duty: Modern Warfare III واحدة من أفضل ألعاب الأكشن والقتال في عام 2024. تتميز اللعبة بجرافيك عالي الجودة وأسلوب بصري واقعي مع تأثيرات بصرية مذهلة. تحتوي على شخصيات محاربة وأسلحة متنوعة مع بيئات قتالية متطورة.',
            'keywords': ['call of duty', 'modern warfare', 'fps', 'action', 'military', 'gaming']
        },
        {
            'title': 'The Legend of Zelda: Tears of the Kingdom - مغامرة ملحمية جديدة',
            'content': 'تقدم Nintendo لعبة Zelda الجديدة بأسلوب فني رائع يجمع بين الفانتازيا والمغامرة. تتميز اللعبة بألوان زاهية وتصميم شخصيات مميز مع بيئات طبيعية خلابة وعناصر سحرية متنوعة.',
            'keywords': ['zelda', 'nintendo', 'adventure', 'fantasy', 'rpg', 'gaming']
        },
        {
            'title': 'FIFA 24 - تحديثات جديدة في عالم كرة القدم',
            'content': 'يقدم FIFA 24 تجربة كرة قدم محسنة مع جرافيك واقعي للملاعب واللاعبين. تتميز اللعبة بألوان الملاعب الخضراء الزاهية وتصميم اللاعبين الواقعي مع حركات سلسة ومؤثرات بصرية متطورة.',
            'keywords': ['fifa', 'football', 'soccer', 'sports', 'ea sports', 'gaming']
        },
        {
            'title': 'Cyberpunk 2077: Phantom Liberty - توسعة مستقبلية',
            'content': 'تأخذنا توسعة Phantom Liberty إلى عالم مستقبلي مظلم مليء بالتكنولوجيا المتقدمة. تتميز بأسلوب بصري نيون مع ألوان زرقاء وبرتقالية مشعة، وشخصيات سايبربانك مع تقنيات مستقبلية.',
            'keywords': ['cyberpunk', 'futuristic', 'sci-fi', 'rpg', 'cd projekt', 'gaming']
        }
    ]
    
    print(f"🎮 تحليل {len(test_articles)} مقال...")
    
    results = []
    
    for i, article in enumerate(test_articles, 1):
        print(f"\n🔍 {i}. تحليل مقال: {article['title'][:50]}...")
        print("-" * 60)
        
        try:
            analysis = await game_image_analyzer.analyze_game_for_image_generation(article)
            
            if analysis and analysis.get('confidence_score', 0) > 0.5:
                results.append(analysis)
                
                print(f"✅ نجح التحليل - ثقة: {analysis['confidence_score']:.2f}")
                print(f"🎮 اللعبة: {analysis.get('game_name', 'Unknown')}")
                print(f"🎨 النمط البصري: {analysis.get('visual_style', {}).get('visual_style', 'Unknown')[:100]}...")
                print(f"🌈 لوحة الألوان: {analysis.get('visual_style', {}).get('color_palette', 'Unknown')[:100]}...")
                print(f"🎯 النوع: {analysis.get('visual_style', {}).get('genre', 'Unknown')}")
                print(f"📝 طريقة التحليل: {analysis.get('analysis_method', 'Unknown')}")
                
                if analysis.get('enhanced_prompt'):
                    print(f"💡 Prompt للإنشاء:")
                    print(f"   {analysis['enhanced_prompt'][:150]}...")
                
            else:
                print(f"❌ فشل التحليل أو ثقة منخفضة")
                if analysis:
                    print(f"   ثقة: {analysis.get('confidence_score', 0):.2f}")
                    print(f"   طريقة: {analysis.get('analysis_method', 'Unknown')}")
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    # عرض الإحصائيات
    print(f"\n📊 ملخص النتائج:")
    print("=" * 60)
    
    successful = len(results)
    print(f"نجح: {successful}/{len(test_articles)}")
    
    if successful > 0:
        avg_confidence = sum(r['confidence_score'] for r in results) / successful
        print(f"متوسط الثقة: {avg_confidence:.2f}")
        
        # تحليل طرق التحليل المستخدمة
        methods = {}
        for r in results:
            method = r.get('analysis_method', 'unknown')
            methods[method] = methods.get(method, 0) + 1
        
        print(f"\n🔍 طرق التحليل المستخدمة:")
        for method, count in methods.items():
            print(f"   • {method}: {count} مرة")
        
        # تحليل الأنواع المكتشفة
        genres = {}
        for r in results:
            genre = r.get('visual_style', {}).get('genre', 'unknown')
            genres[genre] = genres.get(genre, 0) + 1
        
        print(f"\n🎯 أنواع الألعاب المكتشفة:")
        for genre, count in genres.items():
            print(f"   • {genre}: {count} مرة")
    
    # عرض إحصائيات النظام
    print(f"\n📈 إحصائيات النظام:")
    stats = game_image_analyzer.get_usage_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # حفظ النتائج
    report = {
        'test_timestamp': datetime.now().isoformat(),
        'total_articles': len(test_articles),
        'successful_analyses': successful,
        'system_stats': stats,
        'detailed_results': [
            {
                'article_title': test_articles[i]['title'],
                'game_name': r.get('game_name', 'Unknown'),
                'confidence_score': r.get('confidence_score', 0),
                'analysis_method': r.get('analysis_method', 'Unknown'),
                'visual_style': r.get('visual_style', {}),
                'enhanced_prompt': r.get('enhanced_prompt', '')[:200]
            }
            for i, r in enumerate(results)
        ]
    }
    
    report_file = f"updated_game_analyzer_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ التقرير في: {report_file}")

async def test_image_analysis():
    """اختبار تحليل الصور الموجودة"""
    
    print(f"\n🖼️ اختبار تحليل الصور الموجودة:")
    print("-" * 60)
    
    # إنشاء صورة اختبار بسيطة
    try:
        from PIL import Image, ImageDraw
        
        # إنشاء صورة اختبار
        img = Image.new('RGB', (400, 300), color='#1a1a2e')
        draw = ImageDraw.Draw(img)
        
        # رسم عناصر ألعاب
        draw.rectangle([50, 50, 350, 250], fill='#16213e', outline='#0f3460', width=3)
        draw.text((120, 140), "Call of Duty", fill='white')
        draw.ellipse([320, 80, 340, 100], fill='#e94560', outline='white', width=2)
        
        test_image_path = 'test_game_image_analysis.png'
        img.save(test_image_path, 'PNG')
        
        # تحليل الصورة
        print(f"📸 تحليل الصورة: {test_image_path}")
        
        analysis = await game_image_analyzer.analyze_image_for_style_extraction(test_image_path)
        
        if analysis:
            print(f"✅ نجح تحليل الصورة - ثقة: {analysis.get('confidence_score', 0):.2f}")
            print(f"🎮 اللعبة المكتشفة: {analysis.get('game_name', 'Unknown')}")
            print(f"🎨 النمط البصري: {analysis.get('visual_style', 'Unknown')[:100]}...")
            print(f"🌈 لوحة الألوان: {analysis.get('color_palette', 'Unknown')[:100]}...")
            print(f"🖼️ نمط الفن: {analysis.get('art_style', 'Unknown')}")
            
            if analysis.get('creation_prompt'):
                print(f"💡 Prompt للإنشاء:")
                print(f"   {analysis['creation_prompt'][:150]}...")
        else:
            print(f"❌ فشل في تحليل الصورة")
        
        # تنظيف الملف
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
            
    except ImportError:
        print("⚠️ PIL غير متوفر، تخطي اختبار تحليل الصور")
    except Exception as e:
        print(f"❌ خطأ في اختبار تحليل الصور: {e}")

async def main():
    """الدالة الرئيسية"""
    try:
        await test_article_analysis()
        await test_image_analysis()
        print("\n🎉 اكتمل اختبار نظام تحليل صور الألعاب المحدث!")
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في التشغيل: {e}")

if __name__ == "__main__":
    asyncio.run(main())
