#!/usr/bin/env python3
"""
أداة تشخيص مشاكل الصوت في نظام Whisper
تساعد في تحديد وحل مشاكل رفع الملفات الصوتية
"""

import sys
import os
import asyncio
import aiohttp
from pathlib import Path

# إضافة المسار الجذر للمشروع
sys.path.append(str(Path(__file__).parent.parent))

from config.audio_settings import AudioConfig, AudioDiagnostics, AudioErrorHandling
from modules.logger import logger
from config.settings import BotConfig

class AudioDiagnosticTool:
    """أداة تشخيص شاملة لمشاكل الصوت"""
    
    def __init__(self):
        self.whisper_api_url = BotConfig.WHISPER_API_URL
        self.whisper_api_key = BotConfig.WHISPER_API_KEY
        self.hf_token = BotConfig.HF_TOKEN
    
    async def diagnose_audio_file(self, audio_data: bytes, video_id: str = "test") -> dict:
        """تشخيص شامل لملف صوتي"""
        logger.info("🔍 بدء التشخيص الشامل للملف الصوتي...")
        
        diagnosis = {
            'file_analysis': {},
            'upload_strategy': {},
            'test_results': {},
            'recommendations': []
        }
        
        # 1. تحليل الملف
        diagnosis['file_analysis'] = AudioDiagnostics.analyze_file_size(audio_data)
        logger.info(f"📊 تحليل الملف: {diagnosis['file_analysis']}")
        
        # 2. اقتراح استراتيجية الرفع
        size_mb = diagnosis['file_analysis']['size_mb']
        diagnosis['upload_strategy'] = AudioDiagnostics.suggest_upload_strategy(size_mb)
        logger.info(f"🎯 استراتيجية الرفع: {diagnosis['upload_strategy']['method']}")
        
        # 3. اختبار الرفع الفعلي
        diagnosis['test_results'] = await self._test_upload_methods(audio_data, video_id)
        
        # 4. توصيات نهائية
        diagnosis['recommendations'] = self._generate_recommendations(diagnosis)
        
        return diagnosis
    
    async def _test_upload_methods(self, audio_data: bytes, video_id: str) -> dict:
        """اختبار طرق الرفع المختلفة"""
        test_results = {
            'direct_upload': False,
            'compressed_upload': False,
            'split_upload': False,
            'alternative_methods': [],
            'successful_method': None,
            'errors': []
        }
        
        async with aiohttp.ClientSession() as session:
            # 1. اختبار الرفع المباشر
            logger.info("🧪 اختبار الرفع المباشر...")
            direct_result = await self._test_direct_upload(session, audio_data, video_id)
            test_results['direct_upload'] = direct_result['success']
            if direct_result['success']:
                test_results['successful_method'] = 'direct'
            else:
                test_results['errors'].append(f"رفع مباشر: {direct_result['error']}")
            
            # 2. اختبار الرفع بعد الضغط
            if not test_results['direct_upload']:
                logger.info("🧪 اختبار الرفع بعد الضغط...")
                compressed_data = await self._compress_for_test(audio_data)
                compressed_result = await self._test_direct_upload(session, compressed_data, f"{video_id}_compressed")
                test_results['compressed_upload'] = compressed_result['success']
                if compressed_result['success']:
                    test_results['successful_method'] = 'compressed'
                else:
                    test_results['errors'].append(f"رفع مضغوط: {compressed_result['error']}")
            
            # 3. اختبار الطرق البديلة
            logger.info("🧪 اختبار الطرق البديلة...")
            alt_results = await self._test_alternative_methods(session, audio_data, video_id)
            test_results['alternative_methods'] = alt_results
            
            if not test_results['successful_method'] and alt_results:
                successful_alt = next((method for method in alt_results if method['success']), None)
                if successful_alt:
                    test_results['successful_method'] = f"alternative_{successful_alt['method']}"
        
        return test_results
    
    async def _test_direct_upload(self, session: aiohttp.ClientSession, audio_data: bytes, video_id: str) -> dict:
        """اختبار الرفع المباشر"""
        try:
            data = aiohttp.FormData()
            data.add_field(
                'file',
                audio_data,
                filename=f'{video_id}_test.mp3',
                content_type='audio/mpeg'
            )
            data.add_field('model', 'whisper-small')
            data.add_field('language', 'en')
            data.add_field('response_format', 'text')
            
            headers = {
                'Authorization': f'Bearer {self.hf_token}',
                'X-API-Key': self.whisper_api_key,
                'User-Agent': 'AudioDiagnosticTool/1.0'
            }
            
            async with session.post(
                self.whisper_api_url, 
                headers=headers, 
                data=data, 
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    result = await response.text()
                    return {
                        'success': True,
                        'response_length': len(result),
                        'status_code': response.status
                    }
                else:
                    error_text = await response.text()
                    return {
                        'success': False,
                        'error': f"HTTP {response.status}: {error_text[:100]}",
                        'status_code': response.status
                    }
        
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'status_code': None
            }
    
    async def _test_alternative_methods(self, session: aiohttp.ClientSession, audio_data: bytes, video_id: str) -> list:
        """اختبار الطرق البديلة"""
        methods = [
            {'model': 'whisper-tiny', 'format': 'text', 'content_type': 'audio/mpeg'},
            {'model': 'whisper-base', 'format': 'json', 'content_type': 'audio/wav'},
            {'model': 'whisper-small', 'format': 'text', 'content_type': 'audio/mp4'},
        ]
        
        results = []
        
        for i, method in enumerate(methods, 1):
            try:
                logger.info(f"🧪 اختبار الطريقة البديلة {i}: {method['model']}")
                
                data = aiohttp.FormData()
                data.add_field(
                    'file',
                    audio_data[:5*1024*1024],  # أول 5 ميجا فقط للاختبار
                    filename=f'{video_id}_alt{i}.mp3',
                    content_type=method['content_type']
                )
                data.add_field('model', method['model'])
                data.add_field('language', 'en')
                data.add_field('response_format', method['format'])
                
                headers = {
                    'Authorization': f'Bearer {self.hf_token}',
                    'X-API-Key': self.whisper_api_key,
                    'User-Agent': f'AudioDiagnosticTool/1.0-Alt{i}'
                }
                
                async with session.post(
                    self.whisper_api_url, 
                    headers=headers, 
                    data=data, 
                    timeout=aiohttp.ClientTimeout(total=20)
                ) as response:
                    success = response.status == 200
                    error = None if success else await response.text()
                    
                    results.append({
                        'method': f"{method['model']}_{method['format']}",
                        'success': success,
                        'status_code': response.status,
                        'error': error[:100] if error else None
                    })
                
                await asyncio.sleep(1)  # تأخير بين الاختبارات
                
            except Exception as e:
                results.append({
                    'method': f"{method['model']}_{method['format']}",
                    'success': False,
                    'status_code': None,
                    'error': str(e)
                })
        
        return results
    
    async def _compress_for_test(self, audio_data: bytes) -> bytes:
        """ضغط الملف للاختبار"""
        # ضغط بسيط - أخذ أول 8 ميجا
        max_size = 8 * 1024 * 1024
        if len(audio_data) > max_size:
            return audio_data[:max_size]
        return audio_data
    
    def _generate_recommendations(self, diagnosis: dict) -> list:
        """إنشاء توصيات بناءً على التشخيص"""
        recommendations = []
        
        file_analysis = diagnosis['file_analysis']
        test_results = diagnosis['test_results']
        
        # توصيات بناءً على حجم الملف
        if file_analysis['status'] == 'too_large':
            recommendations.extend([
                "🔧 الملف كبير جداً - استخدم الضغط أو التقسيم",
                "📦 جرب تقليل جودة الصوت إلى 128kbps",
                "✂️ فكر في تقسيم الملف إلى أجزاء أصغر"
            ])
        
        # توصيات بناءً على نتائج الاختبار
        if test_results['successful_method']:
            recommendations.append(f"✅ الطريقة الناجحة: {test_results['successful_method']}")
        else:
            recommendations.extend([
                "❌ جميع طرق الرفع فشلت",
                "🔄 تحقق من إعدادات API",
                "🌐 تحقق من الاتصال بالإنترنت"
            ])
        
        # توصيات عامة
        recommendations.extend([
            "📊 استخدم AudioConfig.get_model_for_size() لاختيار النموذج المناسب",
            "🎯 طبق الضغط التدريجي حسب الحجم",
            "🔄 استخدم الطرق البديلة عند فشل الطريقة الأساسية"
        ])
        
        return recommendations
    
    def print_diagnosis_report(self, diagnosis: dict):
        """طباعة تقرير التشخيص"""
        print("\n" + "="*60)
        print("🔍 تقرير تشخيص الملف الصوتي")
        print("="*60)
        
        # تحليل الملف
        file_analysis = diagnosis['file_analysis']
        print(f"\n📊 تحليل الملف:")
        print(f"   الحجم: {file_analysis['size_mb']} ميجابايت")
        print(f"   الحالة: {file_analysis['status']}")
        
        # استراتيجية الرفع
        strategy = diagnosis['upload_strategy']
        print(f"\n🎯 استراتيجية الرفع المقترحة:")
        print(f"   الطريقة: {strategy['method']}")
        print(f"   النموذج: {strategy['model']}")
        print(f"   الجودة: {strategy['quality']}")
        
        # نتائج الاختبار
        test_results = diagnosis['test_results']
        print(f"\n🧪 نتائج الاختبار:")
        print(f"   الرفع المباشر: {'✅' if test_results['direct_upload'] else '❌'}")
        print(f"   الرفع المضغوط: {'✅' if test_results['compressed_upload'] else '❌'}")
        print(f"   الطريقة الناجحة: {test_results['successful_method'] or 'لا توجد'}")
        
        # الأخطاء
        if test_results['errors']:
            print(f"\n❌ الأخطاء:")
            for error in test_results['errors']:
                print(f"   • {error}")
        
        # التوصيات
        print(f"\n💡 التوصيات:")
        for rec in diagnosis['recommendations']:
            print(f"   • {rec}")
        
        print("\n" + "="*60)

async def main():
    """الدالة الرئيسية للاختبار"""
    if len(sys.argv) < 2:
        print("الاستخدام: python audio_diagnostics.py <video_id>")
        print("مثال: python audio_diagnostics.py 5Vk3SD8K1tI")
        return
    
    video_id = sys.argv[1]
    
    # محاكاة بيانات صوتية للاختبار
    # في الواقع، ستحتاج لتحميل الصوت الفعلي
    test_audio_data = b"test_audio_data" * (2 * 1024 * 1024)  # 2MB تقريباً
    
    tool = AudioDiagnosticTool()
    diagnosis = await tool.diagnose_audio_file(test_audio_data, video_id)
    tool.print_diagnosis_report(diagnosis)

if __name__ == "__main__":
    asyncio.run(main())
