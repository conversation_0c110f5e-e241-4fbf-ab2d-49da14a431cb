# اختبار شامل لنظام البحث الذكي الجديد
import asyncio
import time
import json
from datetime import datetime
from typing import List, Dict, Any

# استيراد النظام الجديد
from modules.unified_intelligent_search import (
    unified_intelligent_search, 
    UnifiedSearchRequest, 
    SearchMode, 
    SearchPriority
)
from modules.contextual_query_analyzer import contextual_query_analyzer
from modules.semantic_search_engine import semantic_search_engine, SemanticSearchType
from modules.adaptive_learning_system import adaptive_learning_system
from modules.engine_coordination_system import engine_coordination_system
from modules.intelligent_result_evaluator import intelligent_result_evaluator
from modules.logger import logger

class IntelligentSearchTester:
    """فئة اختبار النظام الذكي"""
    
    def __init__(self):
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': [],
            'performance_metrics': {},
            'system_insights': {}
        }
        
        # استعلامات اختبار متنوعة
        self.test_queries = [
            # أخبار عامة
            "latest gaming news",
            "PlayStation 5 news today",
            "Xbox Series X updates",
            
            # مراجعات
            "Cyberpunk 2077 review",
            "best games 2025",
            "FIFA 25 rating",
            
            # أدلة ودروس
            "how to play Elden Ring",
            "Minecraft building guide",
            "gaming setup tutorial",
            
            # أخبار عاجلة
            "breaking gaming announcement",
            "Nintendo Direct news",
            "Steam sale today",
            
            # استعلامات معقدة
            "comparison between PS5 and Xbox Series X performance",
            "upcoming RPG games release dates 2025",
            "esports tournament results today"
        ]
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """تشغيل اختبار شامل للنظام"""
        logger.info("🧪 بدء الاختبار الشامل لنظام البحث الذكي")
        
        start_time = time.time()
        
        try:
            # 1. اختبار الوحدات الفردية
            await self._test_individual_components()
            
            # 2. اختبار أنماط البحث المختلفة
            await self._test_search_modes()
            
            # 3. اختبار الأداء
            await self._test_performance()
            
            # 4. اختبار التعلم والتكيف
            await self._test_learning_adaptation()
            
            # 5. اختبار البحث المجمع
            await self._test_batch_search()
            
            # 6. جمع رؤى النظام
            await self._collect_system_insights()
            
            # حساب النتائج النهائية
            total_time = time.time() - start_time
            self._calculate_final_results(total_time)
            
            logger.info(f"✅ الاختبار الشامل مكتمل في {total_time:.2f} ثانية")
            
            return self.test_results
            
        except Exception as e:
            logger.error(f"❌ فشل في الاختبار الشامل: {e}")
            return self.test_results
    
    async def _test_individual_components(self):
        """اختبار الوحدات الفردية"""
        logger.info("🔍 اختبار الوحدات الفردية")
        
        # اختبار محلل الاستعلامات
        await self._test_query_analyzer()
        
        # اختبار البحث الدلالي
        await self._test_semantic_search()
        
        # اختبار نظام التنسيق
        await self._test_coordination_system()
        
        # اختبار نظام التقييم
        await self._test_evaluation_system()
    
    async def _test_query_analyzer(self):
        """اختبار محلل الاستعلامات"""
        test_name = "Query Analyzer Test"
        
        try:
            test_query = "latest PlayStation 5 games review"
            analysis = await contextual_query_analyzer.analyze_query(test_query)
            
            # فحص النتائج
            success = (
                analysis is not None and
                analysis.intent is not None and
                len(analysis.keywords) > 0 and
                analysis.confidence_score > 0
            )
            
            self._record_test_result(test_name, success, {
                'query': test_query,
                'intent': analysis.intent.value if analysis else None,
                'keywords_count': len(analysis.keywords) if analysis else 0,
                'entities_count': len(analysis.entities) if analysis else 0
            })
            
        except Exception as e:
            self._record_test_result(test_name, False, {'error': str(e)})
    
    async def _test_semantic_search(self):
        """اختبار البحث الدلالي"""
        test_name = "Semantic Search Test"
        
        try:
            test_query = "gaming news"
            semantic_query = await semantic_search_engine.semantic_search(
                test_query, SemanticSearchType.HYBRID
            )
            
            success = (
                semantic_query is not None and
                len(semantic_query.concepts) > 0 and
                len(semantic_query.semantic_expansions) > 0 and
                semantic_query.confidence_score > 0
            )
            
            self._record_test_result(test_name, success, {
                'query': test_query,
                'concepts_count': len(semantic_query.concepts) if semantic_query else 0,
                'expansions_count': len(semantic_query.semantic_expansions) if semantic_query else 0,
                'confidence': semantic_query.confidence_score if semantic_query else 0
            })
            
        except Exception as e:
            self._record_test_result(test_name, False, {'error': str(e)})
    
    async def _test_coordination_system(self):
        """اختبار نظام التنسيق"""
        test_name = "Coordination System Test"
        
        try:
            test_query = "gaming news today"
            results = await engine_coordination_system.coordinate_search(
                query=test_query,
                max_results=5
            )
            
            success = (
                results is not None and
                len(results) > 0 and
                any(result.success for result in results)
            )
            
            self._record_test_result(test_name, success, {
                'query': test_query,
                'engines_count': len(results) if results else 0,
                'successful_engines': sum(1 for r in results if r.success) if results else 0,
                'total_results': sum(len(r.results) for r in results if r.success) if results else 0
            })
            
        except Exception as e:
            self._record_test_result(test_name, False, {'error': str(e)})
    
    async def _test_evaluation_system(self):
        """اختبار نظام التقييم"""
        test_name = "Evaluation System Test"
        
        try:
            # إنشاء نتائج وهمية للاختبار
            dummy_results = [
                {
                    'title': 'Latest Gaming News Today',
                    'content': 'Gaming industry news and updates for today',
                    'url': 'https://example.com/news1',
                    'source': 'gaming-news.com'
                },
                {
                    'title': 'PlayStation 5 Review',
                    'content': 'Comprehensive review of PlayStation 5 console',
                    'url': 'https://example.com/review1',
                    'source': 'gamespot.com'
                }
            ]
            
            test_query = "gaming news"
            evaluations = await intelligent_result_evaluator.evaluate_results(
                results=dummy_results,
                query=test_query
            )
            
            success = (
                evaluations is not None and
                len(evaluations) > 0 and
                all(eval.overall_score >= 0 for eval in evaluations)
            )
            
            self._record_test_result(test_name, success, {
                'query': test_query,
                'results_count': len(dummy_results),
                'evaluations_count': len(evaluations) if evaluations else 0,
                'avg_score': sum(e.overall_score for e in evaluations) / len(evaluations) if evaluations else 0
            })
            
        except Exception as e:
            self._record_test_result(test_name, False, {'error': str(e)})
    
    async def _test_search_modes(self):
        """اختبار أنماط البحث المختلفة"""
        logger.info("🎯 اختبار أنماط البحث المختلفة")
        
        test_query = "PlayStation 5 news"
        
        # اختبار كل نمط
        for mode in SearchMode:
            await self._test_single_search_mode(test_query, mode)
    
    async def _test_single_search_mode(self, query: str, mode: SearchMode):
        """اختبار نمط بحث واحد"""
        test_name = f"Search Mode: {mode.value}"
        
        try:
            request = UnifiedSearchRequest(
                query=query,
                mode=mode,
                max_results=5,
                timeout=20.0
            )
            
            result = await unified_intelligent_search.search(request)
            
            success = (
                result is not None and
                result.total_results >= 0 and
                result.execution_time > 0 and
                result.quality_score >= 0
            )
            
            self._record_test_result(test_name, success, {
                'query': query,
                'mode': mode.value,
                'results_count': result.total_results if result else 0,
                'execution_time': result.execution_time if result else 0,
                'quality_score': result.quality_score if result else 0,
                'engines_used': result.engines_used if result else []
            })
            
        except Exception as e:
            self._record_test_result(test_name, False, {'error': str(e)})
    
    async def _test_performance(self):
        """اختبار الأداء"""
        logger.info("⚡ اختبار الأداء")
        
        performance_queries = self.test_queries[:5]  # أول 5 استعلامات
        execution_times = []
        
        for query in performance_queries:
            start_time = time.time()
            
            try:
                result = await unified_intelligent_search.quick_search(query, max_results=3)
                execution_time = time.time() - start_time
                execution_times.append(execution_time)
                
            except Exception as e:
                logger.error(f"❌ فشل في اختبار الأداء للاستعلام '{query}': {e}")
        
        if execution_times:
            avg_time = sum(execution_times) / len(execution_times)
            max_time = max(execution_times)
            min_time = min(execution_times)
            
            # تقييم الأداء
            performance_grade = "excellent" if avg_time < 5 else "good" if avg_time < 10 else "fair" if avg_time < 20 else "poor"
            
            self.test_results['performance_metrics'] = {
                'average_execution_time': avg_time,
                'max_execution_time': max_time,
                'min_execution_time': min_time,
                'performance_grade': performance_grade,
                'queries_tested': len(execution_times)
            }
            
            self._record_test_result("Performance Test", True, self.test_results['performance_metrics'])
        else:
            self._record_test_result("Performance Test", False, {'error': 'No successful executions'})
    
    async def _test_learning_adaptation(self):
        """اختبار التعلم والتكيف"""
        test_name = "Learning & Adaptation Test"
        
        try:
            # محاكاة عدة عمليات بحث للتعلم
            learning_queries = ["gaming news", "game review", "gaming guide"]
            
            for query in learning_queries:
                await unified_intelligent_search.adaptive_search(query, max_results=3)
            
            # فحص إحصائيات التعلم
            learning_insights = adaptive_learning_system.get_learning_insights()
            
            success = (
                learning_insights is not None and
                learning_insights['performance_metrics']['total_learning_events'] > 0
            )
            
            self._record_test_result(test_name, success, {
                'learning_events': learning_insights['performance_metrics']['total_learning_events'] if learning_insights else 0,
                'patterns_discovered': learning_insights['performance_metrics']['patterns_discovered'] if learning_insights else 0
            })
            
        except Exception as e:
            self._record_test_result(test_name, False, {'error': str(e)})
    
    async def _test_batch_search(self):
        """اختبار البحث المجمع"""
        test_name = "Batch Search Test"
        
        try:
            batch_queries = self.test_queries[:3]  # أول 3 استعلامات
            results = await unified_intelligent_search.batch_search(batch_queries, SearchMode.QUICK)
            
            success = (
                results is not None and
                len(results) > 0 and
                len(results) <= len(batch_queries)
            )
            
            self._record_test_result(test_name, success, {
                'input_queries': len(batch_queries),
                'successful_results': len(results) if results else 0,
                'total_results_found': sum(r.total_results for r in results) if results else 0
            })
            
        except Exception as e:
            self._record_test_result(test_name, False, {'error': str(e)})
    
    async def _collect_system_insights(self):
        """جمع رؤى النظام"""
        try:
            self.test_results['system_insights'] = unified_intelligent_search.get_system_insights()
        except Exception as e:
            logger.error(f"❌ فشل في جمع رؤى النظام: {e}")
    
    def _record_test_result(self, test_name: str, success: bool, details: Dict[str, Any]):
        """تسجيل نتيجة اختبار"""
        self.test_results['total_tests'] += 1
        
        if success:
            self.test_results['passed_tests'] += 1
            status = "PASSED"
        else:
            self.test_results['failed_tests'] += 1
            status = "FAILED"
        
        self.test_results['test_details'].append({
            'test_name': test_name,
            'status': status,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info(f"📋 {test_name}: {status}")
    
    def _calculate_final_results(self, total_time: float):
        """حساب النتائج النهائية"""
        total_tests = self.test_results['total_tests']
        passed_tests = self.test_results['passed_tests']
        
        success_rate = (passed_tests / total_tests) if total_tests > 0 else 0
        
        self.test_results['summary'] = {
            'total_execution_time': total_time,
            'success_rate': success_rate,
            'grade': 'excellent' if success_rate >= 0.9 else 'good' if success_rate >= 0.7 else 'fair' if success_rate >= 0.5 else 'poor',
            'recommendations': self._generate_test_recommendations(success_rate)
        }
    
    def _generate_test_recommendations(self, success_rate: float) -> List[str]:
        """إنشاء توصيات بناءً على نتائج الاختبار"""
        recommendations = []
        
        if success_rate < 0.7:
            recommendations.append("مراجعة الوحدات الفاشلة وإصلاح الأخطاء")
        
        if self.test_results['performance_metrics'].get('average_execution_time', 0) > 10:
            recommendations.append("تحسين الأداء وتقليل أوقات الاستجابة")
        
        if success_rate >= 0.9:
            recommendations.append("النظام يعمل بكفاءة عالية - جاهز للإنتاج")
        
        return recommendations

# دالة تشغيل الاختبار
async def run_intelligent_search_test():
    """تشغيل اختبار النظام الذكي"""
    tester = IntelligentSearchTester()
    results = await tester.run_comprehensive_test()
    
    # طباعة النتائج
    print("\n" + "="*50)
    print("🧪 نتائج اختبار نظام البحث الذكي")
    print("="*50)
    print(f"إجمالي الاختبارات: {results['total_tests']}")
    print(f"الاختبارات الناجحة: {results['passed_tests']}")
    print(f"الاختبارات الفاشلة: {results['failed_tests']}")
    print(f"معدل النجاح: {results['summary']['success_rate']:.2%}")
    print(f"التقييم: {results['summary']['grade']}")
    print(f"وقت التنفيذ الإجمالي: {results['summary']['total_execution_time']:.2f} ثانية")
    
    if results['summary']['recommendations']:
        print("\n📋 التوصيات:")
        for rec in results['summary']['recommendations']:
            print(f"  • {rec}")
    
    return results

if __name__ == "__main__":
    asyncio.run(run_intelligent_search_test())
