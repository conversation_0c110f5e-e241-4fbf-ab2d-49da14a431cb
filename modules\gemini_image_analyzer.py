#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محلل الصور الذكي باستخدام Gemini 2.5 Pro
يوفر قدرات OCR وكشف محتوى الألعاب
"""

import asyncio
import aiohttp
import base64
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from .logger import logger
from config.settings import google_api_manager

@dataclass
class ImageAnalysisResult:
    """نتيجة تحليل الصورة"""
    image_path: str
    has_text: bool
    extracted_text: str
    is_gaming_related: bool
    gaming_confidence: float
    gaming_elements: List[str]
    analysis_timestamp: datetime
    success: bool
    error_message: Optional[str] = None

class GeminiImageAnalyzer:
    """محلل الصور الذكي باستخدام Gemini 2.5 Pro"""
    
    def __init__(self):
        self.enabled = bool(google_api_manager and google_api_manager.get_key())
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.model_name = "gemini-2.5-pro"
        
        # إعدادات التحليل
        self.analysis_config = {
            'timeout': 30,
            'max_retries': 2,
            'retry_delay': 3,
            'max_tokens': 1024,
            'temperature': 0.3  # دقة أعلى للتحليل
        }
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'ocr_extractions': 0,
            'gaming_detections': 0,
            'daily_usage': 0,
            'last_reset': datetime.now().date()
        }
        
        # قوالب التحليل
        self.analysis_prompts = {
            'ocr': """
            Extract all visible text from this image. 
            Return only the text content, nothing else.
            If no text is visible, respond with "NO_TEXT_FOUND".
            """,
            
            'gaming_detection': """
            Analyze this image and determine if it's related to gaming.
            
            Look for these gaming elements:
            - Gaming controllers, keyboards, mice
            - Game screenshots or interfaces
            - Gaming setups, monitors, RGB lighting
            - Game characters, logos, or artwork
            - Gaming hardware or accessories
            - Esports or gaming events
            
            Respond in this exact format:
            GAMING_RELATED: [YES/NO]
            CONFIDENCE: [0.0-1.0]
            ELEMENTS: [list gaming elements found, separated by commas]
            """,
            
            'game_info_extraction': """
            Analyze this gaming-related image and extract information:
            
            Look for:
            - Game title or name
            - Platform (PC, PlayStation, Xbox, Nintendo, Mobile)
            - Genre indicators
            - UI elements or game interface
            - Character names or game elements
            
            Respond in this format:
            GAME_NAME: [name if visible, or UNKNOWN]
            PLATFORM: [platform if identifiable, or UNKNOWN]
            GENRE: [genre if identifiable, or UNKNOWN]
            UI_ELEMENTS: [list UI elements found]
            """
        }
        
        logger.info(f"🔍 تم تهيئة محلل الصور Gemini 2.5 Pro - الحالة: {'مفعل' if self.enabled else 'معطل'}")
    
    async def analyze_image(self, image_path: str, analysis_types: List[str] = None) -> ImageAnalysisResult:
        """تحليل شامل للصورة"""
        
        if not self.enabled:
            logger.warning("⚠️ محلل الصور Gemini غير مفعل")
            return ImageAnalysisResult(
                image_path=image_path,
                has_text=False,
                extracted_text="",
                is_gaming_related=False,
                gaming_confidence=0.0,
                gaming_elements=[],
                analysis_timestamp=datetime.now(),
                success=False,
                error_message="محلل الصور غير مفعل"
            )
        
        if not os.path.exists(image_path):
            logger.error(f"❌ الصورة غير موجودة: {image_path}")
            return ImageAnalysisResult(
                image_path=image_path,
                has_text=False,
                extracted_text="",
                is_gaming_related=False,
                gaming_confidence=0.0,
                gaming_elements=[],
                analysis_timestamp=datetime.now(),
                success=False,
                error_message="الصورة غير موجودة"
            )
        
        # تحديد أنواع التحليل المطلوبة
        if analysis_types is None:
            analysis_types = ['ocr', 'gaming_detection']
        
        self.usage_stats['total_analyses'] += 1
        self.usage_stats['daily_usage'] += 1
        
        logger.info(f"🔍 بدء تحليل الصورة: {os.path.basename(image_path)}")
        
        try:
            # تحويل الصورة إلى base64
            image_data = await self._encode_image_to_base64(image_path)
            if not image_data:
                raise Exception("فشل في قراءة الصورة")
            
            # تنفيذ التحليلات المطلوبة
            results = {}
            
            for analysis_type in analysis_types:
                if analysis_type in self.analysis_prompts:
                    result = await self._perform_analysis(image_data, analysis_type, image_path)
                    results[analysis_type] = result
                    
                    # تأخير قصير بين التحليلات
                    if len(analysis_types) > 1:
                        await asyncio.sleep(1)
            
            # معالجة النتائج وإنشاء التقرير النهائي
            analysis_result = self._process_analysis_results(image_path, results)
            
            if analysis_result.success:
                self.usage_stats['successful_analyses'] += 1
                if analysis_result.has_text:
                    self.usage_stats['ocr_extractions'] += 1
                if analysis_result.is_gaming_related:
                    self.usage_stats['gaming_detections'] += 1
            else:
                self.usage_stats['failed_analyses'] += 1
            
            logger.info(f"✅ تم تحليل الصورة بنجاح: {os.path.basename(image_path)}")
            return analysis_result
            
        except Exception as e:
            self.usage_stats['failed_analyses'] += 1
            logger.error(f"❌ فشل في تحليل الصورة {image_path}: {e}")
            
            return ImageAnalysisResult(
                image_path=image_path,
                has_text=False,
                extracted_text="",
                is_gaming_related=False,
                gaming_confidence=0.0,
                gaming_elements=[],
                analysis_timestamp=datetime.now(),
                success=False,
                error_message=str(e)
            )
    
    async def _perform_analysis(self, image_data: str, analysis_type: str, image_path: str) -> Dict[str, Any]:
        """تنفيذ نوع تحليل محدد"""
        
        try:
            url = f"{self.base_url}/models/{self.model_name}:generateContent"
            
            headers = {
                'Content-Type': 'application/json'
            }
            
            params = {
                'key': google_api_manager.get_key()
            }
            
            # تحديد نوع الصورة
            image_format = 'image/png' if image_path.lower().endswith('.png') else 'image/jpeg'
            
            payload = {
                "contents": [{
                    "parts": [
                        {
                            "text": self.analysis_prompts[analysis_type]
                        },
                        {
                            "inlineData": {
                                "mimeType": image_format,
                                "data": image_data
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "temperature": self.analysis_config['temperature'],
                    "topK": 32,
                    "topP": 0.8,
                    "maxOutputTokens": self.analysis_config['max_tokens'],
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url, 
                    headers=headers, 
                    params=params, 
                    json=payload, 
                    timeout=self.analysis_config['timeout']
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'candidates' in data and data['candidates']:
                            content = data['candidates'][0].get('content', {})
                            parts = content.get('parts', [])
                            
                            if parts and 'text' in parts[0]:
                                response_text = parts[0]['text'].strip()
                                
                                return {
                                    'success': True,
                                    'response': response_text,
                                    'analysis_type': analysis_type
                                }
                    
                    # في حالة فشل الطلب
                    error_text = await response.text()
                    return {
                        'success': False,
                        'error': f'HTTP {response.status}: {error_text[:100]}',
                        'analysis_type': analysis_type
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'analysis_type': analysis_type
            }
    
    def _process_analysis_results(self, image_path: str, results: Dict[str, Dict]) -> ImageAnalysisResult:
        """معالجة نتائج التحليل وإنشاء التقرير النهائي"""
        
        # قيم افتراضية
        has_text = False
        extracted_text = ""
        is_gaming_related = False
        gaming_confidence = 0.0
        gaming_elements = []
        success = False
        error_messages = []
        
        # معالجة نتائج OCR
        if 'ocr' in results:
            ocr_result = results['ocr']
            if ocr_result['success']:
                response = ocr_result['response']
                if response and response != "NO_TEXT_FOUND":
                    has_text = True
                    extracted_text = response
                success = True
            else:
                error_messages.append(f"OCR: {ocr_result['error']}")
        
        # معالجة نتائج كشف الألعاب
        if 'gaming_detection' in results:
            gaming_result = results['gaming_detection']
            if gaming_result['success']:
                response = gaming_result['response']
                
                # تحليل الاستجابة المنظمة
                lines = response.split('\n')
                for line in lines:
                    line = line.strip()
                    if line.startswith('GAMING_RELATED:'):
                        is_gaming_related = 'YES' in line.upper()
                    elif line.startswith('CONFIDENCE:'):
                        try:
                            confidence_str = line.split(':')[1].strip()
                            gaming_confidence = float(confidence_str)
                        except:
                            gaming_confidence = 0.5 if is_gaming_related else 0.0
                    elif line.startswith('ELEMENTS:'):
                        elements_str = line.split(':', 1)[1].strip()
                        if elements_str and elements_str != 'NONE':
                            gaming_elements = [e.strip() for e in elements_str.split(',') if e.strip()]
                
                success = True
            else:
                error_messages.append(f"Gaming Detection: {gaming_result['error']}")
        
        # تحديد النجاح الإجمالي
        overall_success = success and len(error_messages) == 0
        
        return ImageAnalysisResult(
            image_path=image_path,
            has_text=has_text,
            extracted_text=extracted_text,
            is_gaming_related=is_gaming_related,
            gaming_confidence=gaming_confidence,
            gaming_elements=gaming_elements,
            analysis_timestamp=datetime.now(),
            success=overall_success,
            error_message='; '.join(error_messages) if error_messages else None
        )
    
    async def _encode_image_to_base64(self, image_path: str) -> Optional[str]:
        """تحويل الصورة إلى base64"""
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                encoded_data = base64.b64encode(image_data).decode('utf-8')
                return encoded_data
        except Exception as e:
            logger.error(f"❌ فشل في تحويل الصورة إلى base64: {e}")
            return None
    
    async def batch_analyze_images(self, image_paths: List[str], analysis_types: List[str] = None) -> List[ImageAnalysisResult]:
        """تحليل مجموعة من الصور"""
        
        logger.info(f"🔍 بدء تحليل مجموعة من {len(image_paths)} صورة")
        
        results = []
        for i, image_path in enumerate(image_paths, 1):
            logger.info(f"🔍 تحليل الصورة {i}/{len(image_paths)}: {os.path.basename(image_path)}")
            
            result = await self.analyze_image(image_path, analysis_types)
            results.append(result)
            
            # تأخير بين الصور لتجنب تجاوز الحدود
            if i < len(image_paths):
                await asyncio.sleep(2)
        
        successful_analyses = sum(1 for r in results if r.success)
        logger.info(f"✅ تم تحليل {successful_analyses}/{len(image_paths)} صورة بنجاح")
        
        return results
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخدام"""
        
        total_analyses = self.usage_stats['total_analyses']
        success_rate = (self.usage_stats['successful_analyses'] / total_analyses * 100) if total_analyses > 0 else 0
        
        return {
            'enabled': self.enabled,
            'model': self.model_name,
            'total_analyses': total_analyses,
            'successful_analyses': self.usage_stats['successful_analyses'],
            'failed_analyses': self.usage_stats['failed_analyses'],
            'success_rate': round(success_rate, 2),
            'ocr_extractions': self.usage_stats['ocr_extractions'],
            'gaming_detections': self.usage_stats['gaming_detections'],
            'daily_usage': self.usage_stats['daily_usage'],
            'last_reset': self.usage_stats['last_reset']
        }

# إنشاء مثيل عام
gemini_image_analyzer = GeminiImageAnalyzer()
