# واجهة البحث الذكي الموحدة - الجيل الجديد
import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum

from .logger import logger
from .intelligent_search_manager import intelligent_search_manager, SearchStrategy, SearchContext
from .api_usage_manager import api_usage_manager, APIProvider, UsagePriority, get_cached_result, cache_result
from .local_search_engine import local_search_engine
from .contextual_query_analyzer import contextual_query_analyzer, QueryAnalysis
from .adaptive_learning_system import adaptive_learning_system
from .engine_coordination_system import engine_coordination_system, CoordinationStrategy, EngineCapability
from .semantic_search_engine import semantic_search_engine, SemanticSearchType
from .intelligent_result_evaluator import intelligent_result_evaluator, EvaluationMethod

class SearchMode(Enum):
    """أنماط البحث"""
    QUICK = "quick"                 # بحث سريع
    COMPREHENSIVE = "comprehensive" # بحث شامل
    INTELLIGENT = "intelligent"     # بحث ذكي
    SEMANTIC = "semantic"          # بحث دلالي
    ADAPTIVE = "adaptive"          # بحث تكيفي
    EXPERT = "expert"              # بحث خبير

class SearchPriority(Enum):
    """أولوية البحث"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5

@dataclass
class UnifiedSearchRequest:
    """طلب بحث موحد"""
    query: str
    mode: SearchMode = SearchMode.INTELLIGENT
    priority: SearchPriority = SearchPriority.NORMAL
    max_results: int = 10
    timeout: float = 30.0
    context: Optional[SearchContext] = None
    use_ai_enhancement: bool = True
    use_semantic_analysis: bool = True
    use_learning: bool = True
    custom_weights: Optional[Dict[str, float]] = None
    metadata: Dict[str, Any] = None

@dataclass
class UnifiedSearchResult:
    """نتيجة بحث موحدة"""
    query: str
    results: List[Dict[str, Any]]
    total_results: int
    execution_time: float
    search_strategy: str
    engines_used: List[str]
    quality_score: float
    relevance_score: float
    semantic_analysis: Optional[Dict] = None
    learning_insights: Optional[Dict] = None
    recommendations: List[str] = None
    metadata: Dict[str, Any] = None

class UnifiedIntelligentSearch:
    """واجهة البحث الذكي الموحدة - الجيل الجديد"""
    
    def __init__(self):
        # إحصائيات النظام الموحد
        self.unified_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'mode_usage': {mode.value: 0 for mode in SearchMode},
            'average_execution_time': 0.0,
            'average_quality_score': 0.0,
            'total_results_found': 0
        }
        
        # إعدادات النظام
        self.system_config = {
            'enable_caching': True,
            'enable_learning': True,
            'enable_coordination': True,
            'enable_semantic_analysis': True,
            'enable_ai_enhancement': True,
            'max_concurrent_searches': 3,
            'default_timeout': 30.0,
            'quality_threshold': 0.6
        }
        
        logger.info("🚀 تم تهيئة واجهة البحث الذكي الموحدة")
    
    async def search(self, request: UnifiedSearchRequest) -> UnifiedSearchResult:
        """البحث الذكي الموحد الرئيسي"""
        
        start_time = time.time()
        self.unified_stats['total_searches'] += 1
        self.unified_stats['mode_usage'][request.mode.value] += 1
        
        try:
            logger.info(f"🔍 بدء البحث الذكي: '{request.query}' | نمط: {request.mode.value}")
            
            # 1. تحليل الاستعلام
            query_analysis = await self._analyze_query(request)
            
            # 2. اختيار الاستراتيجية المثلى
            search_strategy = await self._select_optimal_strategy(request, query_analysis)
            
            # 3. تنفيذ البحث
            search_results = await self._execute_unified_search(request, query_analysis, search_strategy)
            
            # 4. تقييم وترتيب النتائج
            evaluated_results = await self._evaluate_and_rank_results(request, search_results, query_analysis)
            
            # 5. التعلم من النتائج
            learning_insights = await self._learn_from_search(request, evaluated_results, query_analysis)
            
            # 6. إنشاء النتيجة الموحدة
            unified_result = await self._create_unified_result(
                request, evaluated_results, search_strategy, learning_insights, start_time
            )
            
            # تحديث الإحصائيات
            self._update_unified_stats(unified_result)
            
            logger.info(f"✅ البحث مكتمل: {len(unified_result.results)} نتيجة في {unified_result.execution_time:.2f}ث")
            
            self.unified_stats['successful_searches'] += 1
            return unified_result
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث الموحد: {e}")
            self.unified_stats['failed_searches'] += 1
            
            # إرجاع نتيجة فارغة مع معلومات الخطأ
            return UnifiedSearchResult(
                query=request.query,
                results=[],
                total_results=0,
                execution_time=time.time() - start_time,
                search_strategy="failed",
                engines_used=[],
                quality_score=0.0,
                relevance_score=0.0,
                recommendations=[f"فشل في البحث: {str(e)}"],
                metadata={'error': str(e)}
            )
    
    async def _analyze_query(self, request: UnifiedSearchRequest) -> Optional[QueryAnalysis]:
        """تحليل الاستعلام"""
        try:
            if not self.system_config['enable_semantic_analysis']:
                return None
            
            analysis = await contextual_query_analyzer.analyze_query(
                request.query, 
                use_ai_enhancement=request.use_ai_enhancement and self.system_config['enable_ai_enhancement']
            )
            
            logger.debug(f"📊 تحليل الاستعلام: {analysis.intent.value} | {analysis.complexity.value}")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ فشل في تحليل الاستعلام: {e}")
            return None
    
    async def _select_optimal_strategy(self, request: UnifiedSearchRequest, analysis: Optional[QueryAnalysis]) -> Dict[str, Any]:
        """اختيار الاستراتيجية المثلى"""
        try:
            strategy_info = {
                'search_strategy': SearchStrategy.HYBRID,
                'coordination_strategy': CoordinationStrategy.ADAPTIVE,
                'semantic_type': SemanticSearchType.HYBRID,
                'evaluation_method': EvaluationMethod.HYBRID,
                'reasoning': []
            }
            
            # اختيار بناءً على نمط البحث
            if request.mode == SearchMode.QUICK:
                strategy_info['search_strategy'] = SearchStrategy.COLLABORATIVE
                strategy_info['coordination_strategy'] = CoordinationStrategy.PARALLEL
                strategy_info['reasoning'].append("نمط سريع - استراتيجية متوازية")
                
            elif request.mode == SearchMode.COMPREHENSIVE:
                strategy_info['search_strategy'] = SearchStrategy.ADAPTIVE
                strategy_info['coordination_strategy'] = CoordinationStrategy.SEQUENTIAL
                strategy_info['reasoning'].append("نمط شامل - استراتيجية تكيفية")
                
            elif request.mode == SearchMode.SEMANTIC:
                strategy_info['search_strategy'] = SearchStrategy.SEMANTIC
                strategy_info['semantic_type'] = SemanticSearchType.SEMANTIC
                strategy_info['reasoning'].append("نمط دلالي - تركيز على المعنى")
                
            elif request.mode == SearchMode.ADAPTIVE:
                # استخدام التعلم لاختيار الاستراتيجية
                if self.system_config['enable_learning'] and analysis:
                    context = {
                        'intent': analysis.intent.value,
                        'complexity': analysis.complexity.value,
                        'urgency': analysis.urgency_level
                    }
                    optimal_strategy, confidence = await adaptive_learning_system.get_optimal_strategy(context)
                    strategy_info['search_strategy'] = SearchStrategy(optimal_strategy)
                    strategy_info['reasoning'].append(f"تعلم تكيفي - ثقة: {confidence:.2f}")
            
            # تحسين بناءً على الأولوية
            if request.priority in [SearchPriority.URGENT, SearchPriority.CRITICAL]:
                strategy_info['coordination_strategy'] = CoordinationStrategy.PARALLEL
                strategy_info['reasoning'].append("أولوية عالية - تنفيذ متوازي")
            
            # تحسين بناءً على التحليل
            if analysis:
                if analysis.urgency_level >= 4:
                    strategy_info['search_strategy'] = SearchStrategy.COLLABORATIVE
                    strategy_info['reasoning'].append("إلحاح عالي - استراتيجية تعاونية")
                
                if len(analysis.entities) > 3:
                    strategy_info['semantic_type'] = SemanticSearchType.CONCEPTUAL
                    strategy_info['reasoning'].append("كيانات متعددة - بحث مفاهيمي")
            
            logger.debug(f"🎯 استراتيجية مختارة: {strategy_info['search_strategy'].value}")
            return strategy_info
            
        except Exception as e:
            logger.error(f"❌ فشل في اختيار الاستراتيجية: {e}")
            return {
                'search_strategy': SearchStrategy.HYBRID,
                'coordination_strategy': CoordinationStrategy.ADAPTIVE,
                'semantic_type': SemanticSearchType.HYBRID,
                'evaluation_method': EvaluationMethod.HYBRID,
                'reasoning': ['استراتيجية افتراضية بسبب خطأ']
            }
    
    async def _execute_unified_search(self, 
                                    request: UnifiedSearchRequest, 
                                    analysis: Optional[QueryAnalysis],
                                    strategy: Dict[str, Any]) -> List[Dict]:
        """تنفيذ البحث الموحد"""
        try:
            all_results = []
            
            # تحديد القدرات المطلوبة
            required_capabilities = [EngineCapability.NEWS_SEARCH]
            if analysis:
                if analysis.intent.value == 'find_reviews':
                    required_capabilities.append(EngineCapability.DEEP_ANALYSIS)
                elif analysis.urgency_level >= 4:
                    required_capabilities.append(EngineCapability.REAL_TIME)
            
            # فحص التخزين المؤقت أولاً
            cached_results = await get_cached_result(f"unified_{request.query}", APIProvider.LOCAL)
            if cached_results and request.mode != SearchMode.QUICK:
                logger.info("💾 استخدام نتائج محفوظة من البحث الموحد")
                all_results = cached_results.get('results', [])
            else:
                # تنفيذ البحث الذكي الرئيسي
                if self.system_config['enable_coordination']:
                    coordination_results = await engine_coordination_system.coordinate_search(
                        query=request.query,
                        required_capabilities=required_capabilities,
                        strategy=strategy['coordination_strategy'],
                        max_results=request.max_results,
                        priority=request.priority.value,
                        context={'analysis': asdict(analysis) if analysis else {}}
                    )

                    # جمع النتائج من جميع المحركات
                    for engine_result in coordination_results:
                        if engine_result.success:
                            all_results.extend(engine_result.results)

                # إذا لم نحصل على نتائج كافية، استخدم البحث المحلي
                if len(all_results) < request.max_results // 2:
                    logger.info("🏠 استخدام البحث المحلي كمكمل")
                    local_results = await local_search_engine.search(
                        request.query,
                        max_results=request.max_results - len(all_results),
                        search_type=self._get_search_type_from_analysis(analysis)
                    )

                    # تحويل نتائج البحث المحلي للتنسيق الموحد
                    for local_result in local_results:
                        all_results.append({
                            'title': local_result.title,
                            'url': local_result.url,
                            'content': local_result.content,
                            'source': local_result.source,
                            'published_date': local_result.published_date.isoformat() if local_result.published_date else None,
                            'search_engine': 'local',
                            'metadata': local_result.metadata
                        })
            
            # بحث دلالي إضافي إذا كان مطلوباً
            if (request.use_semantic_analysis and 
                self.system_config['enable_semantic_analysis'] and
                strategy['semantic_type'] != SemanticSearchType.HYBRID):
                
                semantic_query = await semantic_search_engine.semantic_search(
                    request.query, 
                    strategy['semantic_type']
                )
                
                # استخدام التوسيعات الدلالية للبحث الإضافي
                for expansion in semantic_query.semantic_expansions[:3]:
                    expansion_results = await engine_coordination_system.coordinate_search(
                        query=expansion,
                        required_capabilities=required_capabilities,
                        strategy=CoordinationStrategy.PARALLEL,
                        max_results=request.max_results // 3,
                        priority=request.priority.value
                    )
                    
                    for engine_result in expansion_results:
                        if engine_result.success:
                            all_results.extend(engine_result.results)
            
            # إزالة التكرار
            unique_results = self._remove_duplicates(all_results)
            
            logger.debug(f"🔍 تم العثور على {len(unique_results)} نتيجة فريدة")
            return unique_results
            
        except Exception as e:
            logger.error(f"❌ فشل في تنفيذ البحث الموحد: {e}")
            return []

    async def _evaluate_and_rank_results(self,
                                       request: UnifiedSearchRequest,
                                       results: List[Dict],
                                       analysis: Optional[QueryAnalysis]) -> List[Dict]:
        """تقييم وترتيب النتائج"""
        try:
            if not results:
                return []

            # تحضير الاستعلام الدلالي للتقييم
            semantic_query = None
            if request.use_semantic_analysis and analysis:
                semantic_query = await semantic_search_engine.semantic_search(request.query)

            # تقييم النتائج
            evaluations = await intelligent_result_evaluator.evaluate_results(
                results=results,
                query=request.query,
                semantic_query=semantic_query,
                evaluation_method=EvaluationMethod.HYBRID
            )

            # ترتيب النتائج حسب التقييم
            ranked_results = []
            for i, evaluation in enumerate(evaluations):
                if i < len(results):
                    result = results[i].copy()
                    result['evaluation'] = {
                        'overall_score': evaluation.overall_score,
                        'weighted_score': evaluation.weighted_score,
                        'quality_tier': evaluation.quality_tier,
                        'recommendation': evaluation.recommendation,
                        'individual_scores': [asdict(score) for score in evaluation.individual_scores]
                    }
                    ranked_results.append(result)

            # ترتيب حسب النقاط المرجحة
            ranked_results.sort(key=lambda x: x.get('evaluation', {}).get('weighted_score', 0), reverse=True)

            # الحد من العدد المطلوب
            final_results = ranked_results[:request.max_results]

            logger.debug(f"🎯 تم تقييم وترتيب {len(final_results)} نتيجة")
            return final_results

        except Exception as e:
            logger.error(f"❌ فشل في تقييم وترتيب النتائج: {e}")
            return results[:request.max_results]  # إرجاع النتائج الأصلية كخطة بديلة

    async def _learn_from_search(self,
                               request: UnifiedSearchRequest,
                               results: List[Dict],
                               analysis: Optional[QueryAnalysis]) -> Optional[Dict]:
        """التعلم من نتائج البحث"""
        try:
            if not self.system_config['enable_learning'] or not request.use_learning:
                return None

            # حساب مقاييس النجاح
            success_metrics = self._calculate_success_metrics(results)

            # تسجيل حدث التعلم
            context = {
                'mode': request.mode.value,
                'priority': request.priority.value,
                'analysis': asdict(analysis) if analysis else {},
                'result_count': len(results),
                'execution_time': time.time()
            }

            learning_event = await adaptive_learning_system.record_learning_event(
                query=request.query,
                strategy_used=request.mode.value,
                engine_used='unified_system',
                results=results,
                context=context
            )

            if learning_event:
                return {
                    'learning_value': learning_event.learning_value,
                    'success_metrics': learning_event.success_metrics,
                    'recommendations': self._generate_learning_recommendations(learning_event)
                }

        except Exception as e:
            logger.error(f"❌ فشل في التعلم من البحث: {e}")

        return None

    def _calculate_success_metrics(self, results: List[Dict]) -> Dict[str, float]:
        """حساب مقاييس النجاح"""
        if not results:
            return {'success_rate': 0.0, 'quality_score': 0.0, 'relevance_score': 0.0}

        # حساب متوسط النقاط
        quality_scores = []
        relevance_scores = []

        for result in results:
            evaluation = result.get('evaluation', {})
            if evaluation:
                quality_scores.append(evaluation.get('weighted_score', 0.5))

                # البحث عن نقاط الصلة في التقييمات الفردية
                individual_scores = evaluation.get('individual_scores', [])
                for score in individual_scores:
                    if score.get('criterion') == 'relevance':
                        relevance_scores.append(score.get('score', 0.5))
                        break
                else:
                    relevance_scores.append(0.5)  # قيمة افتراضية

        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
        avg_relevance = sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0.0

        # معدل النجاح (نسبة النتائج عالية الجودة)
        high_quality_count = sum(1 for score in quality_scores if score > 0.7)
        success_rate = high_quality_count / len(quality_scores) if quality_scores else 0.0

        return {
            'success_rate': success_rate,
            'quality_score': avg_quality,
            'relevance_score': avg_relevance
        }

    def _generate_learning_recommendations(self, learning_event) -> List[str]:
        """إنشاء توصيات التعلم"""
        recommendations = []

        if learning_event.learning_value > 0.8:
            recommendations.append("أداء ممتاز - استمر في استخدام هذه الاستراتيجية")
        elif learning_event.learning_value > 0.6:
            recommendations.append("أداء جيد - يمكن تحسينه قليلاً")
        else:
            recommendations.append("أداء ضعيف - جرب استراتيجية مختلفة")

        # توصيات محددة بناءً على المقاييس
        success_metrics = learning_event.success_metrics
        if success_metrics.get('relevance_score', 0) < 0.6:
            recommendations.append("تحسين الصلة - استخدم كلمات مفتاحية أكثر دقة")

        if success_metrics.get('quality_score', 0) < 0.6:
            recommendations.append("تحسين الجودة - ركز على مصادر موثوقة")

        return recommendations

    async def _create_unified_result(self,
                                   request: UnifiedSearchRequest,
                                   results: List[Dict],
                                   strategy: Dict[str, Any],
                                   learning_insights: Optional[Dict],
                                   start_time: float) -> UnifiedSearchResult:
        """إنشاء النتيجة الموحدة"""

        execution_time = time.time() - start_time

        # حساب النقاط الإجمالية
        quality_scores = []
        relevance_scores = []
        engines_used = set()

        for result in results:
            evaluation = result.get('evaluation', {})
            if evaluation:
                quality_scores.append(evaluation.get('weighted_score', 0.5))

                # البحث عن نقاط الصلة
                individual_scores = evaluation.get('individual_scores', [])
                for score in individual_scores:
                    if score.get('criterion') == 'relevance':
                        relevance_scores.append(score.get('score', 0.5))
                        break
                else:
                    relevance_scores.append(0.5)

            # جمع المحركات المستخدمة
            if 'search_engine' in result:
                engines_used.add(result['search_engine'])

        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
        avg_relevance = sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0.0

        # إنشاء التوصيات
        recommendations = []
        if avg_quality < 0.6:
            recommendations.append("جودة النتائج منخفضة - جرب استعلام أكثر تحديداً")
        if avg_relevance < 0.6:
            recommendations.append("صلة النتائج منخفضة - أعد صياغة الاستعلام")
        if len(results) < request.max_results // 2:
            recommendations.append("عدد النتائج قليل - جرب استعلام أوسع")

        # إضافة توصيات التعلم
        if learning_insights and 'recommendations' in learning_insights:
            recommendations.extend(learning_insights['recommendations'])

        return UnifiedSearchResult(
            query=request.query,
            results=results,
            total_results=len(results),
            execution_time=execution_time,
            search_strategy=strategy['search_strategy'].value,
            engines_used=list(engines_used),
            quality_score=avg_quality,
            relevance_score=avg_relevance,
            semantic_analysis=strategy.get('semantic_analysis'),
            learning_insights=learning_insights,
            recommendations=recommendations,
            metadata={
                'mode': request.mode.value,
                'priority': request.priority.value,
                'strategy_reasoning': strategy.get('reasoning', []),
                'timestamp': datetime.now().isoformat()
            }
        )

    def _remove_duplicates(self, results: List[Dict]) -> List[Dict]:
        """إزالة النتائج المكررة"""
        seen_urls = set()
        seen_titles = set()
        unique_results = []

        for result in results:
            url = result.get('url', '')
            title = result.get('title', '').lower()

            # فحص التكرار بناءً على URL
            if url and url in seen_urls:
                continue

            # فحص التكرار بناءً على العنوان (تشابه عالي)
            is_duplicate = False
            for seen_title in seen_titles:
                if title and seen_title:
                    # حساب التشابه البسيط
                    title_words = set(title.split())
                    seen_words = set(seen_title.split())
                    if title_words and seen_words:
                        similarity = len(title_words & seen_words) / len(title_words | seen_words)
                        if similarity > 0.8:  # تشابه عالي
                            is_duplicate = True
                            break

            if not is_duplicate:
                unique_results.append(result)
                if url:
                    seen_urls.add(url)
                if title:
                    seen_titles.add(title)

        return unique_results

    def _update_unified_stats(self, result: UnifiedSearchResult):
        """تحديث إحصائيات النظام الموحد"""
        try:
            # تحديث متوسط وقت التنفيذ
            total_searches = self.unified_stats['total_searches']
            current_avg_time = self.unified_stats['average_execution_time']

            self.unified_stats['average_execution_time'] = (
                (current_avg_time * (total_searches - 1) + result.execution_time) / total_searches
            )

            # تحديث متوسط نقاط الجودة
            current_avg_quality = self.unified_stats['average_quality_score']
            self.unified_stats['average_quality_score'] = (
                (current_avg_quality * (total_searches - 1) + result.quality_score) / total_searches
            )

            # تحديث إجمالي النتائج
            self.unified_stats['total_results_found'] += result.total_results

        except Exception as e:
            logger.error(f"❌ فشل في تحديث الإحصائيات: {e}")

    async def quick_search(self, query: str, max_results: int = 5) -> UnifiedSearchResult:
        """بحث سريع مبسط"""
        request = UnifiedSearchRequest(
            query=query,
            mode=SearchMode.QUICK,
            priority=SearchPriority.NORMAL,
            max_results=max_results,
            timeout=15.0,
            use_ai_enhancement=False,
            use_semantic_analysis=False
        )
        return await self.search(request)

    async def comprehensive_search(self, query: str, max_results: int = 20) -> UnifiedSearchResult:
        """بحث شامل متقدم"""
        request = UnifiedSearchRequest(
            query=query,
            mode=SearchMode.COMPREHENSIVE,
            priority=SearchPriority.HIGH,
            max_results=max_results,
            timeout=60.0,
            use_ai_enhancement=True,
            use_semantic_analysis=True
        )
        return await self.search(request)

    async def semantic_search(self, query: str, max_results: int = 10) -> UnifiedSearchResult:
        """بحث دلالي متخصص"""
        request = UnifiedSearchRequest(
            query=query,
            mode=SearchMode.SEMANTIC,
            priority=SearchPriority.NORMAL,
            max_results=max_results,
            use_semantic_analysis=True
        )
        return await self.search(request)

    async def adaptive_search(self, query: str, context: Optional[SearchContext] = None, max_results: int = 10) -> UnifiedSearchResult:
        """بحث تكيفي ذكي"""
        request = UnifiedSearchRequest(
            query=query,
            mode=SearchMode.ADAPTIVE,
            priority=SearchPriority.NORMAL,
            max_results=max_results,
            context=context,
            use_learning=True
        )
        return await self.search(request)

    async def batch_search(self, queries: List[str], mode: SearchMode = SearchMode.INTELLIGENT) -> List[UnifiedSearchResult]:
        """بحث مجمع لعدة استعلامات"""
        try:
            # تحديد عدد العمليات المتوازية
            max_concurrent = min(len(queries), self.system_config['max_concurrent_searches'])

            # تقسيم الاستعلامات إلى مجموعات
            batches = [queries[i:i + max_concurrent] for i in range(0, len(queries), max_concurrent)]

            all_results = []

            for batch in batches:
                # إنشاء طلبات البحث
                requests = [
                    UnifiedSearchRequest(
                        query=query,
                        mode=mode,
                        max_results=10
                    ) for query in batch
                ]

                # تنفيذ متوازي
                batch_results = await asyncio.gather(
                    *[self.search(request) for request in requests],
                    return_exceptions=True
                )

                # فلترة النتائج الناجحة
                for result in batch_results:
                    if isinstance(result, UnifiedSearchResult):
                        all_results.append(result)
                    else:
                        logger.error(f"❌ فشل في بحث مجمع: {result}")

            logger.info(f"📊 البحث المجمع مكتمل: {len(all_results)} من {len(queries)} استعلام")
            return all_results

        except Exception as e:
            logger.error(f"❌ فشل في البحث المجمع: {e}")
            return []

    def get_system_insights(self) -> Dict[str, Any]:
        """الحصول على رؤى النظام الموحد"""
        insights = {
            'unified_stats': self.unified_stats.copy(),
            'system_health': self._assess_system_health(),
            'performance_metrics': self._calculate_performance_metrics(),
            'recommendations': self._generate_system_recommendations()
        }

        # إضافة رؤى من الأنظمة الفرعية
        try:
            insights['learning_insights'] = adaptive_learning_system.get_learning_insights()
            insights['coordination_insights'] = engine_coordination_system.get_coordination_insights()
            insights['evaluation_insights'] = intelligent_result_evaluator.get_evaluation_insights()
        except Exception as e:
            logger.error(f"❌ فشل في جمع رؤى الأنظمة الفرعية: {e}")

        return insights

    def _assess_system_health(self) -> Dict[str, str]:
        """تقييم صحة النظام"""
        health = {}

        # صحة معدل النجاح
        success_rate = (
            self.unified_stats['successful_searches'] /
            max(self.unified_stats['total_searches'], 1)
        )

        if success_rate >= 0.9:
            health['success_rate'] = 'excellent'
        elif success_rate >= 0.7:
            health['success_rate'] = 'good'
        elif success_rate >= 0.5:
            health['success_rate'] = 'fair'
        else:
            health['success_rate'] = 'poor'

        # صحة الأداء
        avg_time = self.unified_stats['average_execution_time']
        if avg_time <= 5.0:
            health['performance'] = 'excellent'
        elif avg_time <= 10.0:
            health['performance'] = 'good'
        elif avg_time <= 20.0:
            health['performance'] = 'fair'
        else:
            health['performance'] = 'poor'

        # صحة الجودة
        avg_quality = self.unified_stats['average_quality_score']
        if avg_quality >= 0.8:
            health['quality'] = 'excellent'
        elif avg_quality >= 0.6:
            health['quality'] = 'good'
        elif avg_quality >= 0.4:
            health['quality'] = 'fair'
        else:
            health['quality'] = 'poor'

        return health

    def _calculate_performance_metrics(self) -> Dict[str, float]:
        """حساب مقاييس الأداء"""
        total_searches = max(self.unified_stats['total_searches'], 1)

        return {
            'success_rate': self.unified_stats['successful_searches'] / total_searches,
            'failure_rate': self.unified_stats['failed_searches'] / total_searches,
            'average_execution_time': self.unified_stats['average_execution_time'],
            'average_quality_score': self.unified_stats['average_quality_score'],
            'average_results_per_search': self.unified_stats['total_results_found'] / total_searches,
            'most_used_mode': max(self.unified_stats['mode_usage'], key=self.unified_stats['mode_usage'].get) if self.unified_stats['mode_usage'] else 'none'
        }

    def _generate_system_recommendations(self) -> List[str]:
        """إنشاء توصيات النظام"""
        recommendations = []
        health = self._assess_system_health()

        if health['success_rate'] in ['fair', 'poor']:
            recommendations.append("تحسين معدل النجاح - راجع استراتيجيات البحث")

        if health['performance'] in ['fair', 'poor']:
            recommendations.append("تحسين الأداء - قلل timeout أو حسن التنسيق")

        if health['quality'] in ['fair', 'poor']:
            recommendations.append("تحسين الجودة - راجع معايير التقييم")

        # توصيات بناءً على الاستخدام
        mode_usage = self.unified_stats['mode_usage']
        if mode_usage.get('quick', 0) > mode_usage.get('comprehensive', 0) * 3:
            recommendations.append("استخدم البحث الشامل أكثر للحصول على نتائج أفضل")

        if not recommendations:
            recommendations.append("النظام يعمل بكفاءة عالية")

        return recommendations

    async def optimize_system(self):
        """تحسين النظام تلقائياً"""
        try:
            logger.info("🔧 بدء تحسين النظام الموحد")

            # تحسين الأنظمة الفرعية
            if self.system_config['enable_learning']:
                await adaptive_learning_system.optimize_learning_parameters()

            if self.system_config['enable_coordination']:
                await engine_coordination_system.optimize_coordination()

            # تحسين الإعدادات الموحدة
            performance_metrics = self._calculate_performance_metrics()

            # تحسين timeout بناءً على الأداء
            if performance_metrics['average_execution_time'] > 20.0:
                self.system_config['default_timeout'] = max(15.0, self.system_config['default_timeout'] * 0.8)
            elif performance_metrics['average_execution_time'] < 5.0:
                self.system_config['default_timeout'] = min(60.0, self.system_config['default_timeout'] * 1.2)

            # تحسين عدد العمليات المتوازية
            if performance_metrics['success_rate'] > 0.9 and performance_metrics['average_execution_time'] < 10.0:
                self.system_config['max_concurrent_searches'] = min(5, self.system_config['max_concurrent_searches'] + 1)
            elif performance_metrics['success_rate'] < 0.7:
                self.system_config['max_concurrent_searches'] = max(1, self.system_config['max_concurrent_searches'] - 1)

            logger.info("✅ تم تحسين النظام الموحد")

        except Exception as e:
            logger.error(f"❌ فشل في تحسين النظام: {e}")

    def _get_search_type_from_analysis(self, analysis: Optional[QueryAnalysis]) -> str:
        """تحديد نوع البحث من التحليل"""
        if not analysis:
            return "general"

        intent_mapping = {
            'find_news': 'news',
            'find_reviews': 'reviews',
            'find_guides': 'guides',
            'find_updates': 'news',
            'find_releases': 'news',
            'find_comparisons': 'reviews',
            'find_tutorials': 'guides',
            'find_analysis': 'reviews'
        }

        return intent_mapping.get(analysis.intent.value, 'general')

    async def get_api_usage_report(self) -> Dict[str, Any]:
        """تقرير استخدام API"""
        try:
            usage_report = api_usage_manager.get_usage_report()
            local_stats = local_search_engine.get_stats()

            combined_report = {
                'api_usage': usage_report,
                'local_search': local_stats,
                'cost_savings': {
                    'estimated_api_calls_saved': local_stats['local_search_stats']['successful_searches'],
                    'estimated_cost_saved': local_stats['local_search_stats']['successful_searches'] * 0.01,
                    'cache_efficiency': usage_report['totals']['cache_hit_rate']
                },
                'recommendations': []
            }

            # إضافة توصيات توفير
            if usage_report['totals']['total_cost'] > 2.0:
                combined_report['recommendations'].append("التكلفة عالية - استخدم البحث المحلي أكثر")

            if usage_report['totals']['cache_hit_rate'] < 30:
                combined_report['recommendations'].append("معدل استخدام التخزين المؤقت منخفض - زد مدة الحفظ")

            if local_stats['success_rate'] > 0.8:
                combined_report['recommendations'].append("البحث المحلي فعال - يمكن الاعتماد عليه أكثر")

            return combined_report

        except Exception as e:
            logger.error(f"❌ فشل في إنشاء تقرير استخدام API: {e}")
            return {}

    async def optimize_api_usage(self):
        """تحسين استخدام API"""
        try:
            logger.info("💰 بدء تحسين استخدام API")

            # تحسين مدير API
            await api_usage_manager.optimize_usage()

            # تنظيف التخزين المؤقت للبحث المحلي
            await local_search_engine.cleanup_cache()

            # تحديث إعدادات النظام بناءً على الاستخدام
            usage_report = api_usage_manager.get_usage_report()

            # إذا كانت التكلفة عالية، قلل استخدام APIs المدفوعة
            if usage_report['totals']['total_cost'] > 3.0:
                self.system_config['enable_ai_enhancement'] = False
                logger.info("🔧 تم تعطيل تحسين الذكاء الاصطناعي لتوفير التكلفة")

            # إذا كان معدل النجاح للبحث المحلي عالي، اعتمد عليه أكثر
            local_stats = local_search_engine.get_stats()
            if local_stats['success_rate'] > 0.7:
                # زيادة الاعتماد على البحث المحلي
                self.system_config['local_search_priority'] = True
                logger.info("🏠 تم زيادة أولوية البحث المحلي")

            logger.info("✅ تم تحسين استخدام API")

        except Exception as e:
            logger.error(f"❌ فشل في تحسين استخدام API: {e}")

# إنشاء مثيل عام
unified_intelligent_search = UnifiedIntelligentSearch()
