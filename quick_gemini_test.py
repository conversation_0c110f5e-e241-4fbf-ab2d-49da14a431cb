#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لـ Gemini 2.5 Pro API
مثال بسيط لاختبار قدرات البحث على الويب
"""

import asyncio
import os
import sys

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.gemini_search_tester import GeminiSearchTester

async def quick_test():
    """اختبار سريع ومبسط"""
    
    print("🧪 اختبار سريع لـ Gemini 2.5 Pro API")
    print("=" * 50)
    
    # الحصول على API Key
    api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    
    if not api_key:
        print("❌ لم يتم العثور على API Key")
        print("💡 قم بتعيين GEMINI_API_KEY في متغيرات البيئة")
        print("   أو احصل على مفتاح من: https://aistudio.google.com/app/apikey")
        return
    
    print(f"🔑 API Key موجود: {api_key[:10]}...")
    
    # إنشاء أداة الاختبار
    tester = GeminiSearchTester(api_key)
    
    try:
        # 1. اختبار صحة API Key
        print("\n🔍 اختبار صحة API Key...")
        api_result = await tester.test_api_key_validity()
        
        if api_result.get('valid'):
            print("✅ API Key صحيح وفعال")
        else:
            print(f"❌ API Key غير صحيح: {api_result.get('error')}")
            return
        
        # 2. اختبار بحث بسيط
        print("\n🌐 اختبار البحث على الويب...")
        
        # استعلام اختبار بسيط
        test_query = "What are the latest gaming news today? Search for recent PlayStation and Xbox updates."
        
        # إنشاء اختبار مخصص
        custom_test = {
            'name': 'Quick Web Search Test',
            'query': test_query,
            'expected_keywords': ['gaming', 'news', 'playstation', 'xbox', 'today']
        }
        
        # حفظ الاختبارات الأصلية وتعيين الاختبار المخصص
        original_tests = tester.web_search_tests
        tester.web_search_tests = [custom_test]
        
        # تنفيذ الاختبار
        results = await tester.test_web_search_capability('gemini-2.5-pro')
        
        if results:
            result = results[0]
            
            print(f"⏱️ وقت التنفيذ: {result.execution_time:.2f} ثانية")
            
            if result.success:
                print("✅ الاختبار نجح")
                
                if result.has_web_search:
                    print("🎉 تم اكتشاف قدرة البحث على الويب!")
                    print("💡 Gemini 2.5 Pro يمكنه البحث في الويب للحصول على معلومات حديثة")
                else:
                    print("❓ لم يتم اكتشاف بحث ويب واضح")
                    print("💡 قد يكون Gemini محدود للمعلومات المدربة مسبقاً")
                
                print(f"\n💬 معاينة الاستجابة:")
                print("-" * 40)
                preview = result.response_text[:400] + "..." if len(result.response_text) > 400 else result.response_text
                print(preview)
                
            else:
                print(f"❌ الاختبار فشل: {result.error_message}")
        
        # استعادة الاختبارات الأصلية
        tester.web_search_tests = original_tests
        
        # 3. النتيجة النهائية
        print(f"\n{'='*50}")
        print("📊 ملخص النتائج:")
        
        stats = tester.get_test_statistics()
        if stats['web_search_available']:
            print("🟢 النتيجة: Gemini 2.5 Pro يدعم البحث على الويب")
            print("✅ يمكن استخدامه للبحث العميق والمعلومات الحديثة")
            print("💡 مناسب لتطبيقات البحث المتقدمة")
        else:
            print("🟡 النتيجة: قدرة البحث على الويب غير مؤكدة")
            print("⚠️ قد يكون محدود للمعلومات المدربة مسبقاً")
            print("💡 مناسب للاستعلامات العامة فقط")
    
    except Exception as e:
        print(f"❌ حدث خطأ أثناء الاختبار: {e}")

async def interactive_test():
    """اختبار تفاعلي بسيط"""
    
    print("🎯 اختبار تفاعلي لـ Gemini 2.5 Pro")
    print("=" * 50)
    
    # طلب API Key
    api_key = input("أدخل Gemini API Key (أو اتركه فارغاً لاستخدام متغير البيئة): ").strip()
    
    if not api_key:
        api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    
    if not api_key:
        print("❌ لا يوجد API Key")
        return
    
    # طلب استعلام مخصص
    query = input("أدخل استعلام للاختبار (أو اتركه فارغاً للاختبار الافتراضي): ").strip()
    
    if not query:
        query = "Search for the latest technology news today"
    
    print(f"\n🔍 اختبار الاستعلام: {query}")
    print("-" * 50)
    
    # إنشاء أداة الاختبار
    tester = GeminiSearchTester(api_key)
    
    try:
        # اختبار مخصص
        custom_test = {
            'name': 'Interactive Test',
            'query': query,
            'expected_keywords': query.lower().split()[:5]  # أول 5 كلمات
        }
        
        tester.web_search_tests = [custom_test]
        results = await tester.test_web_search_capability('gemini-2.5-pro')
        
        if results:
            result = results[0]
            
            print(f"⏱️ وقت التنفيذ: {result.execution_time:.2f}ث")
            print(f"✅ النجاح: {'نعم' if result.success else 'لا'}")
            print(f"🌐 البحث على الويب: {'مكتشف' if result.has_web_search else 'غير مكتشف'}")
            
            if result.success:
                print(f"\n💬 الاستجابة الكاملة:")
                print("=" * 50)
                print(result.response_text)
            else:
                print(f"\n❌ خطأ: {result.error_message}")
    
    except Exception as e:
        print(f"❌ حدث خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    
    if len(sys.argv) > 1 and sys.argv[1] == '--interactive':
        # النمط التفاعلي
        asyncio.run(interactive_test())
    else:
        # الاختبار السريع
        asyncio.run(quick_test())

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
