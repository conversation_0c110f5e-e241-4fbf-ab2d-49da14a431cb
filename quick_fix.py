#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح سريع لجميع المشاكل
"""

import os
import sys
import subprocess
from datetime import datetime

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*60)
    print(f"🔧 {title}")
    print("="*60)

def run_fix_script(script_name):
    """تشغيل سكريبت إصلاح"""
    if os.path.exists(script_name):
        print(f"▶️ تشغيل {script_name}...")
        try:
            result = subprocess.run([sys.executable, script_name], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print(f"✅ نجح {script_name}")
                return True
            else:
                print(f"❌ فشل {script_name}")
                if result.stderr:
                    print(f"الخطأ: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تشغيل {script_name}: {e}")
            return False
    else:
        print(f"⚠️ الملف غير موجود: {script_name}")
        return False

def main():
    """الدالة الرئيسية للإصلاح السريع"""
    print("🚀 بدء الإصلاح السريع لجميع المشاكل")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # قائمة الإصلاحات بالترتيب
    fixes = [
        "fix_all_database_issues.py",
        "fix_missing_libraries.py", 
        "fix_monitoring_system.py"
    ]
    
    success_count = 0
    
    for script in fixes:
        print_header(f"تشغيل {script}")
        if run_fix_script(script):
            success_count += 1
    
    # اختبار النظام
    print_header("اختبار النظام")
    test_success = run_fix_script("test_system_comprehensive.py")
    
    # النتائج النهائية
    print_header("النتائج النهائية")
    print(f"📊 نجح {success_count}/{len(fixes)} إصلاحات")
    
    if success_count == len(fixes):
        print("🎉 تم إكمال جميع الإصلاحات بنجاح!")
        
        if test_success:
            print("✅ النظام جاهز للتشغيل")
            print("🔄 يمكنك الآن تشغيل: python main.py")
        else:
            print("⚠️ تم الإصلاح ولكن هناك تحذيرات في الاختبار")
            print("🔄 يمكنك المحاولة: python main.py")
    else:
        print("⚠️ تم إكمال معظم الإصلاحات")
        print("🔍 راجع الأخطاء أعلاه")
    
    print(f"\n⏰ انتهى في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
