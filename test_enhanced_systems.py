#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للأنظمة المحسنة
"""

import asyncio
import json
import time
import os
from datetime import datetime
from typing import Dict, List, Any

# استيراد الأنظمة المحسنة
try:
    from modules.advanced_rag_system import advanced_rag_system, RAGDocument, RAGQuery, RAGMode, ContentType
    from modules.multimodal_analyzer import multimodal_analyzer, MediaAnalysisRequest, MediaType, AnalysisType
    from modules.memory_system import memory_system, Memory, MemoryType, MemoryImportance
    from modules.enhanced_agent_integration import enhanced_agent
    from modules.logger import logger
    
    SYSTEMS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ خطأ في استيراد الأنظمة: {e}")
    SYSTEMS_AVAILABLE = False

class EnhancedSystemsTester:
    """فئة اختبار الأنظمة المحسنة"""
    
    def __init__(self):
        self.test_results = {
            'rag_system': {'passed': 0, 'failed': 0, 'details': []},
            'multimodal_system': {'passed': 0, 'failed': 0, 'details': []},
            'memory_system': {'passed': 0, 'failed': 0, 'details': []},
            'integration_system': {'passed': 0, 'failed': 0, 'details': []},
            'overall': {'start_time': None, 'end_time': None, 'total_time': 0}
        }
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        if not SYSTEMS_AVAILABLE:
            print("❌ الأنظمة غير متوفرة للاختبار")
            return
        
        print("🚀 بدء اختبار الأنظمة المحسنة...")
        self.test_results['overall']['start_time'] = datetime.now()
        
        try:
            # اختبار نظام RAG
            await self.test_rag_system()
            
            # اختبار نظام التحليل متعدد الوسائط
            await self.test_multimodal_system()
            
            # اختبار نظام الذاكرة المتقدم
            await self.test_memory_system()
            
            # اختبار نظام التكامل
            await self.test_integration_system()
            
            # إنشاء التقرير النهائي
            await self.generate_final_report()
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل الاختبارات: {e}")
        
        self.test_results['overall']['end_time'] = datetime.now()
        self.test_results['overall']['total_time'] = (
            self.test_results['overall']['end_time'] - 
            self.test_results['overall']['start_time']
        ).total_seconds()
    
    async def test_rag_system(self):
        """اختبار نظام RAG المتقدم"""
        print("\n🔍 اختبار نظام RAG المتقدم...")
        
        try:
            # اختبار 1: إضافة وثيقة
            test_doc = RAGDocument(
                id="test_doc_1",
                content="هذا اختبار لنظام RAG المتقدم للألعاب. Minecraft هي لعبة شائعة جداً.",
                content_type=ContentType.TEXT,
                metadata={"tags": ["minecraft", "gaming", "test"]}
            )
            
            success = await advanced_rag_system.add_document(test_doc)
            self._record_test_result('rag_system', 'إضافة وثيقة', success, 
                                   "تم إضافة الوثيقة بنجاح" if success else "فشل في إضافة الوثيقة")
            
            # اختبار 2: البحث النصي
            query = RAGQuery(
                query="ألعاب Minecraft",
                mode=RAGMode.TEXT_ONLY,
                content_types=[ContentType.TEXT],
                max_results=5
            )
            
            results = await advanced_rag_system.search(query)
            success = len(results) > 0
            self._record_test_result('rag_system', 'البحث النصي', success,
                                   f"تم العثور على {len(results)} نتيجة" if success else "لم يتم العثور على نتائج")
            
            # اختبار 3: البحث المختلط
            query.mode = RAGMode.HYBRID
            hybrid_results = await advanced_rag_system.search(query)
            success = len(hybrid_results) >= 0  # حتى لو لم توجد نتائج، النظام يعمل
            self._record_test_result('rag_system', 'البحث المختلط', success,
                                   f"البحث المختلط يعمل - {len(hybrid_results)} نتيجة")
            
            # اختبار 4: الإحصائيات
            stats = await advanced_rag_system.get_stats()
            success = isinstance(stats, dict) and 'total_documents' in stats
            self._record_test_result('rag_system', 'الإحصائيات', success,
                                   f"الإحصائيات متوفرة: {stats.get('total_documents', 0)} وثيقة")
            
        except Exception as e:
            self._record_test_result('rag_system', 'خطأ عام', False, str(e))
    
    async def test_multimodal_system(self):
        """اختبار نظام التحليل متعدد الوسائط"""
        print("\n🖼️ اختبار نظام التحليل متعدد الوسائط...")
        
        try:
            # اختبار 1: التحقق من توفر النماذج
            stats = await multimodal_analyzer.get_stats()
            success = stats.get('enabled', False)
            self._record_test_result('multimodal_system', 'توفر النماذج', success,
                                   "النماذج متوفرة ومحملة" if success else "النماذج غير متوفرة")
            
            # اختبار 2: تحليل نص وهمي (محاكاة صورة)
            if multimodal_analyzer.enabled:
                # إنشاء ملف نصي وهمي لمحاكاة الصورة
                test_image_path = "test_image.txt"
                with open(test_image_path, 'w', encoding='utf-8') as f:
                    f.write("محتوى اختبار للصورة")
                
                request = MediaAnalysisRequest(
                    media_path=test_image_path,
                    media_type=MediaType.IMAGE,
                    analysis_types=[AnalysisType.OCR],
                    extract_gaming_content=True
                )
                
                try:
                    results = await multimodal_analyzer.analyze_media(request)
                    success = isinstance(results, list)
                    self._record_test_result('multimodal_system', 'تحليل الوسائط', success,
                                           f"تم تحليل الوسائط - {len(results)} نتيجة")
                except Exception as e:
                    self._record_test_result('multimodal_system', 'تحليل الوسائط', False, str(e))
                finally:
                    # حذف الملف الاختباري
                    if os.path.exists(test_image_path):
                        os.remove(test_image_path)
            
            # اختبار 3: حساب نقاط المحتوى
            gaming_score = await multimodal_analyzer.get_gaming_content_score([])
            success = isinstance(gaming_score, (int, float))
            self._record_test_result('multimodal_system', 'نقاط المحتوى', success,
                                   f"نقاط المحتوى: {gaming_score}")
            
        except Exception as e:
            self._record_test_result('multimodal_system', 'خطأ عام', False, str(e))
    
    async def test_memory_system(self):
        """اختبار نظام الذاكرة المتقدم"""
        print("\n🧠 اختبار نظام الذاكرة المتقدم...")
        
        try:
            # اختبار 1: إنشاء ذاكرة
            test_memory = Memory(
                id="test_memory_1",
                content="ذاكرة اختبار للألعاب - Fortnite لعبة شائعة",
                memory_type=MemoryType.ARTICLE,
                importance=MemoryImportance.MEDIUM,
                metadata={"test": True, "game": "fortnite"}
            )
            
            success = await memory_system.store_memory(test_memory)
            self._record_test_result('memory_system', 'إنشاء ذاكرة', success,
                                   "تم إنشاء الذاكرة بنجاح" if success else "فشل في إنشاء الذاكرة")
            
            # اختبار 2: البحث في الذاكرة
            memories = await memory_system.smart_memory_retrieval("Fortnite", max_results=5)
            success = isinstance(memories, list)
            self._record_test_result('memory_system', 'البحث في الذاكرة', success,
                                   f"تم العثور على {len(memories)} ذاكرة")
            
            # اختبار 3: التجميع الدلالي
            await memory_system.perform_semantic_clustering()
            success = True  # إذا لم يحدث خطأ، فالاختبار نجح
            self._record_test_result('memory_system', 'التجميع الدلالي', success,
                                   "تم تنفيذ التجميع الدلالي")
            
            # اختبار 4: بناء العلاقات
            await memory_system.build_semantic_relations()
            success = True
            self._record_test_result('memory_system', 'بناء العلاقات', success,
                                   "تم بناء العلاقات الدلالية")
            
            # اختبار 5: الحصول على الرؤى
            insights = await memory_system.get_memory_insights()
            success = isinstance(insights, dict) and len(insights) > 0
            self._record_test_result('memory_system', 'رؤى الذاكرة', success,
                                   f"تم الحصول على {len(insights)} رؤية")
            
        except Exception as e:
            self._record_test_result('memory_system', 'خطأ عام', False, str(e))
    
    async def test_integration_system(self):
        """اختبار نظام التكامل"""
        print("\n🔗 اختبار نظام التكامل...")
        
        try:
            # اختبار 1: التحليل المحسن
            test_content = "اختبار التحليل المحسن للألعاب - Call of Duty لعبة حرب"
            result = await enhanced_agent.enhance_content_analysis(test_content)
            
            success = result.confidence > 0
            self._record_test_result('integration_system', 'التحليل المحسن', success,
                                   f"ثقة التحليل: {result.confidence:.2f}")
            
            # اختبار 2: تحسين الاستعلام
            enhanced_query = await enhanced_agent.enhance_search_query("ألعاب جديدة")
            success = len(enhanced_query) >= len("ألعاب جديدة")
            self._record_test_result('integration_system', 'تحسين الاستعلام', success,
                                   f"الاستعلام المحسن: {enhanced_query}")
            
            # اختبار 3: توصيات المحتوى
            recommendations = await enhanced_agent.get_content_recommendations(test_content)
            success = isinstance(recommendations, list)
            self._record_test_result('integration_system', 'توصيات المحتوى', success,
                                   f"تم توليد {len(recommendations)} توصية")
            
            # اختبار 4: الإحصائيات الشاملة
            stats = await enhanced_agent.get_enhancement_stats()
            success = isinstance(stats, dict) and 'integration_stats' in stats
            self._record_test_result('integration_system', 'الإحصائيات الشاملة', success,
                                   "تم الحصول على الإحصائيات الشاملة")
            
        except Exception as e:
            self._record_test_result('integration_system', 'خطأ عام', False, str(e))
    
    def _record_test_result(self, system: str, test_name: str, success: bool, details: str):
        """تسجيل نتيجة اختبار"""
        if success:
            self.test_results[system]['passed'] += 1
            status = "✅"
        else:
            self.test_results[system]['failed'] += 1
            status = "❌"
        
        self.test_results[system]['details'].append({
            'test': test_name,
            'status': status,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
        
        print(f"  {status} {test_name}: {details}")
    
    async def generate_final_report(self):
        """إنشاء التقرير النهائي"""
        print("\n" + "="*60)
        print("📊 تقرير اختبار الأنظمة المحسنة")
        print("="*60)
        
        total_passed = 0
        total_failed = 0
        
        for system_name, results in self.test_results.items():
            if system_name == 'overall':
                continue
            
            passed = results['passed']
            failed = results['failed']
            total = passed + failed
            
            total_passed += passed
            total_failed += failed
            
            if total > 0:
                success_rate = (passed / total) * 100
                print(f"\n🔧 {system_name}:")
                print(f"   ✅ نجح: {passed}")
                print(f"   ❌ فشل: {failed}")
                print(f"   📈 معدل النجاح: {success_rate:.1f}%")
        
        # الإحصائيات الإجمالية
        total_tests = total_passed + total_failed
        overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n🎯 النتائج الإجمالية:")
        print(f"   📊 إجمالي الاختبارات: {total_tests}")
        print(f"   ✅ نجح: {total_passed}")
        print(f"   ❌ فشل: {total_failed}")
        print(f"   📈 معدل النجاح الإجمالي: {overall_success_rate:.1f}%")
        print(f"   ⏱️ وقت التنفيذ: {self.test_results['overall']['total_time']:.2f} ثانية")
        
        # حفظ التقرير
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 تم حفظ التقرير المفصل في: {report_file}")
        
        # تقييم الأداء
        if overall_success_rate >= 80:
            print("\n🎉 ممتاز! الأنظمة تعمل بشكل ممتاز")
        elif overall_success_rate >= 60:
            print("\n👍 جيد! الأنظمة تعمل بشكل جيد مع بعض التحسينات المطلوبة")
        else:
            print("\n⚠️ يحتاج تحسين! هناك مشاكل تحتاج إلى إصلاح")

async def main():
    """الدالة الرئيسية"""
    tester = EnhancedSystemsTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
