#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام Gemini 2.5 Pro المحسن
"""

import asyncio
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_gemini_system():
    """اختبار النظام الجديد"""
    print("🚀 بدء اختبار نظام Gemini 2.5 Pro المحسن...")
    
    try:
        # استيراد النظام الجديد
        from modules.gemini_enhanced_system import (
            gemini_enhanced_system, 
            GeminiAnalysisRequest, 
            AnalysisMode, 
            ContentType
        )
        
        print("✅ تم استيراد النظام بنجاح")
        
        # فحص حالة النظام
        if not gemini_enhanced_system.enabled:
            print("❌ النظام غير مفعل - تحقق من مفاتيح API")
            return False
        
        print("✅ النظام مفعل ومتاح")
        
        # اختبار تحليل بسيط
        print("\n🔍 اختبار التحليل الأساسي...")
        
        test_content = """
        تحديث جديد للعبة Minecraft يضيف ميزات مثيرة للاعبين. 
        التحديث يتضمن كائنات جديدة وتحسينات في الأداء.
        Epic Games تعلن عن تحديث Fortnite الجديد أيضاً.
        """
        
        request = GeminiAnalysisRequest(
            content=test_content,
            mode=AnalysisMode.CONTENT_ANALYSIS,
            content_type=ContentType.GAMING_NEWS,
            max_output_length=1000
        )
        
        result = await gemini_enhanced_system.analyze_content(request)
        
        if result.confidence_score > 0:
            print(f"✅ التحليل نجح بثقة: {result.confidence_score:.2f}")
            print(f"📝 التحليل: {result.analysis[:200]}...")
            print(f"🔑 الرؤى: {result.key_insights[:3]}")
            print(f"🏷️ المواضيع ذات الصلة: {result.related_topics[:3]}")
        else:
            print("❌ فشل في التحليل")
            return False
        
        # اختبار استخراج الكيانات
        print("\n🎮 اختبار استخراج كيانات الألعاب...")
        
        entities = await gemini_enhanced_system.extract_gaming_entities(test_content)
        print(f"✅ الكيانات المستخرجة: {entities}")
        
        # اختبار البحث عن محتوى مشابه
        print("\n🔍 اختبار البحث عن محتوى مشابه...")
        
        similar_content = await gemini_enhanced_system.search_similar_content(
            "Minecraft updates", 
            max_results=3
        )
        print(f"✅ تم العثور على {len(similar_content)} محتوى مشابه")
        
        # عرض الإحصائيات
        print("\n📊 إحصائيات النظام:")
        stats = await gemini_enhanced_system.get_stats()
        
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"   {key}: {value:.2f}")
            else:
                print(f"   {key}: {value}")
        
        print("\n✅ جميع الاختبارات نجحت!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

async def test_agent_integration():
    """اختبار نظام التكامل"""
    print("\n🔗 اختبار نظام التكامل المحسن...")
    
    try:
        from modules.gemini_agent_integration import gemini_agent_integration
        
        print("✅ تم استيراد نظام التكامل بنجاح")
        
        # اختبار تحليل محتوى محسن
        test_content = "تحديث جديد في لعبة Call of Duty يضيف خرائط جديدة ومعدات متطورة"
        
        result = await gemini_agent_integration.enhance_content_analysis(
            content=test_content,
            content_type="gaming_news"
        )
        
        if result.confidence > 0:
            print(f"✅ التحليل المحسن نجح بثقة: {result.confidence:.2f}")
            print(f"📝 المحتوى المحسن: {result.content[:200]}...")
            print(f"🤖 نتائج Gemini متوفرة: {'نعم' if result.gemini_results else 'لا'}")
        else:
            print("❌ فشل في التحليل المحسن")
            return False
        
        # عرض إحصائيات التحسين
        print("\n📈 إحصائيات التحسين:")
        enhancement_stats = await gemini_agent_integration.get_enhancement_stats()
        
        for key, value in enhancement_stats.items():
            if key != 'gemini_stats' and isinstance(value, (int, float)):
                if isinstance(value, float):
                    print(f"   {key}: {value:.2f}")
                else:
                    print(f"   {key}: {value}")
        
        print("\n✅ اختبار التكامل نجح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لنظام Gemini 2.5 Pro المحسن")
    print("=" * 60)
    
    # اختبار النظام الأساسي
    basic_test = await test_gemini_system()
    
    # اختبار نظام التكامل
    integration_test = await test_agent_integration()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if basic_test and integration_test:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("✨ تم استبدال RAG بـ Gemini 2.5 Pro بنجاح")
    else:
        print("❌ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء أعلاه")
    print("=" * 60)

if __name__ == "__main__":
    # تشغيل الاختبارات
    asyncio.run(main())
