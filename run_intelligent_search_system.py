#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام البحث الذكي المتقدم - ملف التشغيل الرئيسي
تطوير: نظام البحث الذكي المتقدم
التاريخ: 2025-07-22
"""

import asyncio
import sys
import os
import argparse
import json
from datetime import datetime
from typing import Dict, List, Any

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النظام الذكي
from modules.unified_intelligent_search import (
    unified_intelligent_search,
    UnifiedSearchRequest,
    SearchMode,
    SearchPriority
)
from modules.intelligent_search_config import config_manager, PerformanceMode
from modules.intelligent_search_monitor import intelligent_search_monitor, start_monitoring, stop_monitoring
from modules.api_conservation_config import api_conservation_manager
from modules.logger import logger
from integrate_intelligent_search import intelligent_search_integrator
from test_intelligent_search_system import run_intelligent_search_test

class IntelligentSearchSystem:
    """النظام الرئيسي للبحث الذكي"""
    
    def __init__(self):
        self.system_info = {
            'name': 'نظام البحث الذكي المتقدم',
            'version': '1.0.0',
            'build_date': '2025-07-22',
            'author': 'فريق تطوير البحث الذكي',
            'status': 'مطور وجاهز للاستخدام'
        }
        
        self.running = False
        
    async def initialize_system(self, config_mode: str = "balanced"):
        """تهيئة النظام"""
        try:
            logger.info("🚀 بدء تهيئة نظام البحث الذكي المتقدم")
            
            # تحديد نمط الأداء
            performance_modes = {
                'speed': PerformanceMode.SPEED_OPTIMIZED,
                'quality': PerformanceMode.QUALITY_OPTIMIZED,
                'balanced': PerformanceMode.BALANCED,
                'saving': PerformanceMode.RESOURCE_SAVING
            }
            
            mode = performance_modes.get(config_mode, PerformanceMode.BALANCED)
            config_manager.set_performance_mode(mode)
            
            logger.info(f"⚙️ تم تعيين نمط الأداء: {mode.value}")
            
            # بدء المراقبة
            start_monitoring()
            logger.info("🔍 تم بدء نظام المراقبة")
            
            # فحص صحة النظام
            health_report = intelligent_search_monitor._perform_health_check()
            logger.info(f"🏥 حالة النظام: {health_report.overall_status.value}")
            
            self.running = True
            logger.info("✅ تم تهيئة النظام بنجاح")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة النظام: {e}")
            return False
    
    async def run_interactive_mode(self):
        """تشغيل النمط التفاعلي"""
        print("\n" + "="*60)
        print("🧠 مرحباً بك في نظام البحث الذكي المتقدم")
        print("="*60)
        print("الأوامر المتاحة:")
        print("  search <استعلام>     - بحث ذكي")
        print("  quick <استعلام>      - بحث سريع")
        print("  comprehensive <استعلام> - بحث شامل")
        print("  semantic <استعلام>   - بحث دلالي")
        print("  status               - حالة النظام")
        print("  config               - إعدادات النظام")
        print("  usage                - تقرير استخدام API")
        print("  savings              - تقرير التوفير")
        print("  test                 - تشغيل الاختبارات")
        print("  help                 - المساعدة")
        print("  exit                 - خروج")
        print("-"*60)
        
        while self.running:
            try:
                user_input = input("\n🔍 أدخل أمر أو استعلام: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() == 'exit':
                    break
                elif user_input.lower() == 'help':
                    await self._show_help()
                elif user_input.lower() == 'status':
                    await self._show_status()
                elif user_input.lower() == 'config':
                    await self._show_config()
                elif user_input.lower() == 'usage':
                    await self._show_api_usage()
                elif user_input.lower() == 'savings':
                    await self._show_savings_report()
                elif user_input.lower() == 'test':
                    await self._run_tests()
                elif user_input.startswith('search '):
                    query = user_input[7:]
                    await self._perform_search(query, SearchMode.INTELLIGENT)
                elif user_input.startswith('quick '):
                    query = user_input[6:]
                    await self._perform_search(query, SearchMode.QUICK)
                elif user_input.startswith('comprehensive '):
                    query = user_input[14:]
                    await self._perform_search(query, SearchMode.COMPREHENSIVE)
                elif user_input.startswith('semantic '):
                    query = user_input[9:]
                    await self._perform_search(query, SearchMode.SEMANTIC)
                else:
                    # بحث افتراضي
                    await self._perform_search(user_input, SearchMode.INTELLIGENT)
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف النظام بواسطة المستخدم")
                break
            except Exception as e:
                logger.error(f"❌ خطأ في النمط التفاعلي: {e}")
                print(f"❌ حدث خطأ: {e}")
    
    async def _perform_search(self, query: str, mode: SearchMode):
        """تنفيذ البحث"""
        try:
            print(f"\n🔍 البحث عن: '{query}' | النمط: {mode.value}")
            print("-" * 50)
            
            start_time = asyncio.get_event_loop().time()
            
            # إنشاء طلب البحث
            request = UnifiedSearchRequest(
                query=query,
                mode=mode,
                max_results=10
            )
            
            # تنفيذ البحث
            result = await unified_intelligent_search.search(request)
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # عرض النتائج
            print(f"✅ تم العثور على {result.total_results} نتيجة في {execution_time:.2f} ثانية")
            print(f"📊 نقاط الجودة: {result.quality_score:.2f} | الصلة: {result.relevance_score:.2f}")
            print(f"🔧 الاستراتيجية: {result.search_strategy} | المحركات: {', '.join(result.engines_used)}")
            
            if result.results:
                print("\n📋 أفضل النتائج:")
                for i, item in enumerate(result.results[:5], 1):
                    title = item.get('title', 'بدون عنوان')[:80]
                    source = item.get('source', 'مصدر غير معروف')
                    evaluation = item.get('evaluation', {})
                    score = evaluation.get('weighted_score', 0)
                    
                    print(f"  {i}. {title}")
                    print(f"     المصدر: {source} | النقاط: {score:.2f}")
                    print(f"     الرابط: {item.get('url', 'غير متوفر')}")
                    print()
            
            if result.recommendations:
                print("💡 التوصيات:")
                for rec in result.recommendations:
                    print(f"  • {rec}")
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث: {e}")
            print(f"❌ فشل في البحث: {e}")
    
    async def _show_help(self):
        """عرض المساعدة"""
        help_text = """
🆘 مساعدة نظام البحث الذكي المتقدم

📖 الأوامر الأساسية:
  search <استعلام>     - بحث ذكي متوازن (افتراضي)
  quick <استعلام>      - بحث سريع للنتائج الفورية
  comprehensive <استعلام> - بحث شامل ومفصل
  semantic <استعلام>   - بحث دلالي يفهم المعنى

🔧 أوامر النظام:
  status               - عرض حالة النظام والأداء
  config               - عرض الإعدادات الحالية
  usage                - تقرير استخدام وتكلفة API
  savings              - تقرير التوفير والحماية
  test                 - تشغيل الاختبارات الشاملة

💡 نصائح للبحث:
  • استخدم كلمات مفتاحية واضحة
  • أضف السياق الزمني (مثل: "اليوم", "2025")
  • حدد نوع المحتوى (مثل: "أخبار", "مراجعة", "دليل")
  • استخدم أسماء الألعاب والشركات المحددة

🎯 أمثلة:
  search PlayStation 5 news today
  quick gaming news
  comprehensive Cyberpunk 2077 review
  semantic best RPG games 2025
        """
        print(help_text)
    
    async def _show_status(self):
        """عرض حالة النظام"""
        try:
            print("\n📊 حالة نظام البحث الذكي")
            print("="*40)
            
            # حالة النظام العامة
            insights = unified_intelligent_search.get_system_insights()
            
            if insights:
                stats = insights.get('unified_stats', {})
                performance = insights.get('performance_metrics', {})
                health = insights.get('system_health', {})
                
                print(f"🔍 إجمالي البحثات: {stats.get('total_searches', 0)}")
                print(f"✅ البحثات الناجحة: {stats.get('successful_searches', 0)}")
                print(f"📈 معدل النجاح: {performance.get('success_rate', 0):.2%}")
                print(f"⚡ متوسط وقت التنفيذ: {performance.get('average_execution_time', 0):.2f}ث")
                print(f"🎯 متوسط نقاط الجودة: {performance.get('average_quality_score', 0):.2f}")
                
                print(f"\n🏥 صحة النظام:")
                print(f"  معدل النجاح: {health.get('success_rate', 'غير معروف')}")
                print(f"  الأداء: {health.get('performance', 'غير معروف')}")
                print(f"  الجودة: {health.get('quality', 'غير معروف')}")
            
            # حالة المراقبة
            monitor_stats = intelligent_search_monitor.monitoring_stats
            print(f"\n🔍 إحصائيات المراقبة:")
            print(f"  دورات المراقبة: {monitor_stats.get('total_monitoring_cycles', 0)}")
            print(f"  التنبيهات المولدة: {monitor_stats.get('alerts_generated', 0)}")
            print(f"  فحوصات الصحة: {monitor_stats.get('health_checks_performed', 0)}")
            
        except Exception as e:
            logger.error(f"❌ فشل في عرض الحالة: {e}")
            print(f"❌ فشل في عرض الحالة: {e}")
    
    async def _show_config(self):
        """عرض الإعدادات"""
        try:
            print("\n⚙️ إعدادات النظام الحالية")
            print("="*40)
            
            config = config_manager.get_config()
            
            print(f"🎛️ نمط الأداء: {config.performance_mode.value}")
            print(f"🔍 الحد الأقصى للنتائج: {config.default_max_results}")
            print(f"⏱️ مهلة البحث: {config.default_timeout}ث")
            print(f"🧠 التعلم التكيفي: {'مفعل' if config.enable_learning else 'معطل'}")
            print(f"🌐 البحث الدلالي: {'مفعل' if config.enable_semantic_search else 'معطل'}")
            print(f"🤖 تحسين الذكاء الاصطناعي: {'مفعل' if config.enable_ai_enhancement else 'معطل'}")
            print(f"🔗 تنسيق المحركات: {'مفعل' if config.enable_engine_coordination else 'معطل'}")
            
            print(f"\n📊 أوزان التقييم:")
            for criterion, weight in config.evaluation_weights.items():
                print(f"  {criterion}: {weight:.2f}")
            
        except Exception as e:
            logger.error(f"❌ فشل في عرض الإعدادات: {e}")
            print(f"❌ فشل في عرض الإعدادات: {e}")

    async def _show_api_usage(self):
        """عرض تقرير استخدام API"""
        try:
            print("\n💰 تقرير استخدام API")
            print("="*40)

            usage_report = await unified_intelligent_search.get_api_usage_report()

            if usage_report:
                api_usage = usage_report.get('api_usage', {})
                totals = api_usage.get('totals', {})

                print(f"💸 التكلفة اليومية: ${totals.get('total_cost', 0):.3f}")
                print(f"📊 إجمالي الطلبات: {totals.get('total_requests', 0)}")
                print(f"💾 معدل استخدام التخزين المؤقت: {totals.get('cache_hit_rate', 0):.1f}%")

                print(f"\n🔍 إحصائيات البحث المحلي:")
                local_stats = usage_report.get('local_search', {}).get('local_search_stats', {})
                print(f"  البحثات المحلية: {local_stats.get('successful_searches', 0)}")
                print(f"  معدل النجاح: {usage_report.get('local_search', {}).get('success_rate', 0):.2%}")

                print(f"\n💡 التوفير المقدر:")
                savings = usage_report.get('cost_savings', {})
                print(f"  طلبات API موفرة: {savings.get('estimated_api_calls_saved', 0)}")
                print(f"  التكلفة الموفرة: ${savings.get('estimated_cost_saved', 0):.3f}")

                if usage_report.get('recommendations'):
                    print(f"\n📋 التوصيات:")
                    for rec in usage_report['recommendations']:
                        print(f"  • {rec}")

        except Exception as e:
            logger.error(f"❌ فشل في عرض تقرير API: {e}")
            print(f"❌ فشل في عرض تقرير API: {e}")

    async def _show_savings_report(self):
        """عرض تقرير التوفير"""
        try:
            print("\n🏦 تقرير التوفير والحماية")
            print("="*40)

            conservation_report = api_conservation_manager.get_conservation_report()

            if conservation_report and 'error' not in conservation_report:
                print(f"🛡️ مستوى الحماية: {conservation_report.get('conservation_level', 'غير معروف')}")
                print(f"💰 الميزانية اليومية: ${conservation_report.get('daily_budget', 0):.2f}")

                savings = conservation_report.get('total_savings', {})
                print(f"\n💸 إجمالي التوفير:")
                print(f"  طلبات API موفرة: {savings.get('api_calls_saved', 0)}")
                print(f"  التكلفة الموفرة: ${savings.get('cost_saved', 0):.3f}")
                print(f"  التوفير اليومي: ${savings.get('daily_cost_saved', 0):.3f}")

                patterns = conservation_report.get('usage_patterns', {})
                print(f"\n📊 أنماط الاستخدام:")
                print(f"  البحثات المحلية: {patterns.get('local_searches_used', 0)}")
                print(f"  استخدامات التخزين المؤقت: {patterns.get('cache_hits_gained', 0)}")

                metrics = conservation_report.get('efficiency_metrics', {})
                print(f"\n📈 مقاييس الكفاءة:")
                print(f"  معدل التوفير: {metrics.get('conservation_rate', 0):.2%}")
                print(f"  كفاءة التكلفة: {metrics.get('cost_efficiency', 0):.2%}")

                if conservation_report.get('recommendations'):
                    print(f"\n💡 توصيات التوفير:")
                    for rec in conservation_report['recommendations']:
                        print(f"  • {rec}")
            else:
                print("❌ تعذر الحصول على تقرير التوفير")

        except Exception as e:
            logger.error(f"❌ فشل في عرض تقرير التوفير: {e}")
            print(f"❌ فشل في عرض تقرير التوفير: {e}")

    async def _run_tests(self):
        """تشغيل الاختبارات"""
        try:
            print("\n🧪 تشغيل الاختبارات الشاملة...")
            print("="*40)
            
            results = await run_intelligent_search_test()
            
            print(f"\n📋 نتائج الاختبار:")
            print(f"  إجمالي الاختبارات: {results['total_tests']}")
            print(f"  الناجحة: {results['passed_tests']}")
            print(f"  الفاشلة: {results['failed_tests']}")
            print(f"  معدل النجاح: {results['summary']['success_rate']:.2%}")
            print(f"  التقييم: {results['summary']['grade']}")
            
            if results['summary']['recommendations']:
                print(f"\n💡 التوصيات:")
                for rec in results['summary']['recommendations']:
                    print(f"  • {rec}")
            
        except Exception as e:
            logger.error(f"❌ فشل في تشغيل الاختبارات: {e}")
            print(f"❌ فشل في تشغيل الاختبارات: {e}")
    
    async def shutdown_system(self):
        """إيقاف النظام"""
        try:
            logger.info("🔄 بدء إيقاف النظام...")
            
            # إيقاف المراقبة
            stop_monitoring()
            
            # تحسين النظام قبل الإغلاق
            await unified_intelligent_search.optimize_system()
            
            self.running = False
            logger.info("✅ تم إيقاف النظام بنجاح")
            
        except Exception as e:
            logger.error(f"❌ فشل في إيقاف النظام: {e}")

async def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description='نظام البحث الذكي المتقدم')
    parser.add_argument('--mode', choices=['speed', 'quality', 'balanced', 'saving'], 
                       default='balanced', help='نمط الأداء')
    parser.add_argument('--query', type=str, help='استعلام للبحث المباشر')
    parser.add_argument('--test', action='store_true', help='تشغيل الاختبارات فقط')
    parser.add_argument('--config', action='store_true', help='عرض الإعدادات فقط')
    
    args = parser.parse_args()
    
    # إنشاء النظام
    system = IntelligentSearchSystem()
    
    try:
        # تهيئة النظام
        if not await system.initialize_system(args.mode):
            print("❌ فشل في تهيئة النظام")
            return 1
        
        # تنفيذ الأوامر
        if args.test:
            await system._run_tests()
        elif args.config:
            await system._show_config()
        elif args.query:
            await system._perform_search(args.query, SearchMode.INTELLIGENT)
        else:
            # النمط التفاعلي
            await system.run_interactive_mode()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بواسطة المستخدم")
        return 0
    except Exception as e:
        logger.error(f"❌ خطأ في النظام الرئيسي: {e}")
        print(f"❌ خطأ في النظام: {e}")
        return 1
    finally:
        await system.shutdown_system()

if __name__ == "__main__":
    # تشغيل النظام
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
