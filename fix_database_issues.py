#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل قاعدة البيانات - إنشاء الجداول المفقودة
"""

import sqlite3
import os
import sys
from datetime import datetime

def fix_database():
    """إصلاح قاعدة البيانات وإنشاء الجداول المفقودة"""
    
    db_path = "data/articles.db"
    
    # إنشاء مجلد البيانات إذا لم يكن موجوداً
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    print("🔧 بدء إصلاح قاعدة البيانات...")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # فحص الجداول الموجودة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            existing_tables = [row[0] for row in cursor.fetchall()]
            print(f"📋 الجداول الموجودة: {existing_tables}")
            
            # إنشاء جدول المقالات المنشورة
            print("📝 إنشاء جدول published_articles...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS published_articles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT,
                    content_hash TEXT UNIQUE NOT NULL,
                    semantic_hash TEXT NOT NULL,
                    source_url TEXT,
                    source_type TEXT,
                    blogger_url TEXT,
                    telegram_message_id INTEGER,
                    keywords TEXT,
                    category TEXT,
                    dialect TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    published_at TIMESTAMP,
                    view_count INTEGER DEFAULT 0,
                    engagement_score REAL DEFAULT 0.0
                )
            ''')
            
            # إضافة عمود content إذا لم يكن موجوداً
            try:
                cursor.execute('ALTER TABLE published_articles ADD COLUMN content TEXT')
                print("✅ تم إضافة عمود content")
            except sqlite3.OperationalError:
                print("ℹ️ عمود content موجود بالفعل")
            
            # إنشاء جدول المصادر المراقبة
            print("📝 إنشاء جدول monitored_sources...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monitored_sources (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    source_type TEXT,
                    last_checked TIMESTAMP,
                    articles_found INTEGER DEFAULT 0,
                    success_rate REAL DEFAULT 0.0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول سجل الأخطاء
            print("📝 إنشاء جدول error_log...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS error_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    error_type TEXT NOT NULL,
                    error_message TEXT,
                    stack_trace TEXT,
                    source_url TEXT,
                    retry_count INTEGER DEFAULT 0,
                    resolved BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول إحصائيات الأداء
            print("📝 إنشاء جدول performance_stats...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE UNIQUE NOT NULL,
                    articles_processed INTEGER DEFAULT 0,
                    articles_published INTEGER DEFAULT 0,
                    api_calls_gemini INTEGER DEFAULT 0,
                    api_calls_telegram INTEGER DEFAULT 0,
                    api_calls_blogger INTEGER DEFAULT 0,
                    errors_count INTEGER DEFAULT 0,
                    uptime_hours REAL DEFAULT 0.0
                )
            ''')
            
            # إنشاء جدول التحليلات اليومية
            print("📝 إنشاء جدول daily_analytics...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS daily_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE UNIQUE NOT NULL,
                    articles_published INTEGER DEFAULT 0,
                    avg_quality_score REAL DEFAULT 0.0,
                    avg_seo_score REAL DEFAULT 0.0,
                    total_words INTEGER DEFAULT 0,
                    avg_words_per_article REAL DEFAULT 0.0,
                    top_keywords TEXT,
                    top_categories TEXT,
                    success_rate REAL DEFAULT 0.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول تحليل المحتوى
            print("📝 إنشاء جدول content_analytics...")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS content_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    article_id INTEGER,
                    content_type TEXT,
                    quality_score INTEGER DEFAULT 0,
                    seo_score INTEGER DEFAULT 0,
                    word_count INTEGER DEFAULT 0,
                    keyword_count INTEGER DEFAULT 0,
                    readability_score REAL DEFAULT 0.0,
                    engagement_prediction REAL DEFAULT 0.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (article_id) REFERENCES published_articles (id)
                )
            ''')
            
            # إنشاء الفهارس المهمة
            print("📝 إنشاء الفهارس...")
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_published_articles_hash ON published_articles(content_hash)",
                "CREATE INDEX IF NOT EXISTS idx_published_articles_semantic ON published_articles(semantic_hash)",
                "CREATE INDEX IF NOT EXISTS idx_published_articles_date ON published_articles(published_at)",
                "CREATE INDEX IF NOT EXISTS idx_published_articles_category ON published_articles(category)",
                "CREATE INDEX IF NOT EXISTS idx_error_log_date ON error_log(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_performance_stats_date ON performance_stats(date)"
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            conn.commit()
            
            # فحص الجداول بعد الإنشاء
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            final_tables = [row[0] for row in cursor.fetchall()]
            print(f"✅ الجداول النهائية: {final_tables}")
            
            # إضافة بيانات تجريبية إذا كانت قاعدة البيانات فارغة
            cursor.execute("SELECT COUNT(*) FROM published_articles")
            article_count = cursor.fetchone()[0]
            
            if article_count == 0:
                print("📝 إضافة مقال تجريبي...")
                cursor.execute('''
                    INSERT INTO published_articles 
                    (title, content_hash, semantic_hash, source_url, source_type, category, dialect, published_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    "مقال تجريبي - اختبار النظام",
                    "test_hash_123",
                    "test_semantic_123", 
                    "https://example.com",
                    "test",
                    "اختبار",
                    "egyptian",
                    datetime.now()
                ))
                conn.commit()
                print("✅ تم إضافة مقال تجريبي")
            
            print("🎉 تم إصلاح قاعدة البيانات بنجاح!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        return False

def verify_database():
    """التحقق من سلامة قاعدة البيانات"""
    db_path = "data/articles.db"
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # فحص الجداول المطلوبة
            required_tables = [
                'published_articles',
                'monitored_sources', 
                'error_log',
                'performance_stats'
            ]
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            missing_tables = [table for table in required_tables if table not in existing_tables]
            
            if missing_tables:
                print(f"⚠️ جداول مفقودة: {missing_tables}")
                return False
            else:
                print("✅ جميع الجداول المطلوبة موجودة")
                
            # فحص بنية جدول published_articles
            cursor.execute("PRAGMA table_info(published_articles)")
            columns = [row[1] for row in cursor.fetchall()]
            
            required_columns = ['id', 'title', 'content_hash', 'semantic_hash']
            missing_columns = [col for col in required_columns if col not in columns]
            
            if missing_columns:
                print(f"⚠️ أعمدة مفقودة في published_articles: {missing_columns}")
                return False
            else:
                print("✅ بنية جدول published_articles صحيحة")
                
            return True
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    print("🔧 أداة إصلاح قاعدة البيانات")
    print("=" * 50)
    
    # التحقق من الحالة الحالية
    print("1️⃣ فحص الحالة الحالية...")
    if verify_database():
        print("✅ قاعدة البيانات سليمة")
    else:
        print("⚠️ قاعدة البيانات تحتاج إصلاح")
        
        # إصلاح قاعدة البيانات
        print("\n2️⃣ إصلاح قاعدة البيانات...")
        if fix_database():
            print("\n3️⃣ التحقق النهائي...")
            if verify_database():
                print("🎉 تم إصلاح قاعدة البيانات بنجاح!")
            else:
                print("❌ فشل في إصلاح قاعدة البيانات")
                sys.exit(1)
        else:
            print("❌ فشل في إصلاح قاعدة البيانات")
            sys.exit(1)
    
    print("\n✅ انتهى إصلاح قاعدة البيانات")
