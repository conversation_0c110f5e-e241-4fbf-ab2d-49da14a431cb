#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لإصلاحات Whisper و ApiKeyManager
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.api_key_manager import ApiKeyManager
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from config.settings import google_api_manager

def test_api_key_manager():
    """اختبار ApiKeyManager مع الطرق الجديدة"""
    print("🔑 اختبار ApiKeyManager...")
    
    try:
        # إنشاء مدير اختبار
        test_keys = ["test_key_1", "test_key_2", "test_key_3"]
        manager = ApiKeyManager(test_keys, "Test Service")
        
        print(f"✅ تم إنشاء مدير المفاتيح مع {len(test_keys)} مفتاح")
        
        # اختبار get_key
        key1 = manager.get_key()
        print(f"✅ get_key(): {key1}")
        
        # اختبار get_api_key (الطريقة الجديدة للتوافق)
        key2 = manager.get_api_key()
        print(f"✅ get_api_key(): {key2}")
        
        # اختبار mark_key_failed
        manager.mark_key_failed(key1)
        print(f"✅ تم وضع المفتاح في القائمة السوداء")
        
        # اختبار get_available_keys_count
        available_count = manager.get_available_keys_count()
        print(f"✅ عدد المفاتيح المتاحة: {available_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ApiKeyManager: {e}")
        return False

def test_global_api_manager():
    """اختبار مدير المفاتيح العام"""
    print("\n🌐 اختبار مدير المفاتيح العام...")
    
    try:
        if google_api_manager:
            print("✅ مدير المفاتيح العام متاح")
            
            # اختبار get_key
            try:
                key = google_api_manager.get_key()
                print(f"✅ get_key() نجح: {key[:10]}...")
            except Exception as e:
                print(f"⚠️ get_key() فشل: {e}")
            
            # اختبار get_api_key
            try:
                key = google_api_manager.get_api_key()
                print(f"✅ get_api_key() نجح: {key[:10]}...")
            except Exception as e:
                print(f"⚠️ get_api_key() فشل: {e}")
            
            return True
        else:
            print("⚠️ مدير المفاتيح العام غير متاح")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير المفاتيح العام: {e}")
        return False

async def test_youtube_analyzer():
    """اختبار محلل YouTube مع الإصلاحات الجديدة"""
    print("\n🎥 اختبار محلل YouTube...")
    
    try:
        analyzer = AdvancedYouTubeAnalyzer()
        print("✅ تم إنشاء محلل YouTube")
        
        # اختبار الحصول على تفاصيل فيديو
        test_video_id = "Dvtswxb51K4"  # من الرابط المقترح
        print(f"🔍 اختبار الحصول على تفاصيل الفيديو: {test_video_id}")
        
        video_details = await analyzer._get_video_details(test_video_id)
        
        if video_details:
            print("✅ تم الحصول على تفاصيل الفيديو بنجاح")
            print(f"   📹 العنوان: {video_details.get('title', 'غير محدد')}")
            print(f"   📺 القناة: {video_details.get('channel_title', 'غير محدد')}")
            print(f"   ⏱️ المدة: {video_details.get('duration', 'غير محدد')}")
            return True
        else:
            print("⚠️ لم يتم الحصول على تفاصيل الفيديو")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار محلل YouTube: {e}")
        return False

async def test_whisper_basic():
    """اختبار أساسي لـ Whisper"""
    print("\n🎤 اختبار أساسي لـ Whisper...")
    
    try:
        analyzer = AdvancedYouTubeAnalyzer()
        test_video_id = "Dvtswxb51K4"
        
        print(f"🎵 اختبار تحميل الصوت للفيديو: {test_video_id}")
        
        # اختبار تحميل الصوت
        video_url = f"https://www.youtube.com/watch?v={test_video_id}"
        
        import aiohttp
        async with aiohttp.ClientSession() as session:
            audio_data = await analyzer._download_audio_from_video(video_url, session)
        
        if audio_data:
            print(f"✅ تم تحميل الصوت بنجاح - {len(audio_data):,} بايت")
            
            # اختبار استخراج النص (محاولة سريعة)
            print("🎤 محاولة استخراج النص...")
            transcript = await analyzer.extract_video_transcript_with_whisper(test_video_id)
            
            if transcript and len(transcript.strip()) > 0:
                print(f"✅ تم استخراج النص بنجاح - {len(transcript)} حرف")
                print(f"📄 عينة: {transcript[:100]}...")
                return True
            else:
                print("⚠️ لم يتم استخراج النص أو النص فارغ")
                return False
        else:
            print("❌ فشل في تحميل الصوت")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Whisper: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار إصلاحات Whisper و ApiKeyManager")
    print("=" * 50)
    
    results = []
    
    # اختبار 1: ApiKeyManager
    result1 = test_api_key_manager()
    results.append(("ApiKeyManager", result1))
    
    # اختبار 2: مدير المفاتيح العام
    result2 = test_global_api_manager()
    results.append(("Global API Manager", result2))
    
    # اختبار 3: محلل YouTube
    result3 = await test_youtube_analyzer()
    results.append(("YouTube Analyzer", result3))
    
    # اختبار 4: Whisper أساسي
    result4 = await test_whisper_basic()
    results.append(("Whisper Basic", result4))
    
    # ملخص النتائج
    print("\n📊 ملخص نتائج الاختبار:")
    print("=" * 30)
    
    successful_tests = 0
    for test_name, success in results:
        status = "✅ نجح" if success else "❌ فشل"
        print(f"{test_name}: {status}")
        if success:
            successful_tests += 1
    
    success_rate = (successful_tests / len(results)) * 100
    print(f"\n📈 معدل النجاح: {successful_tests}/{len(results)} ({success_rate:.1f}%)")
    
    if success_rate >= 75:
        print("🎉 الإصلاحات تعمل بشكل جيد!")
    elif success_rate >= 50:
        print("👍 الإصلاحات تعمل جزئياً، قد تحتاج مراجعة")
    else:
        print("⚠️ الإصلاحات تحتاج مراجعة إضافية")
    
    return success_rate >= 50

if __name__ == "__main__":
    asyncio.run(main())
