#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل سريع لاختبار الوكيل الرئيسي
يختبر جميع قدرات الوكيل بشكل مكثف
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_header():
    """طباعة رأس الاختبار"""
    print("🚀" + "=" * 68 + "🚀")
    print("🎮          اختبار شامل للوكيل الرئيسي - Gaming News Bot          🎮")
    print("🚀" + "=" * 68 + "🚀")
    print()
    print("📋 سيتم اختبار:")
    print("   🔧 تهيئة الوكيل والمكونات")
    print("   📰 جمع المحتوى من مصادر متعددة")
    print("   🎥 تحليل فيديوهات YouTube")
    print("   📝 توليد المقالات بالذكاء الاصطناعي")
    print("   🖼️ إنشاء الصور التلقائي")
    print("   🔍 تحسين SEO والكلمات المفتاحية")
    print("   📤 النشر على المدونات")
    print("   🧠 البحث العميق باستخدام Gemini")
    print("   🛠️ معالجة الأخطاء التلقائية")
    print("   ⚡ تقييم الأداء والسرعة")
    print()
    print("⏱️ الوقت المتوقع: 5-10 دقائق")
    print("=" * 70)

def print_quick_start_guide():
    """دليل البدء السريع"""
    print("\n📚 دليل البدء السريع:")
    print("=" * 40)
    print("1️⃣ تأكد من وجود مفاتيح Gemini في ملف .env")
    print("2️⃣ شغل الاختبار: python run_agent_test.py")
    print("3️⃣ راقب النتائج في الوقت الفعلي")
    print("4️⃣ راجع التقرير النهائي")
    print()
    print("🔑 للحصول على مفاتيح Gemini مجانية:")
    print("   https://makersuite.google.com/app/apikey")
    print()

async def run_quick_test():
    """تشغيل اختبار سريع"""
    print("⚡ تشغيل الاختبار السريع...")
    
    try:
        # استيراد الاختبار الشامل
        from test_main_agent_comprehensive import MainAgentTester
        
        # إنشاء مثيل الاختبار
        tester = MainAgentTester()
        
        # تشغيل الاختبار
        await tester.run_comprehensive_test()
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود ملف test_main_agent_comprehensive.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    requirements_ok = True
    
    # فحص الملفات المطلوبة
    required_files = [
        'main.py',
        'test_main_agent_comprehensive.py',
        'modules/logger.py',
        'config/api_config.py'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - مفقود")
            requirements_ok = False
    
    # فحص المجلدات
    required_dirs = ['modules', 'config', 'data']
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"  ✅ مجلد {dir_path}")
        else:
            print(f"  ⚠️ مجلد {dir_path} - سيتم إنشاؤه")
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"  ✅ تم إنشاء مجلد {dir_path}")
            except Exception as e:
                print(f"  ❌ فشل إنشاء مجلد {dir_path}: {e}")
                requirements_ok = False
    
    # فحص ملف .env
    if os.path.exists('.env'):
        print("  ✅ ملف .env موجود")
    else:
        print("  ⚠️ ملف .env غير موجود - سيتم إنشاؤه")
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write("# مفاتيح Gemini API\n")
                f.write("GEMINI_API_KEY_1=your_gemini_key_here\n")
                f.write("GEMINI_API_KEY_2=\n")
                f.write("GEMINI_API_KEY_3=\n")
            print("  ✅ تم إنشاء ملف .env")
        except Exception as e:
            print(f"  ❌ فشل إنشاء ملف .env: {e}")
    
    return requirements_ok

def print_system_info():
    """طباعة معلومات النظام"""
    print("\n💻 معلومات النظام:")
    print(f"  🐍 Python: {sys.version.split()[0]}")
    print(f"  💾 النظام: {os.name}")
    print(f"  📁 المجلد الحالي: {os.getcwd()}")
    print(f"  🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

async def main():
    """الدالة الرئيسية"""
    # طباعة الرأس
    print_header()
    
    # طباعة معلومات النظام
    print_system_info()
    
    # فحص المتطلبات
    print("\n" + "=" * 70)
    if not check_requirements():
        print("\n❌ بعض المتطلبات مفقودة. يرجى إصلاحها قبل المتابعة.")
        return
    
    print("\n✅ جميع المتطلبات متوفرة!")
    
    # طباعة دليل البدء السريع
    print_quick_start_guide()
    
    # السؤال عن المتابعة
    try:
        response = input("🚀 هل تريد بدء الاختبار الشامل؟ (y/n): ").lower().strip()
        
        if response in ['y', 'yes', 'نعم', '1']:
            print("\n🎯 بدء الاختبار الشامل...")
            print("=" * 70)
            
            start_time = time.time()
            success = await run_quick_test()
            end_time = time.time()
            
            print("\n" + "=" * 70)
            if success:
                print("🎉 تم الانتهاء من الاختبار بنجاح!")
            else:
                print("⚠️ الاختبار واجه بعض المشاكل")
            
            print(f"⏱️ الوقت الإجمالي: {end_time - start_time:.2f} ثانية")
            print("=" * 70)
            
        else:
            print("\n👋 تم إلغاء الاختبار. يمكنك تشغيله لاحقاً بنفس الأمر.")
            
    except KeyboardInterrupt:
        print("\n\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")

def run_simple_test():
    """تشغيل اختبار بسيط بدون async"""
    print("🔧 تشغيل اختبار بسيط...")
    
    try:
        # اختبار الاستيراد الأساسي
        print("  📦 اختبار الاستيراد...")
        
        try:
            from modules.logger import logger
            print("  ✅ logger")
        except Exception as e:
            print(f"  ❌ logger: {e}")
        
        try:
            from config.api_config import APIConfig
            print("  ✅ APIConfig")
        except Exception as e:
            print(f"  ❌ APIConfig: {e}")
        
        try:
            from main import GamingNewsBot
            print("  ✅ GamingNewsBot")
        except Exception as e:
            print(f"  ❌ GamingNewsBot: {e}")
        
        print("\n✅ الاختبار البسيط مكتمل!")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار البسيط: {e}")
        return False

if __name__ == "__main__":
    try:
        # محاولة تشغيل الاختبار الشامل
        asyncio.run(main())
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبار الشامل: {e}")
        print("\n🔧 محاولة تشغيل اختبار بسيط...")
        
        if run_simple_test():
            print("\n💡 الاختبار البسيط نجح. المشكلة قد تكون في الاختبار الشامل.")
        else:
            print("\n❌ حتى الاختبار البسيط فشل. يرجى فحص الإعدادات.")
        
        print("\n📞 للمساعدة:")
        print("  1. تأكد من تثبيت جميع المكتبات المطلوبة")
        print("  2. تأكد من وجود مفاتيح Gemini في ملف .env")
        print("  3. تأكد من صحة مسارات الملفات")
        print("  4. جرب تشغيل main.py مباشرة للتأكد من عمله")
