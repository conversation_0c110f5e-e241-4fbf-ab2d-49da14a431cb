@echo off
chcp 65001 >nul
title نظام Whisper المحسن - Enhanced Whisper System

echo.
echo ========================================
echo 🎤 نظام Whisper المحسن
echo Enhanced Whisper System
echo ========================================
echo.

echo اختر طريقة التشغيل:
echo Choose startup method:
echo.
echo 1. تشغيل واجهة الويب فقط (آمن)
echo    Start Web Interface Only (Safe)
echo.
echo 2. تشغيل النظام الكامل
echo    Start Complete System
echo.
echo 3. اختبار سريع
echo    Quick Test
echo.

set /p choice="اختر رقم (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🌐 تشغيل واجهة الويب الآمنة...
    echo Starting safe web interface...
    python start_web_interface_safe.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 تشغيل النظام الكامل...
    echo Starting complete system...
    python run_complete_whisper_system.py
) else if "%choice%"=="3" (
    echo.
    echo 🧪 تشغيل اختبار سريع...
    echo Running quick test...
    python quick_test_enhanced_whisper.py
) else (
    echo.
    echo ❌ اختيار غير صحيح، تشغيل واجهة الويب الآمنة...
    echo Invalid choice, starting safe web interface...
    python start_web_interface_safe.py
)

echo.
echo 👋 تم إنهاء النظام
echo System terminated
echo.
pause
