#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مشاكل المكتبات المفقودة
"""

import subprocess
import sys
import os

def install_package(package_name, pip_name=None):
    """تثبيت مكتبة Python"""
    if pip_name is None:
        pip_name = package_name
    
    try:
        print(f"📦 تثبيت {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
        print(f"✅ تم تثبيت {package_name} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت {package_name}: {e}")
        return False

def check_and_install_ai_libraries():
    """فحص وتثبيت مكتبات الذكاء الاصطناعي"""
    print("🧠 فحص مكتبات الذكاء الاصطناعي...")
    
    ai_libraries = [
        ("transformers", "transformers"),
        ("torch", "torch"),
        ("sentence-transformers", "sentence-transformers"),
        ("faiss", "faiss-cpu"),
        ("numpy", "numpy"),
        ("scikit-learn", "scikit-learn")
    ]
    
    installed_count = 0
    
    for lib_name, pip_name in ai_libraries:
        try:
            __import__(lib_name)
            print(f"✅ {lib_name} موجود")
            installed_count += 1
        except ImportError:
            print(f"⚠️ {lib_name} مفقود - محاولة التثبيت...")
            if install_package(lib_name, pip_name):
                installed_count += 1
    
    print(f"📊 تم تثبيت {installed_count}/{len(ai_libraries)} مكتبة ذكاء اصطناعي")
    return installed_count == len(ai_libraries)

def check_and_install_cv_libraries():
    """فحص وتثبيت مكتبات الرؤية الحاسوبية"""
    print("👁️ فحص مكتبات الرؤية الحاسوبية...")
    
    cv_libraries = [
        ("cv2", "opencv-python"),
        ("PIL", "Pillow"),
        ("matplotlib", "matplotlib"),
        ("imageio", "imageio")
    ]
    
    installed_count = 0
    
    for lib_name, pip_name in cv_libraries:
        try:
            __import__(lib_name)
            print(f"✅ {lib_name} موجود")
            installed_count += 1
        except ImportError:
            print(f"⚠️ {lib_name} مفقود - محاولة التثبيت...")
            if install_package(lib_name, pip_name):
                installed_count += 1
    
    print(f"📊 تم تثبيت {installed_count}/{len(cv_libraries)} مكتبة رؤية حاسوبية")
    return installed_count == len(cv_libraries)

def check_and_install_audio_libraries():
    """فحص وتثبيت مكتبات الصوت"""
    print("🎵 فحص مكتبات الصوت...")
    
    audio_libraries = [
        ("whisper", "openai-whisper"),
        ("librosa", "librosa"),
        ("soundfile", "soundfile"),
        ("pydub", "pydub")
    ]
    
    installed_count = 0
    
    for lib_name, pip_name in audio_libraries:
        try:
            __import__(lib_name)
            print(f"✅ {lib_name} موجود")
            installed_count += 1
        except ImportError:
            print(f"⚠️ {lib_name} مفقود - محاولة التثبيت...")
            if install_package(lib_name, pip_name):
                installed_count += 1
    
    print(f"📊 تم تثبيت {installed_count}/{len(audio_libraries)} مكتبة صوت")
    return installed_count == len(audio_libraries)

def check_and_install_web_libraries():
    """فحص وتثبيت مكتبات الويب"""
    print("🌐 فحص مكتبات الويب...")
    
    web_libraries = [
        ("requests", "requests"),
        ("beautifulsoup4", "beautifulsoup4"),
        ("selenium", "selenium"),
        ("aiohttp", "aiohttp"),
        ("feedparser", "feedparser"),
        ("lxml", "lxml")
    ]
    
    installed_count = 0
    
    for lib_name, pip_name in web_libraries:
        try:
            if lib_name == "beautifulsoup4":
                import bs4
            else:
                __import__(lib_name)
            print(f"✅ {lib_name} موجود")
            installed_count += 1
        except ImportError:
            print(f"⚠️ {lib_name} مفقود - محاولة التثبيت...")
            if install_package(lib_name, pip_name):
                installed_count += 1
    
    print(f"📊 تم تثبيت {installed_count}/{len(web_libraries)} مكتبة ويب")
    return installed_count == len(web_libraries)

def check_and_install_database_libraries():
    """فحص وتثبيت مكتبات قاعدة البيانات"""
    print("💾 فحص مكتبات قاعدة البيانات...")
    
    db_libraries = [
        ("sqlite3", None),  # مدمج في Python
        ("sqlalchemy", "sqlalchemy"),
        ("pandas", "pandas")
    ]
    
    installed_count = 0
    
    for lib_name, pip_name in db_libraries:
        try:
            __import__(lib_name)
            print(f"✅ {lib_name} موجود")
            installed_count += 1
        except ImportError:
            if pip_name:
                print(f"⚠️ {lib_name} مفقود - محاولة التثبيت...")
                if install_package(lib_name, pip_name):
                    installed_count += 1
            else:
                print(f"❌ {lib_name} مفقود ولا يمكن تثبيته")
    
    print(f"📊 تم تثبيت {installed_count}/{len(db_libraries)} مكتبة قاعدة بيانات")
    return installed_count == len(db_libraries)

def create_fallback_modules():
    """إنشاء وحدات احتياطية للمكتبات المفقودة"""
    print("🔧 إنشاء وحدات احتياطية...")
    
    # إنشاء مجلد fallback_modules إذا لم يكن موجوداً
    fallback_dir = "fallback_modules"
    os.makedirs(fallback_dir, exist_ok=True)
    
    # إنشاء ملف __init__.py
    with open(os.path.join(fallback_dir, "__init__.py"), "w", encoding="utf-8") as f:
        f.write("# وحدات احتياطية للمكتبات المفقودة\n")
    
    # إنشاء وحدة احتياطية للذكاء الاصطناعي
    ai_fallback = '''
"""
وحدة احتياطية للذكاء الاصطناعي
"""

class FallbackAI:
    def __init__(self):
        self.available = False
    
    def process_text(self, text):
        return {"processed": text, "confidence": 0.5}
    
    def analyze_content(self, content):
        return {"quality": 0.7, "sentiment": "neutral"}

# إنشاء كائن عام
fallback_ai = FallbackAI()
'''
    
    with open(os.path.join(fallback_dir, "ai_fallback.py"), "w", encoding="utf-8") as f:
        f.write(ai_fallback)
    
    # إنشاء وحدة احتياطية للرؤية الحاسوبية
    cv_fallback = '''
"""
وحدة احتياطية للرؤية الحاسوبية
"""

class FallbackCV:
    def __init__(self):
        self.available = False
    
    def process_image(self, image_path):
        return {"processed": True, "features": []}
    
    def analyze_image(self, image_data):
        return {"quality": 0.7, "objects": []}

# إنشاء كائن عام
fallback_cv = FallbackCV()
'''
    
    with open(os.path.join(fallback_dir, "cv_fallback.py"), "w", encoding="utf-8") as f:
        f.write(cv_fallback)
    
    print("✅ تم إنشاء الوحدات الاحتياطية")

def main():
    """الدالة الرئيسية لإصلاح المكتبات"""
    print("🚀 بدء إصلاح مشاكل المكتبات المفقودة...")
    
    results = {
        "ai": check_and_install_ai_libraries(),
        "cv": check_and_install_cv_libraries(),
        "audio": check_and_install_audio_libraries(),
        "web": check_and_install_web_libraries(),
        "database": check_and_install_database_libraries()
    }
    
    # إنشاء وحدات احتياطية للمكتبات المفقودة
    create_fallback_modules()
    
    print("\n📊 ملخص النتائج:")
    for category, success in results.items():
        status = "✅ مكتمل" if success else "⚠️ جزئي"
        print(f"   {category}: {status}")
    
    if all(results.values()):
        print("\n✅ تم إصلاح جميع مشاكل المكتبات بنجاح!")
    else:
        print("\n⚠️ تم إصلاح معظم المشاكل، بعض المكتبات قد تحتاج تثبيت يدوي")
        print("💡 يمكن للنظام العمل مع الوحدات الاحتياطية")

if __name__ == "__main__":
    main()
