#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المحسن لإنشاء الصور اليدوية
Test Enhanced Manual Image Generation System
"""

import asyncio
import os
import sys
import json
from datetime import datetime
from typing import Dict, List

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.manual_image_generator import ManualImageGenerator
from modules.logger import logger

class EnhancedManualImageTester:
    """فئة اختبار النظام المحسن لإنشاء الصور اليدوية"""
    
    def __init__(self):
        self.generator = ManualImageGenerator("Gaming News Enhanced")
        self.test_results = []
        self.output_dir = "test_results/enhanced_manual_images"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # مقالات اختبار متنوعة لفئات مختلفة
        self.test_articles = [
            {
                'title': 'Call of Duty: Modern Warfare III - مراجعة شاملة للعبة الأكشن الجديدة',
                'content': 'لعبة أكشن مليئة بالمعارك والقتال والحروب المثيرة',
                'expected_theme': 'action',
                'expected_category': 'action'
            },
            {
                'title': 'Super Mario Bros Wonder - عودة الألعاب الكلاسيكية',
                'content': 'نينتندو تعيد إحياء الألعاب الكلاسيكية مع ماريو الجديد',
                'expected_theme': 'classic',
                'expected_category': 'classic'
            },
            {
                'title': 'The Legend of Zelda: Tears of the Kingdom - مغامرة ملحمية',
                'content': 'مغامرة استكشافية رائعة في عالم زيلدا الساحر',
                'expected_theme': 'adventure',
                'expected_category': 'adventure'
            },
            {
                'title': 'Resident Evil 4 Remake - رعب البقاء على قيد الحياة',
                'content': 'لعبة رعب مخيفة مع زومبي ومخلوقات مرعبة',
                'expected_theme': 'horror',
                'expected_category': 'horror'
            },
            {
                'title': 'Forza Horizon 5 - سباق السيارات المثير',
                'content': 'سباق سيارات بسرعة عالية وسيارات فورمولا رائعة',
                'expected_theme': 'racing',
                'expected_category': 'racing'
            },
            {
                'title': 'FIFA 24 - كرة القدم الواقعية',
                'content': 'لعبة كرة قدم رياضية واقعية مع فرق عالمية',
                'expected_theme': 'sports',
                'expected_category': 'sports'
            },
            {
                'title': 'Final Fantasy XVI - لعب الأدوار الخيالية',
                'content': 'لعبة ار بي جي خيالية مع سحر ومخلوقات أسطورية',
                'expected_theme': 'rpg',
                'expected_category': 'rpg'
            },
            {
                'title': 'Age of Empires IV - الاستراتيجية التكتيكية',
                'content': 'لعبة استراتيجية تكتيكية مع حروب وحضارات',
                'expected_theme': 'strategy',
                'expected_category': 'strategy'
            },
            {
                'title': 'The Sims 4 - محاكاة الحياة الواقعية',
                'content': 'محاكاة حياة واقعية مع بناء المدن والشخصيات',
                'expected_theme': 'simulation',
                'expected_category': 'simulation'
            },
            {
                'title': 'Portal 3 - ألغاز منطقية معقدة',
                'content': 'ألغاز ذكية تتطلب تفكير منطقي وحلول إبداعية',
                'expected_theme': 'puzzle',
                'expected_category': 'puzzle'
            }
        ]
    
    async def test_enhanced_theme_detection(self) -> Dict:
        """اختبار تحديد المواضيع المحسن"""
        logger.info("🎯 اختبار تحديد المواضيع المحسن...")
        
        results = {
            'total_tests': len(self.test_articles),
            'correct_detections': 0,
            'incorrect_detections': 0,
            'details': []
        }
        
        for article in self.test_articles:
            detected_theme = self.generator.detect_theme_from_article(article)
            expected_theme = article['expected_theme']
            is_correct = detected_theme == expected_theme
            
            if is_correct:
                results['correct_detections'] += 1
            else:
                results['incorrect_detections'] += 1
            
            results['details'].append({
                'title': article['title'],
                'expected': expected_theme,
                'detected': detected_theme,
                'correct': is_correct
            })
            
            logger.info(f"📝 {article['title'][:40]}... -> {detected_theme} ({'✅' if is_correct else '❌'})")
        
        accuracy = (results['correct_detections'] / results['total_tests']) * 100
        logger.info(f"🎯 دقة تحديد المواضيع المحسن: {accuracy:.1f}%")
        
        return results
    
    async def test_font_system(self) -> Dict:
        """اختبار نظام الخطوط المحسن"""
        logger.info("🔤 اختبار نظام الخطوط المحسن...")
        
        results = {
            'arabic_fonts_available': len(self.generator.get_available_fonts('arabic')),
            'english_fonts_available': len(self.generator.get_available_fonts('english')),
            'font_loading_tests': []
        }
        
        # اختبار تحميل خطوط عربية
        for size in [24, 36, 48]:
            try:
                font = self.generator.get_font(size, arabic_support=True)
                success = font is not None
                results['font_loading_tests'].append({
                    'size': size,
                    'language': 'arabic',
                    'success': success,
                    'font_type': type(font).__name__
                })
                logger.info(f"🔤 خط عربي حجم {size}: {'✅' if success else '❌'}")
            except Exception as e:
                results['font_loading_tests'].append({
                    'size': size,
                    'language': 'arabic',
                    'success': False,
                    'error': str(e)
                })
        
        # اختبار تحميل خطوط إنجليزية
        for size in [24, 36, 48]:
            try:
                font = self.generator.get_font(size, arabic_support=False)
                success = font is not None
                results['font_loading_tests'].append({
                    'size': size,
                    'language': 'english',
                    'success': success,
                    'font_type': type(font).__name__
                })
                logger.info(f"🔤 خط إنجليزي حجم {size}: {'✅' if success else '❌'}")
            except Exception as e:
                results['font_loading_tests'].append({
                    'size': size,
                    'language': 'english',
                    'success': False,
                    'error': str(e)
                })
        
        logger.info(f"📊 خطوط عربية متاحة: {results['arabic_fonts_available']}")
        logger.info(f"📊 خطوط إنجليزية متاحة: {results['english_fonts_available']}")
        
        return results
    
    async def test_background_system(self) -> Dict:
        """اختبار نظام الخلفيات المحسن"""
        logger.info("🖼️ اختبار نظام الخلفيات المحسن...")
        
        results = {
            'background_categories': {},
            'total_backgrounds': 0
        }
        
        for category, folder_name in self.generator.background_categories.items():
            available_bgs = self.generator.get_available_backgrounds(category)
            count = len(available_bgs)
            results['background_categories'][category] = {
                'folder_name': folder_name,
                'count': count,
                'available': count > 0
            }
            results['total_backgrounds'] += count
            
            status = '✅' if count > 0 else '❌'
            logger.info(f"🖼️ {category} ({folder_name}): {count} خلفية {status}")
        
        logger.info(f"📊 إجمالي الخلفيات المتاحة: {results['total_backgrounds']}")
        
        return results
    
    async def test_enhanced_image_generation(self) -> Dict:
        """اختبار إنشاء الصور المحسن"""
        logger.info("🎨 اختبار إنشاء الصور المحسن...")
        
        results = {
            'total_tests': len(self.test_articles),
            'successful_generations': 0,
            'failed_generations': 0,
            'background_types_used': {'image': 0, 'gradient': 0},
            'details': []
        }
        
        for i, article in enumerate(self.test_articles):
            logger.info(f"🎨 اختبار {i+1}/{len(self.test_articles)}: {article['title'][:40]}...")
            
            try:
                image_result = await self.generator.generate_manual_image(article)
                
                if image_result:
                    results['successful_generations'] += 1
                    
                    # تحديد نوع الخلفية المستخدمة
                    bg_type = 'image' if 'real_background' in str(image_result) else 'gradient'
                    results['background_types_used'][bg_type] += 1
                    
                    # فحص الملف المُنشأ
                    file_exists = os.path.exists(image_result['local_path'])
                    file_size = os.path.getsize(image_result['local_path']) if file_exists else 0
                    
                    results['details'].append({
                        'article_title': article['title'],
                        'success': True,
                        'filename': image_result['filename'],
                        'file_exists': file_exists,
                        'file_size_kb': round(file_size / 1024, 2),
                        'theme': image_result.get('theme', 'unknown'),
                        'background_type': bg_type,
                        'generation_method': image_result.get('generation_method', 'unknown')
                    })
                    
                    logger.info(f"✅ تم إنشاء الصورة بنجاح: {image_result['filename']} ({file_size/1024:.1f} KB)")
                    
                else:
                    results['failed_generations'] += 1
                    results['details'].append({
                        'article_title': article['title'],
                        'success': False,
                        'error': 'No image result returned'
                    })
                    logger.error(f"❌ فشل في إنشاء الصورة للمقال: {article['title']}")
                    
            except Exception as e:
                results['failed_generations'] += 1
                results['details'].append({
                    'article_title': article['title'],
                    'success': False,
                    'error': str(e)
                })
                logger.error(f"❌ خطأ في إنشاء الصورة: {e}")
        
        success_rate = (results['successful_generations'] / results['total_tests']) * 100
        logger.info(f"🎨 معدل نجاح إنشاء الصور المحسن: {success_rate:.1f}%")
        logger.info(f"🖼️ خلفيات صور حقيقية: {results['background_types_used']['image']}")
        logger.info(f"🎨 خلفيات متدرجة: {results['background_types_used']['gradient']}")
        
        return results

    async def run_comprehensive_enhanced_test(self) -> Dict:
        """تشغيل الاختبار الشامل للنظام المحسن"""
        logger.info("🚀 بدء الاختبار الشامل للنظام المحسن لإنشاء الصور اليدوية...")
        logger.info("=" * 70)

        start_time = datetime.now()

        # تشغيل جميع الاختبارات
        test_results = {
            'start_time': start_time.isoformat(),
            'enhanced_theme_detection': await self.test_enhanced_theme_detection(),
            'font_system': await self.test_font_system(),
            'background_system': await self.test_background_system(),
            'enhanced_image_generation': await self.test_enhanced_image_generation()
        }

        end_time = datetime.now()
        test_results['end_time'] = end_time.isoformat()
        test_results['duration_seconds'] = (end_time - start_time).total_seconds()

        # حساب الإحصائيات العامة
        total_tests = (
            test_results['enhanced_theme_detection']['total_tests'] +
            len(test_results['font_system']['font_loading_tests']) +
            test_results['enhanced_image_generation']['total_tests']
        )

        successful_tests = (
            test_results['enhanced_theme_detection']['correct_detections'] +
            sum(1 for test in test_results['font_system']['font_loading_tests'] if test['success']) +
            test_results['enhanced_image_generation']['successful_generations']
        )

        overall_success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0

        test_results['summary'] = {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'failed_tests': total_tests - successful_tests,
            'overall_success_rate': round(overall_success_rate, 2),
            'duration_minutes': round(test_results['duration_seconds'] / 60, 2),
            'enhancements_status': {
                'theme_detection_accuracy': round(
                    (test_results['enhanced_theme_detection']['correct_detections'] /
                     test_results['enhanced_theme_detection']['total_tests']) * 100, 1
                ),
                'custom_fonts_available': (
                    test_results['font_system']['arabic_fonts_available'] > 0 or
                    test_results['font_system']['english_fonts_available'] > 0
                ),
                'custom_backgrounds_available': test_results['background_system']['total_backgrounds'] > 0,
                'image_generation_success_rate': round(
                    (test_results['enhanced_image_generation']['successful_generations'] /
                     test_results['enhanced_image_generation']['total_tests']) * 100, 1
                )
            }
        }

        # حفظ النتائج
        results_file = os.path.join(self.output_dir, f"enhanced_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)

        # طباعة الملخص
        logger.info("=" * 70)
        logger.info("📊 ملخص نتائج الاختبار الشامل للنظام المحسن:")
        logger.info(f"⏱️ المدة الإجمالية: {test_results['summary']['duration_minutes']} دقيقة")
        logger.info(f"📈 إجمالي الاختبارات: {test_results['summary']['total_tests']}")
        logger.info(f"✅ الاختبارات الناجحة: {test_results['summary']['successful_tests']}")
        logger.info(f"❌ الاختبارات الفاشلة: {test_results['summary']['failed_tests']}")
        logger.info(f"🎯 معدل النجاح الإجمالي: {test_results['summary']['overall_success_rate']}%")
        logger.info("")
        logger.info("🔧 حالة التحسينات:")
        logger.info(f"🎯 دقة تحديد المواضيع: {test_results['summary']['enhancements_status']['theme_detection_accuracy']}%")
        logger.info(f"🔤 خطوط مخصصة متاحة: {'✅' if test_results['summary']['enhancements_status']['custom_fonts_available'] else '❌'}")
        logger.info(f"🖼️ خلفيات مخصصة متاحة: {'✅' if test_results['summary']['enhancements_status']['custom_backgrounds_available'] else '❌'}")
        logger.info(f"🎨 معدل نجاح إنشاء الصور: {test_results['summary']['enhancements_status']['image_generation_success_rate']}%")
        logger.info(f"💾 تم حفظ النتائج في: {results_file}")
        logger.info("=" * 70)

        return test_results

    def print_enhancement_recommendations(self, results: Dict):
        """طباعة توصيات التحسين"""
        logger.info("💡 توصيات التحسين:")

        enhancements = results['summary']['enhancements_status']

        if not enhancements['custom_fonts_available']:
            logger.info("🔤 يُنصح بإضافة خطوط مخصصة في:")
            logger.info(f"   - {self.generator.fonts_dir}/arabic/ (للخطوط العربية)")
            logger.info(f"   - {self.generator.fonts_dir}/english/ (للخطوط الإنجليزية)")

        if not enhancements['custom_backgrounds_available']:
            logger.info("🖼️ يُنصح بإضافة خلفيات مخصصة في:")
            for category, folder_name in self.generator.background_categories.items():
                logger.info(f"   - {self.generator.backgrounds_dir}/{folder_name}/ (لفئة {category})")

        if enhancements['theme_detection_accuracy'] < 90:
            logger.info("🎯 يمكن تحسين دقة تحديد المواضيع بإضافة كلمات مفتاحية أكثر")

        if enhancements['image_generation_success_rate'] < 100:
            logger.info("🎨 تحقق من وجود مشاكل في إنشاء الصور وحلها")

async def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🎮 بدء اختبار النظام المحسن لإنشاء الصور اليدوية للألعاب")

        tester = EnhancedManualImageTester()
        results = await tester.run_comprehensive_enhanced_test()

        # طباعة توصيات التحسين
        tester.print_enhancement_recommendations(results)

        logger.info("🎉 تم الانتهاء من اختبار النظام المحسن بنجاح!")

        return results

    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الاختبار: {e}")
        return None

if __name__ == "__main__":
    # تشغيل الاختبار
    results = asyncio.run(main())
