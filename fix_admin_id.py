#!/usr/bin/env python3
"""
سكريبت لإصلاح معرف المدير تلقائياً في ملف الإعدادات
"""

import asyncio
import re
from telegram import Bo<PERSON>
from config.settings import BotConfig

async def fix_admin_id():
    """إصلاح معرف المدير تلقائياً"""
    try:
        print("🔧 بدء إصلاح معرف المدير...")
        
        # إنشاء البوت
        bot = Bot(token=BotConfig.TELEGRAM_BOT_TOKEN)
        
        # الحصول على التحديثات
        updates = await bot.get_updates(limit=20)
        
        if not updates:
            print("⚠️ لا توجد رسائل حديثة")
            print("💡 أرسل رسالة للبوت أولاً ثم شغل هذا السكريبت")
            return
        
        # البحث عن معرف صالح
        valid_admin = None
        
        for update in reversed(updates):
            if update.message and update.message.from_user:
                chat_id = update.message.chat_id
                username = update.message.from_user.username or "غير محدد"
                first_name = update.message.from_user.first_name or "غير محدد"
                
                # التحقق من صحة المعرف
                try:
                    await bot.send_chat_action(chat_id=chat_id, action="typing")
                    valid_admin = {
                        'chat_id': str(chat_id),
                        'username': username,
                        'first_name': first_name
                    }
                    print(f"✅ تم العثور على معرف صالح: {chat_id} ({first_name})")
                    break
                except Exception:
                    continue
        
        if not valid_admin:
            print("❌ لم يتم العثور على معرف صالح")
            return
        
        # قراءة ملف الإعدادات
        settings_file = "config/settings.py"
        
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"❌ فشل في قراءة ملف الإعدادات: {e}")
            return
        
        # البحث عن السطر الحالي وتحديثه
        old_pattern = r'TELEGRAM_ADMIN_ID\s*=\s*["\'].*?["\']'
        new_line = f'TELEGRAM_ADMIN_ID = "{valid_admin["chat_id"]}"'
        
        if re.search(old_pattern, content):
            # تحديث السطر الموجود
            new_content = re.sub(old_pattern, new_line, content)
            
            # حفظ الملف المحدث
            try:
                with open(settings_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"✅ تم تحديث معرف المدير في {settings_file}")
                print(f"🆔 المعرف الجديد: {valid_admin['chat_id']}")
                print(f"👤 المدير: {valid_admin['first_name']} (@{valid_admin['username']})")
                
                # اختبار المعرف الجديد
                print("\n🧪 اختبار المعرف الجديد...")
                test_message = f"""✅ <b>تم إصلاح معرف المدير بنجاح!</b>

🆔 <b>المعرف الجديد:</b> {valid_admin['chat_id']}
👤 <b>المدير:</b> {valid_admin['first_name']}

🤖 <i>نظام الإشعارات يعمل الآن بشكل صحيح</i>"""
                
                await bot.send_message(
                    chat_id=valid_admin['chat_id'],
                    text=test_message,
                    parse_mode='HTML'
                )
                
                print("✅ تم إرسال رسالة تأكيد للمدير!")
                print("📱 تحقق من تيليجرام لرؤية الرسالة")
                
            except Exception as e:
                print(f"❌ فشل في حفظ ملف الإعدادات: {e}")
        else:
            print("⚠️ لم يتم العثور على TELEGRAM_ADMIN_ID في ملف الإعدادات")
            print(f"💡 أضف هذا السطر يدوياً: {new_line}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    asyncio.run(fix_admin_id())
