#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت بدء تشغيل محسن - بدون تحذيرات
"""

import os
import sys
import warnings

# إخفاء جميع التحذيرات
warnings.filterwarnings("ignore")

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# تشغيل الإصلاحات قبل البدء
try:
    from final_warnings_fix import (
        patch_advanced_rag_system,
        patch_multimodal_analyzer, 
        patch_memory_system,
        suppress_startup_warnings
    )
    
    # تطبيق الإصلاحات بصمت
    suppress_startup_warnings()
    patch_advanced_rag_system()
    patch_multimodal_analyzer()
    patch_memory_system()
    
except ImportError:
    pass

# تشغيل البرنامج الرئيسي
if __name__ == "__main__":
    from main import main
    main()
