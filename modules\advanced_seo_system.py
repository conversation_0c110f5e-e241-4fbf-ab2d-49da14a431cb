#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام SEO شامل متقدم مع Keyword Tool API
يقوم بتحليل الكلمات المفتاحية الرائجة وتحسين الربط الداخلي
"""

import asyncio
import aiohttp
import sqlite3
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin, urlparse
import requests
from bs4 import BeautifulSoup

from .logger import logger
from .database import db

class KeywordToolAPI:
    """واجهة برمجة تطبيقات Keyword Tool"""
    
    def __init__(self):
        # يمكن إضافة مفتاح API هنا إذا كان متوفراً
        self.api_key = None
        self.base_url = "https://keywordtool.io"
        
        # كلمات مفتاحية شائعة في مجال الألعاب كبديل
        self.gaming_keywords_database = {
            'trending': [
                'ألعاب 2025', 'gaming 2025', 'new games', 'ألعاب جديدة',
                'game review', 'مراجعة لعبة', 'gaming news', 'أخبار الألعاب',
                'best games', 'أفضل الألعاب', 'game guide', 'دليل اللعبة',
                'gaming tips', 'نصائح الألعاب', 'game update', 'تحديث اللعبة'
            ],
            'high_volume': [
                'minecraft', 'fortnite', 'call of duty', 'fifa', 'gta',
                'cyberpunk', 'assassins creed', 'the witcher', 'god of war',
                'playstation', 'xbox', 'nintendo', 'steam', 'epic games'
            ],
            'long_tail': [
                'كيفية لعب minecraft', 'أفضل استراتيجيات fortnite',
                'مراجعة شاملة cyberpunk 2077', 'دليل المبتدئين gta',
                'نصائح وحيل call of duty', 'أسرار the witcher 3'
            ],
            'arabic_gaming': [
                'ألعاب عربية', 'مراجعات الألعاب', 'أخبار الألعاب العربية',
                'دليل الألعاب', 'نصائح الألعاب', 'استراتيجيات الألعاب',
                'ألعاب مجانية', 'ألعاب أونلاين', 'ألعاب موبايل'
            ]
        }
    
    async def get_trending_keywords(self, seed_keyword: str, language: str = 'ar') -> List[Dict]:
        """الحصول على الكلمات المفتاحية الرائجة"""
        try:
            # محاولة استخدام API الحقيقي إذا كان متوفراً
            if self.api_key:
                return await self._fetch_from_api(seed_keyword, language)
            else:
                # استخدام قاعدة البيانات المحلية كبديل
                return self._get_keywords_from_database(seed_keyword)
                
        except Exception as e:
            logger.error("❌ فشل في الحصول على الكلمات المفتاحية الرائجة", e)
            return self._get_keywords_from_database(seed_keyword)
    
    async def _fetch_from_api(self, seed_keyword: str, language: str) -> List[Dict]:
        """جلب الكلمات من API الحقيقي"""
        try:
            # هذا مثال لكيفية استخدام API حقيقي
            # يجب تخصيصه حسب API المستخدم
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            params = {
                'keyword': seed_keyword,
                'language': language,
                'country': 'SA',  # السعودية
                'limit': 50
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/api/keywords",
                    headers=headers,
                    params=params
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._process_api_response(data)
                    else:
                        logger.warning(f"⚠️ API استجاب بحالة {response.status}")
                        return self._get_keywords_from_database(seed_keyword)
                        
        except Exception as e:
            logger.error("❌ فشل في جلب البيانات من API", e)
            return self._get_keywords_from_database(seed_keyword)
    
    def _get_keywords_from_database(self, seed_keyword: str) -> List[Dict]:
        """الحصول على الكلمات من قاعدة البيانات المحلية"""
        try:
            seed_lower = seed_keyword.lower()
            relevant_keywords = []
            
            # البحث في الكلمات الرائجة
            for keyword in self.gaming_keywords_database['trending']:
                if any(word in keyword.lower() for word in seed_lower.split()):
                    relevant_keywords.append({
                        'keyword': keyword,
                        'search_volume': 5000 + hash(keyword) % 10000,  # محاكاة حجم البحث
                        'competition': 'medium',
                        'cpc': round(0.5 + (hash(keyword) % 100) / 100, 2),
                        'trend': 'rising'
                    })
            
            # إضافة كلمات عالية الحجم
            for keyword in self.gaming_keywords_database['high_volume']:
                if seed_lower in keyword or keyword in seed_lower:
                    relevant_keywords.append({
                        'keyword': keyword,
                        'search_volume': 10000 + hash(keyword) % 20000,
                        'competition': 'high',
                        'cpc': round(1.0 + (hash(keyword) % 200) / 100, 2),
                        'trend': 'stable'
                    })
            
            # إضافة كلمات طويلة الذيل
            for keyword in self.gaming_keywords_database['long_tail']:
                if any(word in keyword.lower() for word in seed_lower.split()):
                    relevant_keywords.append({
                        'keyword': keyword,
                        'search_volume': 500 + hash(keyword) % 2000,
                        'competition': 'low',
                        'cpc': round(0.2 + (hash(keyword) % 50) / 100, 2),
                        'trend': 'growing'
                    })
            
            # إضافة كلمات عربية
            for keyword in self.gaming_keywords_database['arabic_gaming']:
                relevant_keywords.append({
                    'keyword': keyword,
                    'search_volume': 1000 + hash(keyword) % 5000,
                    'competition': 'medium',
                    'cpc': round(0.3 + (hash(keyword) % 80) / 100, 2),
                    'trend': 'stable'
                })
            
            # ترتيب حسب حجم البحث
            relevant_keywords.sort(key=lambda x: x['search_volume'], reverse=True)
            
            return relevant_keywords[:20]  # أفضل 20 كلمة
            
        except Exception as e:
            logger.error("❌ فشل في الحصول على الكلمات من قاعدة البيانات المحلية", e)
            return []
    
    def _process_api_response(self, data: Dict) -> List[Dict]:
        """معالجة استجابة API"""
        try:
            keywords = []
            
            for item in data.get('keywords', []):
                keywords.append({
                    'keyword': item.get('keyword', ''),
                    'search_volume': item.get('search_volume', 0),
                    'competition': item.get('competition', 'medium'),
                    'cpc': item.get('cpc', 0.0),
                    'trend': item.get('trend', 'stable')
                })
            
            return keywords
            
        except Exception as e:
            logger.error("❌ فشل في معالجة استجابة API", e)
            return []

class InternalLinkingOptimizer:
    """محسن الربط الداخلي"""
    
    def __init__(self):
        self.db_path = "data/articles.db"
        self._init_linking_tables()
    
    def _init_linking_tables(self):
        """إنشاء جداول الربط الداخلي"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول الروابط الداخلية
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS internal_links (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        source_article_id INTEGER NOT NULL,
                        target_article_id INTEGER NOT NULL,
                        anchor_text TEXT NOT NULL,
                        link_context TEXT,
                        relevance_score REAL DEFAULT 0.0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (source_article_id) REFERENCES published_articles (id),
                        FOREIGN KEY (target_article_id) REFERENCES published_articles (id)
                    )
                ''')
                
                # جدول تحليل الربط
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS link_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER NOT NULL,
                        internal_links_count INTEGER DEFAULT 0,
                        external_links_count INTEGER DEFAULT 0,
                        incoming_links_count INTEGER DEFAULT 0,
                        link_density REAL DEFAULT 0.0,
                        anchor_diversity_score REAL DEFAULT 0.0,
                        analysis_date DATE DEFAULT CURRENT_DATE,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')
                
                conn.commit()
                logger.info("✅ تم إنشاء جداول الربط الداخلي بنجاح")
                
        except Exception as e:
            logger.error("❌ فشل في إنشاء جداول الربط الداخلي", e)
    
    async def analyze_and_optimize_internal_links(self, article_id: int) -> Dict:
        """تحليل وتحسين الروابط الداخلية للمقال"""
        try:
            # الحصول على بيانات المقال
            article = self._get_article_data(article_id)
            if not article:
                return {}
            
            # العثور على المقالات ذات الصلة
            related_articles = self._find_related_articles(article)
            
            # إنشاء اقتراحات الربط
            link_suggestions = self._generate_link_suggestions(article, related_articles)
            
            # تحليل الروابط الحالية
            current_links = self._analyze_current_links(article_id)
            
            # حساب نقاط التحسين
            optimization_score = self._calculate_optimization_score(current_links, link_suggestions)
            
            result = {
                'article_id': article_id,
                'current_links': current_links,
                'link_suggestions': link_suggestions,
                'optimization_score': optimization_score,
                'analysis_date': datetime.now().isoformat()
            }
            
            # حفظ التحليل
            self._save_link_analysis(article_id, result)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ فشل في تحليل الروابط الداخلية للمقال {article_id}", e)
            return {}
    
    def _get_article_data(self, article_id: int) -> Optional[Dict]:
        """الحصول على بيانات المقال"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id, title, content, keywords, category
                    FROM published_articles 
                    WHERE id = ?
                ''', (article_id,))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'id': row[0],
                        'title': row[1],
                        'content': row[2],
                        'keywords': row[3],
                        'category': row[4]
                    }
                
                return None
                
        except Exception as e:
            logger.error(f"❌ فشل في الحصول على بيانات المقال {article_id}", e)
            return None
    
    def _find_related_articles(self, article: Dict) -> List[Dict]:
        """العثور على المقالات ذات الصلة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # البحث بناءً على التصنيف
                category_articles = []
                if article.get('category'):
                    cursor.execute('''
                        SELECT id, title, keywords, category
                        FROM published_articles 
                        WHERE category = ? AND id != ?
                        LIMIT 10
                    ''', (article['category'], article['id']))
                    
                    category_articles = [
                        {'id': row[0], 'title': row[1], 'keywords': row[2], 'category': row[3]}
                        for row in cursor.fetchall()
                    ]
                
                # البحث بناءً على الكلمات المفتاحية
                keyword_articles = []
                if article.get('keywords'):
                    keywords = article['keywords'].split(',')
                    for keyword in keywords[:3]:  # أول 3 كلمات مفتاحية
                        keyword = keyword.strip()
                        cursor.execute('''
                            SELECT id, title, keywords, category
                            FROM published_articles 
                            WHERE (keywords LIKE ? OR title LIKE ?) AND id != ?
                            LIMIT 5
                        ''', (f'%{keyword}%', f'%{keyword}%', article['id']))
                        
                        keyword_articles.extend([
                            {'id': row[0], 'title': row[1], 'keywords': row[2], 'category': row[3]}
                            for row in cursor.fetchall()
                        ])
                
                # دمج النتائج وإزالة التكرار
                all_related = category_articles + keyword_articles
                unique_related = []
                seen_ids = set()
                
                for related in all_related:
                    if related['id'] not in seen_ids:
                        unique_related.append(related)
                        seen_ids.add(related['id'])
                
                return unique_related[:15]  # أفضل 15 مقال ذو صلة
                
        except Exception as e:
            logger.error("❌ فشل في العثور على المقالات ذات الصلة", e)
            return []

    def _generate_link_suggestions(self, article: Dict, related_articles: List[Dict]) -> List[Dict]:
        """إنشاء اقتراحات الربط"""
        try:
            suggestions = []
            content = article.get('content', '') or ''

            for related in related_articles:
                # حساب نقاط الصلة
                relevance_score = self._calculate_relevance_score(article, related)

                if relevance_score > 0.3:  # حد أدنى للصلة
                    # اقتراح نص الرابط
                    anchor_text = self._suggest_anchor_text(article, related)

                    # العثور على أفضل مكان للربط
                    link_position = self._find_best_link_position(content, related)

                    suggestions.append({
                        'target_article_id': related['id'],
                        'target_title': related['title'],
                        'anchor_text': anchor_text,
                        'relevance_score': relevance_score,
                        'link_position': link_position,
                        'reason': self._get_linking_reason(article, related)
                    })

            # ترتيب حسب نقاط الصلة
            suggestions.sort(key=lambda x: x['relevance_score'], reverse=True)

            return suggestions[:8]  # أفضل 8 اقتراحات

        except Exception as e:
            logger.error("❌ فشل في إنشاء اقتراحات الربط", e)
            return []

    def _calculate_relevance_score(self, article: Dict, related: Dict) -> float:
        """حساب نقاط الصلة بين المقالين"""
        try:
            score = 0.0

            # نقاط التصنيف (30%)
            if article.get('category') == related.get('category'):
                score += 0.3

            # نقاط الكلمات المفتاحية (50%)
            article_keywords = set((article.get('keywords', '') or '').lower().split(','))
            related_keywords = set((related.get('keywords', '') or '').lower().split(','))

            if article_keywords and related_keywords:
                common_keywords = article_keywords.intersection(related_keywords)
                keyword_similarity = len(common_keywords) / len(article_keywords.union(related_keywords))
                score += keyword_similarity * 0.5

            # نقاط العنوان (20%)
            article_title_words = set(article.get('title', '').lower().split())
            related_title_words = set(related.get('title', '').lower().split())

            if article_title_words and related_title_words:
                common_title_words = article_title_words.intersection(related_title_words)
                title_similarity = len(common_title_words) / len(article_title_words.union(related_title_words))
                score += title_similarity * 0.2

            return min(1.0, score)

        except Exception as e:
            logger.error("❌ فشل في حساب نقاط الصلة", e)
            return 0.0

    def _suggest_anchor_text(self, article: Dict, related: Dict) -> str:
        """اقتراح نص الرابط"""
        try:
            related_title = related.get('title', '')

            # قوالب نصوص الروابط
            anchor_templates = [
                related_title,
                f"اقرأ أيضاً: {related_title}",
                f"تعرف على {related_title}",
                f"دليل {related_title}",
                f"مراجعة {related_title}",
                f"كل ما تحتاج معرفته عن {related_title}"
            ]

            # اختيار أفضل قالب بناءً على التصنيف
            category = related.get('category', '')

            if 'مراجعات' in category:
                return f"مراجعة {related_title}"
            elif 'أدلة' in category:
                return f"دليل {related_title}"
            elif 'أخبار' in category:
                return f"اقرأ أيضاً: {related_title}"
            else:
                return related_title

        except Exception as e:
            logger.error("❌ فشل في اقتراح نص الرابط", e)
            return related.get('title', 'رابط ذو صلة')

    def _find_best_link_position(self, content: str, related: Dict) -> str:
        """العثور على أفضل مكان للربط"""
        try:
            if not content:
                return "في نهاية المقال"

            # البحث عن كلمات مفتاحية مشتركة في المحتوى
            related_keywords = (related.get('keywords', '') or '').split(',')

            for keyword in related_keywords:
                keyword = keyword.strip().lower()
                if keyword and keyword in content.lower():
                    return f"بالقرب من كلمة '{keyword}'"

            # البحث عن كلمات من العنوان
            title_words = related.get('title', '').split()
            for word in title_words:
                if len(word) > 3 and word.lower() in content.lower():
                    return f"بالقرب من كلمة '{word}'"

            return "في منتصف المقال"

        except Exception as e:
            logger.error("❌ فشل في العثور على أفضل مكان للربط", e)
            return "في نهاية المقال"

    def _get_linking_reason(self, article: Dict, related: Dict) -> str:
        """الحصول على سبب الربط"""
        try:
            if article.get('category') == related.get('category'):
                return f"نفس التصنيف ({article.get('category')})"

            # فحص الكلمات المفتاحية المشتركة
            article_keywords = set((article.get('keywords', '') or '').lower().split(','))
            related_keywords = set((related.get('keywords', '') or '').lower().split(','))

            common_keywords = article_keywords.intersection(related_keywords)
            if common_keywords:
                return f"كلمات مفتاحية مشتركة: {', '.join(list(common_keywords)[:2])}"

            return "محتوى ذو صلة"

        except Exception as e:
            logger.error("❌ فشل في الحصول على سبب الربط", e)
            return "محتوى ذو صلة"

    def _analyze_current_links(self, article_id: int) -> Dict:
        """تحليل الروابط الحالية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # عدد الروابط الداخلية الصادرة
                cursor.execute('''
                    SELECT COUNT(*) FROM internal_links
                    WHERE source_article_id = ?
                ''', (article_id,))
                outgoing_links = cursor.fetchone()[0]

                # عدد الروابط الداخلية الواردة
                cursor.execute('''
                    SELECT COUNT(*) FROM internal_links
                    WHERE target_article_id = ?
                ''', (article_id,))
                incoming_links = cursor.fetchone()[0]

                return {
                    'outgoing_internal_links': outgoing_links,
                    'incoming_internal_links': incoming_links,
                    'link_density': self._calculate_link_density(article_id),
                    'anchor_diversity': self._calculate_anchor_diversity(article_id)
                }

        except Exception as e:
            logger.error(f"❌ فشل في تحليل الروابط الحالية للمقال {article_id}", e)
            return {}

    def _calculate_link_density(self, article_id: int) -> float:
        """حساب كثافة الروابط"""
        try:
            article = self._get_article_data(article_id)
            if not article or not article.get('content'):
                return 0.0

            content = article['content']
            word_count = len(content.split())

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT COUNT(*) FROM internal_links
                    WHERE source_article_id = ?
                ''', (article_id,))
                link_count = cursor.fetchone()[0]

            if word_count > 0:
                return (link_count / word_count) * 100

            return 0.0

        except Exception as e:
            logger.error(f"❌ فشل في حساب كثافة الروابط للمقال {article_id}", e)
            return 0.0

    def _calculate_anchor_diversity(self, article_id: int) -> float:
        """حساب تنوع نصوص الروابط"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT anchor_text FROM internal_links
                    WHERE source_article_id = ?
                ''', (article_id,))

                anchors = [row[0] for row in cursor.fetchall()]

                if not anchors:
                    return 0.0

                unique_anchors = len(set(anchors))
                total_anchors = len(anchors)

                return (unique_anchors / total_anchors) * 100 if total_anchors > 0 else 0.0

        except Exception as e:
            logger.error(f"❌ فشل في حساب تنوع نصوص الروابط للمقال {article_id}", e)
            return 0.0

    def _calculate_optimization_score(self, current_links: Dict, suggestions: List[Dict]) -> float:
        """حساب نقاط التحسين"""
        try:
            score = 0.0

            # نقاط الروابط الحالية (40%)
            outgoing = current_links.get('outgoing_internal_links', 0)
            if 3 <= outgoing <= 8:  # العدد المثالي
                score += 40
            elif outgoing > 0:
                score += 20

            # نقاط كثافة الروابط (30%)
            density = current_links.get('link_density', 0)
            if 1 <= density <= 3:  # الكثافة المثالية
                score += 30
            elif density > 0:
                score += 15

            # نقاط تنوع النصوص (20%)
            diversity = current_links.get('anchor_diversity', 0)
            if diversity >= 70:
                score += 20
            elif diversity >= 50:
                score += 15
            elif diversity > 0:
                score += 10

            # نقاط الاقتراحات (10%)
            if suggestions:
                avg_relevance = sum(s['relevance_score'] for s in suggestions) / len(suggestions)
                score += avg_relevance * 10

            return min(100.0, score)

        except Exception as e:
            logger.error("❌ فشل في حساب نقاط التحسين", e)
            return 0.0

    def _save_link_analysis(self, article_id: int, analysis: Dict):
        """حفظ تحليل الروابط"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                current_links = analysis.get('current_links', {})

                cursor.execute('''
                    INSERT OR REPLACE INTO link_analysis
                    (article_id, internal_links_count, incoming_links_count,
                     link_density, anchor_diversity_score, analysis_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    article_id,
                    current_links.get('outgoing_internal_links', 0),
                    current_links.get('incoming_internal_links', 0),
                    current_links.get('link_density', 0.0),
                    current_links.get('anchor_diversity', 0.0),
                    datetime.now().date()
                ))

                conn.commit()

        except Exception as e:
            logger.error("❌ فشل في حفظ تحليل الروابط", e)

class AdvancedSEOSystem:
    """نظام SEO شامل متقدم"""

    def __init__(self):
        self.keyword_tool = KeywordToolAPI()
        self.link_optimizer = InternalLinkingOptimizer()
        self.db_path = "data/articles.db"
        self._init_seo_tables()

    def _init_seo_tables(self):
        """إنشاء جداول SEO"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # جدول تحليل SEO الشامل
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS seo_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER NOT NULL,
                        keyword_optimization_score REAL DEFAULT 0.0,
                        content_optimization_score REAL DEFAULT 0.0,
                        technical_seo_score REAL DEFAULT 0.0,
                        internal_linking_score REAL DEFAULT 0.0,
                        overall_seo_score REAL DEFAULT 0.0,
                        recommendations TEXT,
                        analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')

                conn.commit()
                logger.info("✅ تم إنشاء جداول SEO بنجاح")

        except Exception as e:
            logger.error("❌ فشل في إنشاء جداول SEO", e)

    async def comprehensive_seo_analysis(self, article_id: int) -> Dict:
        """تحليل SEO شامل للمقال"""
        try:
            logger.info(f"🔍 بدء تحليل SEO شامل للمقال {article_id}...")

            # الحصول على بيانات المقال
            article = self.link_optimizer._get_article_data(article_id)
            if not article:
                return {}

            # 1. تحليل الكلمات المفتاحية
            keyword_analysis = await self._analyze_keywords(article)

            # 2. تحليل المحتوى
            content_analysis = self._analyze_content_seo(article)

            # 3. تحليل الروابط الداخلية
            linking_analysis = await self.link_optimizer.analyze_and_optimize_internal_links(article_id)

            # 4. تحليل تقني
            technical_analysis = self._analyze_technical_seo(article)

            # 5. حساب النقاط الإجمالية
            overall_score = self._calculate_overall_seo_score(
                keyword_analysis, content_analysis, linking_analysis, technical_analysis
            )

            # 6. إنشاء التوصيات
            recommendations = self._generate_seo_recommendations(
                keyword_analysis, content_analysis, linking_analysis, technical_analysis
            )

            result = {
                'article_id': article_id,
                'keyword_analysis': keyword_analysis,
                'content_analysis': content_analysis,
                'linking_analysis': linking_analysis,
                'technical_analysis': technical_analysis,
                'overall_score': overall_score,
                'recommendations': recommendations,
                'analysis_date': datetime.now().isoformat()
            }

            # حفظ التحليل
            self._save_seo_analysis(article_id, result)

            logger.info(f"✅ تم تحليل SEO للمقال {article_id} - النقاط: {overall_score:.1f}")

            return result

        except Exception as e:
            logger.error(f"❌ فشل في تحليل SEO الشامل للمقال {article_id}", e)
            return {}

    async def _analyze_keywords(self, article: Dict) -> Dict:
        """تحليل الكلمات المفتاحية"""
        try:
            title = article.get('title', '')
            content = article.get('content', '') or ''
            current_keywords = article.get('keywords', '') or ''

            # الحصول على كلمات مفتاحية رائجة
            trending_keywords = await self.keyword_tool.get_trending_keywords(title)

            # تحليل الكلمات الحالية
            current_keyword_list = [k.strip() for k in current_keywords.split(',') if k.strip()]

            # حساب كثافة الكلمات المفتاحية
            keyword_density = self._calculate_keyword_density(content, current_keyword_list)

            # اقتراح كلمات جديدة
            suggested_keywords = self._suggest_new_keywords(trending_keywords, current_keyword_list)

            return {
                'current_keywords': current_keyword_list,
                'trending_keywords': trending_keywords[:10],
                'suggested_keywords': suggested_keywords,
                'keyword_density': keyword_density,
                'optimization_score': self._calculate_keyword_score(keyword_density, len(current_keyword_list))
            }

        except Exception as e:
            logger.error("❌ فشل في تحليل الكلمات المفتاحية", e)
            return {}

    def _calculate_keyword_density(self, content: str, keywords: List[str]) -> Dict:
        """حساب كثافة الكلمات المفتاحية"""
        try:
            if not content or not keywords:
                return {}

            content_lower = content.lower()
            total_words = len(content.split())

            density_results = {}

            for keyword in keywords:
                keyword_lower = keyword.lower().strip()
                if keyword_lower:
                    count = content_lower.count(keyword_lower)
                    density = (count / total_words) * 100 if total_words > 0 else 0
                    density_results[keyword] = {
                        'count': count,
                        'density': round(density, 2)
                    }

            return density_results

        except Exception as e:
            logger.error("❌ فشل في حساب كثافة الكلمات المفتاحية", e)
            return {}

    def _suggest_new_keywords(self, trending_keywords: List[Dict], current_keywords: List[str]) -> List[Dict]:
        """اقتراح كلمات مفتاحية جديدة"""
        try:
            current_set = set(k.lower() for k in current_keywords)
            suggestions = []

            for trending in trending_keywords:
                keyword = trending.get('keyword', '').lower()
                if keyword and keyword not in current_set:
                    suggestions.append({
                        'keyword': trending.get('keyword', ''),
                        'search_volume': trending.get('search_volume', 0),
                        'competition': trending.get('competition', 'medium'),
                        'recommendation_reason': self._get_keyword_recommendation_reason(trending)
                    })

            return suggestions[:8]  # أفضل 8 اقتراحات

        except Exception as e:
            logger.error("❌ فشل في اقتراح كلمات مفتاحية جديدة", e)
            return []

    def _get_keyword_recommendation_reason(self, keyword_data: Dict) -> str:
        """الحصول على سبب اقتراح الكلمة المفتاحية"""
        try:
            volume = keyword_data.get('search_volume', 0)
            competition = keyword_data.get('competition', 'medium')
            trend = keyword_data.get('trend', 'stable')

            if volume > 10000 and competition == 'low':
                return "حجم بحث عالي ومنافسة منخفضة"
            elif trend == 'rising':
                return "كلمة رائجة ومتزايدة"
            elif competition == 'low':
                return "منافسة منخفضة"
            elif volume > 5000:
                return "حجم بحث جيد"
            else:
                return "كلمة ذات صلة"

        except Exception as e:
            return "كلمة مقترحة"

    def _calculate_keyword_score(self, keyword_density: Dict, keyword_count: int) -> float:
        """حساب نقاط الكلمات المفتاحية"""
        try:
            score = 0.0

            # نقاط عدد الكلمات المفتاحية (40%)
            if 5 <= keyword_count <= 10:
                score += 40
            elif 3 <= keyword_count <= 12:
                score += 30
            elif keyword_count > 0:
                score += 20

            # نقاط كثافة الكلمات (60%)
            if keyword_density:
                densities = [data['density'] for data in keyword_density.values()]
                avg_density = sum(densities) / len(densities)

                if 1 <= avg_density <= 3:  # الكثافة المثالية
                    score += 60
                elif 0.5 <= avg_density <= 5:
                    score += 40
                elif avg_density > 0:
                    score += 20

            return min(100.0, score)

        except Exception as e:
            logger.error("❌ فشل في حساب نقاط الكلمات المفتاحية", e)
            return 0.0

    def _analyze_content_seo(self, article: Dict) -> Dict:
        """تحليل SEO المحتوى"""
        try:
            title = article.get('title', '')
            content = article.get('content', '') or ''

            # تحليل العنوان
            title_analysis = {
                'length': len(title),
                'optimal_length': 30 <= len(title) <= 60,
                'has_keywords': self._title_has_keywords(title, article.get('keywords', '')),
                'readability': self._analyze_title_readability(title)
            }

            # تحليل المحتوى
            content_analysis = {
                'word_count': len(content.split()) if content else 0,
                'optimal_length': len(content.split()) >= 300 if content else False,
                'readability_score': self._calculate_readability_score(content),
                'structure_score': self._analyze_content_structure(content),
                'has_headings': self._has_proper_headings(content)
            }

            # حساب النقاط الإجمالية
            content_score = self._calculate_content_seo_score(title_analysis, content_analysis)

            return {
                'title_analysis': title_analysis,
                'content_analysis': content_analysis,
                'optimization_score': content_score
            }

        except Exception as e:
            logger.error("❌ فشل في تحليل SEO المحتوى", e)
            return {}

    def _title_has_keywords(self, title: str, keywords: str) -> bool:
        """فحص وجود كلمات مفتاحية في العنوان"""
        try:
            if not title or not keywords:
                return False

            title_lower = title.lower()
            keyword_list = [k.strip().lower() for k in keywords.split(',') if k.strip()]

            return any(keyword in title_lower for keyword in keyword_list)

        except Exception as e:
            return False

    def _analyze_title_readability(self, title: str) -> Dict:
        """تحليل قابلية قراءة العنوان"""
        try:
            return {
                'has_numbers': any(char.isdigit() for char in title),
                'has_emotional_words': any(word in title.lower() for word in ['أفضل', 'جديد', 'حصري', 'مذهل', 'رائع']),
                'has_action_words': any(word in title.lower() for word in ['اكتشف', 'تعلم', 'احصل', 'شاهد']),
                'clarity_score': min(100, max(0, 100 - len(title.split()) * 5))  # كلما قل عدد الكلمات، زادت الوضوح
            }

        except Exception as e:
            logger.error("❌ فشل في تحليل قابلية قراءة العنوان", e)
            return {}

    def _calculate_readability_score(self, content: str) -> float:
        """حساب نقاط قابلية القراءة"""
        try:
            if not content:
                return 0.0

            sentences = content.split('.')
            words = content.split()

            if not sentences or not words:
                return 0.0

            avg_sentence_length = len(words) / len(sentences)

            # نقاط بناءً على طول الجملة المتوسط
            if 10 <= avg_sentence_length <= 20:
                return 100.0
            elif 8 <= avg_sentence_length <= 25:
                return 80.0
            elif 5 <= avg_sentence_length <= 30:
                return 60.0
            else:
                return 40.0

        except Exception as e:
            logger.error("❌ فشل في حساب نقاط قابلية القراءة", e)
            return 0.0

    def _analyze_content_structure(self, content: str) -> float:
        """تحليل بنية المحتوى"""
        try:
            if not content:
                return 0.0

            score = 0.0

            # فحص وجود فقرات
            paragraphs = content.split('\n\n')
            if len(paragraphs) >= 3:
                score += 25

            # فحص وجود قوائم
            if '•' in content or '-' in content or any(line.strip().startswith(str(i)) for i in range(1, 10) for line in content.split('\n')):
                score += 25

            # فحص التنوع في طول الفقرات
            if paragraphs:
                paragraph_lengths = [len(p.split()) for p in paragraphs if p.strip()]
                if paragraph_lengths:
                    length_variance = max(paragraph_lengths) - min(paragraph_lengths)
                    if length_variance > 10:
                        score += 25

            # فحص وجود كلمات انتقالية
            transition_words = ['أولاً', 'ثانياً', 'بالإضافة', 'علاوة على ذلك', 'في النهاية', 'أخيراً']
            if any(word in content for word in transition_words):
                score += 25

            return score

        except Exception as e:
            logger.error("❌ فشل في تحليل بنية المحتوى", e)
            return 0.0

    def _has_proper_headings(self, content: str) -> bool:
        """فحص وجود عناوين فرعية مناسبة"""
        try:
            if not content:
                return False

            # البحث عن عناوين فرعية (نص يبدأ بـ # أو نص منفصل بخطوط)
            lines = content.split('\n')

            heading_indicators = ['#', '##', '###', '====', '----']

            for line in lines:
                line = line.strip()
                if any(line.startswith(indicator) for indicator in heading_indicators):
                    return True

                # فحص العناوين المحتملة (خطوط قصيرة ومميزة)
                if len(line.split()) <= 8 and len(line) > 10 and ':' not in line and '.' not in line:
                    return True

            return False

        except Exception as e:
            logger.error("❌ فشل في فحص العناوين الفرعية", e)
            return False

    def _calculate_content_seo_score(self, title_analysis: Dict, content_analysis: Dict) -> float:
        """حساب نقاط SEO المحتوى"""
        try:
            score = 0.0

            # نقاط العنوان (30%)
            if title_analysis.get('optimal_length', False):
                score += 10
            if title_analysis.get('has_keywords', False):
                score += 10
            if title_analysis.get('readability', {}).get('clarity_score', 0) > 70:
                score += 10

            # نقاط المحتوى (70%)
            if content_analysis.get('optimal_length', False):
                score += 20

            readability = content_analysis.get('readability_score', 0)
            score += (readability / 100) * 20

            structure = content_analysis.get('structure_score', 0)
            score += (structure / 100) * 15

            if content_analysis.get('has_headings', False):
                score += 15

            return min(100.0, score)

        except Exception as e:
            logger.error("❌ فشل في حساب نقاط SEO المحتوى", e)
            return 0.0

    def _analyze_technical_seo(self, article: Dict) -> Dict:
        """تحليل SEO التقني"""
        try:
            title = article.get('title', '')
            content = article.get('content', '') or ''

            # تحليل تقني أساسي
            technical_analysis = {
                'title_tag_optimized': len(title) > 0,
                'meta_description_length': min(160, len(content[:160])) if content else 0,
                'content_uniqueness': self._estimate_content_uniqueness(content),
                'mobile_friendly_score': 85.0,  # افتراضي
                'loading_speed_score': 80.0,    # افتراضي
                'schema_markup': False           # يمكن تطويره لاحقاً
            }

            # حساب النقاط التقنية
            technical_score = self._calculate_technical_score(technical_analysis)

            return {
                'analysis': technical_analysis,
                'optimization_score': technical_score
            }

        except Exception as e:
            logger.error("❌ فشل في تحليل SEO التقني", e)
            return {}

    def _estimate_content_uniqueness(self, content: str) -> float:
        """تقدير تفرد المحتوى"""
        try:
            if not content:
                return 0.0

            # تحليل بسيط لتقدير التفرد
            words = content.split()
            unique_words = set(words)

            if len(words) > 0:
                uniqueness_ratio = len(unique_words) / len(words)
                return min(100.0, uniqueness_ratio * 100)

            return 0.0

        except Exception as e:
            logger.error("❌ فشل في تقدير تفرد المحتوى", e)
            return 0.0

    def _calculate_technical_score(self, technical_analysis: Dict) -> float:
        """حساب النقاط التقنية"""
        try:
            score = 0.0

            if technical_analysis.get('title_tag_optimized', False):
                score += 20

            meta_desc_length = technical_analysis.get('meta_description_length', 0)
            if 120 <= meta_desc_length <= 160:
                score += 20
            elif meta_desc_length > 0:
                score += 10

            uniqueness = technical_analysis.get('content_uniqueness', 0)
            score += (uniqueness / 100) * 20

            mobile_score = technical_analysis.get('mobile_friendly_score', 0)
            score += (mobile_score / 100) * 20

            speed_score = technical_analysis.get('loading_speed_score', 0)
            score += (speed_score / 100) * 20

            return min(100.0, score)

        except Exception as e:
            logger.error("❌ فشل في حساب النقاط التقنية", e)
            return 0.0

    def _calculate_overall_seo_score(self, keyword_analysis: Dict, content_analysis: Dict,
                                   linking_analysis: Dict, technical_analysis: Dict) -> float:
        """حساب النقاط الإجمالية لـ SEO"""
        try:
            keyword_score = keyword_analysis.get('optimization_score', 0) * 0.3
            content_score = content_analysis.get('optimization_score', 0) * 0.3
            linking_score = linking_analysis.get('optimization_score', 0) * 0.2
            technical_score = technical_analysis.get('optimization_score', 0) * 0.2

            return keyword_score + content_score + linking_score + technical_score

        except Exception as e:
            logger.error("❌ فشل في حساب النقاط الإجمالية لـ SEO", e)
            return 0.0

    def _generate_seo_recommendations(self, keyword_analysis: Dict, content_analysis: Dict,
                                    linking_analysis: Dict, technical_analysis: Dict) -> List[str]:
        """إنشاء توصيات SEO"""
        try:
            recommendations = []

            # توصيات الكلمات المفتاحية
            if keyword_analysis.get('optimization_score', 0) < 70:
                recommendations.append("تحسين الكلمات المفتاحية وزيادة كثافتها في المحتوى")

            # توصيات المحتوى
            if content_analysis.get('optimization_score', 0) < 70:
                recommendations.append("تحسين بنية المحتوى وإضافة عناوين فرعية")

            # توصيات الربط الداخلي
            if linking_analysis.get('optimization_score', 0) < 70:
                recommendations.append("إضافة المزيد من الروابط الداخلية ذات الصلة")

            # توصيات تقنية
            if technical_analysis.get('optimization_score', 0) < 70:
                recommendations.append("تحسين الجوانب التقنية مثل سرعة التحميل والتوافق مع الموبايل")

            return recommendations

        except Exception as e:
            logger.error("❌ فشل في إنشاء توصيات SEO", e)
            return []

    def _save_seo_analysis(self, article_id: int, analysis: Dict):
        """حفظ تحليل SEO"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT OR REPLACE INTO seo_analysis
                    (article_id, keyword_optimization_score, content_optimization_score,
                     technical_seo_score, internal_linking_score, overall_seo_score, recommendations)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    article_id,
                    analysis.get('keyword_analysis', {}).get('optimization_score', 0),
                    analysis.get('content_analysis', {}).get('optimization_score', 0),
                    analysis.get('technical_analysis', {}).get('optimization_score', 0),
                    analysis.get('linking_analysis', {}).get('optimization_score', 0),
                    analysis.get('overall_score', 0),
                    json.dumps(analysis.get('recommendations', []))
                ))

                conn.commit()

        except Exception as e:
            logger.error("❌ فشل في حفظ تحليل SEO", e)

# إنشاء مثيل عام لنظام SEO المتقدم
advanced_seo_system = AdvancedSEOSystem()
