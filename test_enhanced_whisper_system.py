#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام Whisper المحسن
Comprehensive Test for Enhanced Whisper System
"""

import asyncio
import sys
import os
import time
from datetime import datetime
from typing import Dict, List

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.enhanced_whisper_manager import enhanced_whisper_manager
from modules.logger import logger
from integrate_enhanced_whisper import WhisperIntegrationManager


class EnhancedWhisperTester:
    """أداة اختبار شاملة لنظام Whisper المحسن"""
    
    def __init__(self):
        self.whisper_manager = enhanced_whisper_manager
        self.integration_manager = WhisperIntegrationManager()
        self.test_results = []
        self.start_time = None
        
        # فيديوهات اختبار مختلفة
        self.test_videos = [
            {
                'id': 'bS5yMnHP3w0',
                'title': 'Splitgate 2 Marketing Disaster',
                'expected_language': 'en',
                'description': 'فيديو إنجليزي قصير عن الألعاب'
            },
            {
                'id': 'Dvtswxb51K4',
                'title': 'Test Video 2',
                'expected_language': 'en',
                'description': 'فيديو اختبار ثاني'
            }
        ]
    
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل للنظام"""
        self.start_time = datetime.now()
        logger.info("🧪 بدء اختبار شامل لنظام Whisper المحسن")
        logger.info("=" * 60)
        
        try:
            # الاختبار 1: فحص النظام الأساسي
            await self._test_basic_system()
            
            # الاختبار 2: اختبار الاتصال
            await self._test_connection()
            
            # الاختبار 3: اختبار طرق الرفع المختلفة
            await self._test_upload_methods()
            
            # الاختبار 4: اختبار التكامل
            await self._test_integration()
            
            # الاختبار 5: اختبار الأداء
            await self._test_performance()
            
            # الاختبار 6: اختبار واجهة الويب
            await self._test_web_interface()
            
            # إنشاء تقرير شامل
            await self._generate_final_report()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في الاختبار الشامل: {e}")
            return False
    
    async def _test_basic_system(self):
        """اختبار النظام الأساسي"""
        test_name = "فحص النظام الأساسي"
        logger.info(f"🔍 {test_name}...")
        
        try:
            start_time = time.time()
            
            # فحص توفر النظام
            system_available = hasattr(self.whisper_manager, 'transcribe_audio')
            
            # فحص الإعدادات
            config_valid = bool(
                self.whisper_manager.whisper_api_url and 
                self.whisper_manager.whisper_api_key
            )
            
            # فحص طرق الرفع
            upload_methods_available = len(self.whisper_manager.upload_methods) > 0
            
            end_time = time.time()
            
            success = system_available and config_valid and upload_methods_available
            
            self.test_results.append({
                'test': test_name,
                'status': 'نجح' if success else 'فشل',
                'duration': end_time - start_time,
                'details': {
                    'system_available': system_available,
                    'config_valid': config_valid,
                    'upload_methods_count': len(self.whisper_manager.upload_methods),
                    'api_url': self.whisper_manager.whisper_api_url
                }
            })
            
            if success:
                logger.info(f"✅ {test_name} نجح")
            else:
                logger.error(f"❌ {test_name} فشل")
                
        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
    
    async def _test_connection(self):
        """اختبار الاتصال مع Whisper API"""
        test_name = "اختبار الاتصال"
        logger.info(f"🌐 {test_name}...")
        
        try:
            start_time = time.time()
            
            # اختبار الاتصال
            connection_result = await self.whisper_manager.test_whisper_connection()
            
            end_time = time.time()
            
            success = connection_result.get('success', False)
            
            self.test_results.append({
                'test': test_name,
                'status': 'نجح' if success else 'فشل',
                'duration': end_time - start_time,
                'details': connection_result
            })
            
            if success:
                logger.info(f"✅ {test_name} نجح")
                logger.info(f"   📊 حالة API: {connection_result.get('status', 'غير محدد')}")
            else:
                logger.error(f"❌ {test_name} فشل")
                logger.error(f"   🔍 السبب: {connection_result.get('error', 'غير محدد')}")
                
        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
    
    async def _test_upload_methods(self):
        """اختبار طرق الرفع المختلفة"""
        test_name = "اختبار طرق الرفع"
        logger.info(f"📤 {test_name}...")
        
        try:
            # استخدام فيديو اختبار قصير
            test_video = self.test_videos[0]
            
            # تحميل الصوت أولاً
            video_url = f"https://www.youtube.com/watch?v={test_video['id']}"
            
            import aiohttp
            async with aiohttp.ClientSession() as session:
                audio_data = await self.integration_manager.youtube_analyzer._download_audio_from_video(video_url, session)
            
            if not audio_data:
                logger.error("❌ فشل في تحميل الصوت للاختبار")
                return
            
            # تقليل حجم البيانات للاختبار
            test_audio_data = audio_data[:1024*1024]  # 1MB فقط للاختبار
            
            # اختبار كل طريقة رفع
            method_results = {}
            
            for i, method in enumerate(self.whisper_manager.upload_methods):
                method_name = method.__name__
                logger.info(f"   🔄 اختبار {method_name}...")
                
                try:
                    start_time = time.time()
                    
                    async with aiohttp.ClientSession() as session:
                        result = await method(
                            session, 
                            test_audio_data, 
                            f"test_{i}", 
                            "en"
                        )
                    
                    end_time = time.time()
                    
                    success = result is not None and result.get('success', False)
                    method_results[method_name] = {
                        'success': success,
                        'duration': end_time - start_time,
                        'result': result
                    }
                    
                    if success:
                        logger.info(f"   ✅ {method_name} نجح")
                    else:
                        logger.warning(f"   ⚠️ {method_name} فشل")
                        
                except Exception as method_error:
                    logger.warning(f"   ❌ {method_name} خطأ: {method_error}")
                    method_results[method_name] = {
                        'success': False,
                        'error': str(method_error)
                    }
                
                # انتظار بين الطرق
                await asyncio.sleep(2)
            
            # تقييم النتائج
            successful_methods = sum(1 for r in method_results.values() if r.get('success'))
            total_methods = len(method_results)
            
            self.test_results.append({
                'test': test_name,
                'status': 'نجح' if successful_methods > 0 else 'فشل',
                'details': {
                    'successful_methods': successful_methods,
                    'total_methods': total_methods,
                    'success_rate': (successful_methods / total_methods) * 100,
                    'method_results': method_results
                }
            })
            
            logger.info(f"📊 نتائج طرق الرفع: {successful_methods}/{total_methods} نجح")
            
        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
    
    async def _test_integration(self):
        """اختبار التكامل مع النظام الحالي"""
        test_name = "اختبار التكامل"
        logger.info(f"🔗 {test_name}...")
        
        try:
            start_time = time.time()
            
            # تشغيل اختبار التكامل الشامل
            integration_results = await self.integration_manager.run_full_integration_test()
            
            end_time = time.time()
            
            success = integration_results.get('overall_success', False)
            
            self.test_results.append({
                'test': test_name,
                'status': 'نجح' if success else 'فشل',
                'duration': end_time - start_time,
                'details': integration_results
            })
            
            if success:
                logger.info(f"✅ {test_name} نجح")
            else:
                logger.error(f"❌ {test_name} فشل")
                
        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
    
    async def _test_performance(self):
        """اختبار الأداء"""
        test_name = "اختبار الأداء"
        logger.info(f"⚡ {test_name}...")
        
        try:
            # الحصول على إحصائيات النظام
            stats = self.whisper_manager.get_transcription_stats()
            
            performance_metrics = {
                'total_transcriptions': stats.get('total_transcriptions', 0),
                'success_rate': stats.get('success_rate', 0),
                'average_processing_time': stats.get('average_processing_time', 0),
                'total_words_processed': stats.get('total_words', 0),
                'languages_supported': len(stats.get('languages_detected', []))
            }
            
            # تقييم الأداء
            performance_score = 0
            if performance_metrics['success_rate'] >= 80:
                performance_score += 30
            elif performance_metrics['success_rate'] >= 60:
                performance_score += 20
            elif performance_metrics['success_rate'] >= 40:
                performance_score += 10
            
            if performance_metrics['average_processing_time'] <= 30:
                performance_score += 30
            elif performance_metrics['average_processing_time'] <= 60:
                performance_score += 20
            elif performance_metrics['average_processing_time'] <= 120:
                performance_score += 10
            
            if performance_metrics['total_transcriptions'] > 0:
                performance_score += 20
            
            if performance_metrics['languages_supported'] > 1:
                performance_score += 20
            
            success = performance_score >= 60
            
            self.test_results.append({
                'test': test_name,
                'status': 'نجح' if success else 'فشل',
                'details': {
                    'performance_metrics': performance_metrics,
                    'performance_score': performance_score,
                    'grade': 'ممتاز' if performance_score >= 80 else 'جيد' if performance_score >= 60 else 'مقبول' if performance_score >= 40 else 'ضعيف'
                }
            })
            
            logger.info(f"📊 نقاط الأداء: {performance_score}/100")
            
        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
    
    async def _test_web_interface(self):
        """اختبار واجهة الويب"""
        test_name = "اختبار واجهة الويب"
        logger.info(f"🌐 {test_name}...")
        
        try:
            # فحص توفر ملف واجهة الويب
            web_interface_exists = os.path.exists('whisper_web_interface.py')
            
            # فحص إمكانية استيراد الواجهة
            import_success = False
            try:
                import whisper_web_interface
                import_success = True
            except Exception as import_error:
                logger.warning(f"⚠️ فشل في استيراد واجهة الويب: {import_error}")
            
            success = web_interface_exists and import_success
            
            self.test_results.append({
                'test': test_name,
                'status': 'نجح' if success else 'فشل',
                'details': {
                    'web_interface_exists': web_interface_exists,
                    'import_success': import_success
                }
            })
            
            if success:
                logger.info(f"✅ {test_name} نجح")
                logger.info("   🌐 واجهة الويب متاحة على: http://localhost:5001")
            else:
                logger.warning(f"⚠️ {test_name} فشل جزئياً")
                
        except Exception as e:
            logger.error(f"❌ {test_name} فشل: {e}")
            self.test_results.append({
                'test': test_name,
                'status': 'فشل',
                'error': str(e)
            })
    
    async def _generate_final_report(self):
        """إنشاء تقرير نهائي شامل"""
        logger.info("\n" + "🎤" * 60)
        logger.info("📊 تقرير اختبار نظام Whisper المحسن")
        logger.info("🎤" * 60)
        
        # إحصائيات عامة
        total_tests = len(self.test_results)
        successful_tests = sum(1 for t in self.test_results if t.get('status') == 'نجح')
        failed_tests = total_tests - successful_tests
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds() if self.start_time else 0
        
        logger.info(f"📈 إجمالي الاختبارات: {total_tests}")
        logger.info(f"✅ اختبارات ناجحة: {successful_tests}")
        logger.info(f"❌ اختبارات فاشلة: {failed_tests}")
        logger.info(f"📊 معدل النجاح: {success_rate:.1f}%")
        logger.info(f"⏱️ إجمالي الوقت: {total_duration:.2f} ثانية")
        
        # تفاصيل كل اختبار
        logger.info("\n📋 تفاصيل الاختبارات:")
        for i, test in enumerate(self.test_results, 1):
            status_icon = "✅" if test.get('status') == 'نجح' else "❌"
            logger.info(f"{i}. {status_icon} {test.get('test', 'غير محدد')}: {test.get('status', 'غير محدد')}")
            
            if test.get('duration'):
                logger.info(f"   ⏱️ المدة: {test['duration']:.2f} ثانية")
            
            if test.get('error'):
                logger.info(f"   🔍 الخطأ: {test['error']}")
        
        # التوصيات
        logger.info("\n💡 التوصيات:")
        if success_rate >= 80:
            logger.info("🎉 النظام يعمل بشكل ممتاز!")
            logger.info("✅ جاهز للاستخدام في الإنتاج")
        elif success_rate >= 60:
            logger.info("👍 النظام يعمل بشكل جيد")
            logger.info("🔧 يُنصح بمراجعة الاختبارات الفاشلة")
        elif success_rate >= 40:
            logger.info("⚠️ النظام يحتاج تحسينات")
            logger.info("🔧 يجب إصلاح المشاكل قبل الاستخدام")
        else:
            logger.info("❌ النظام يحتاج مراجعة شاملة")
            logger.info("🚫 غير جاهز للاستخدام")
        
        logger.info("\n🎤" * 60)


async def main():
    """الدالة الرئيسية للاختبار"""
    logger.info("🚀 بدء اختبار نظام Whisper المحسن...")
    
    tester = EnhancedWhisperTester()
    success = await tester.run_comprehensive_test()
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
