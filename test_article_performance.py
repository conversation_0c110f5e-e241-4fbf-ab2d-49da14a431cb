#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تحليل أداء المقالات
"""

import asyncio
import sys
import os
import sqlite3
from datetime import datetime, timedelta

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.article_performance_analyzer import article_performance_analyzer
from modules.logger import logger

def create_test_articles():
    """إنشاء مقالات تجريبية للاختبار"""
    try:
        with sqlite3.connect("data/articles.db") as conn:
            cursor = conn.cursor()
            
            # إنشاء مقالات تجريبية
            test_articles = [
                {
                    'title': 'مراجعة شاملة للعبة Cyberpunk 2077',
                    'content': 'هذا مقال تجريبي عن مراجعة لعبة Cyberpunk 2077. ' * 50,
                    'keywords': 'cyberpunk,gaming,review,rpg',
                    'category': 'مراجعات',
                    'published_at': (datetime.now() - timedelta(days=5)).isoformat(),
                    'view_count': 150,
                    'engagement_score': 75.5
                },
                {
                    'title': 'أخبار عاجلة: إعلان Grand Theft Auto VI',
                    'content': 'خبر عاجل عن إعلان لعبة GTA VI الجديدة. ' * 30,
                    'keywords': 'gta,rockstar,news,announcement',
                    'category': 'أخبار',
                    'published_at': (datetime.now() - timedelta(days=2)).isoformat(),
                    'view_count': 500,
                    'engagement_score': 90.2
                },
                {
                    'title': 'دليل المبتدئين للعبة Minecraft',
                    'content': 'دليل شامل للمبتدئين في لعبة Minecraft. ' * 40,
                    'keywords': 'minecraft,guide,tutorial,beginners',
                    'category': 'أدلة',
                    'published_at': (datetime.now() - timedelta(days=10)).isoformat(),
                    'view_count': 80,
                    'engagement_score': 45.3
                },
                {
                    'title': 'أفضل 10 ألعاب لعام 2025',
                    'content': 'قائمة بأفضل الألعاب المتوقعة في عام 2025. ' * 35,
                    'keywords': 'best games,2025,top games,list',
                    'category': 'قوائم',
                    'published_at': (datetime.now() - timedelta(days=1)).isoformat(),
                    'view_count': 300,
                    'engagement_score': 82.7
                },
                {
                    'title': 'مشكلة في خوادم PlayStation Network',
                    'content': 'تقرير عن مشاكل في خوادم PlayStation Network. ' * 20,
                    'keywords': 'playstation,network,issues,servers',
                    'category': 'أخبار تقنية',
                    'published_at': (datetime.now() - timedelta(days=7)).isoformat(),
                    'view_count': 25,
                    'engagement_score': 30.1
                }
            ]
            
            for article in test_articles:
                cursor.execute('''
                    INSERT OR IGNORE INTO published_articles 
                    (title, content, keywords, category, published_at, view_count, engagement_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    article['title'],
                    article['content'],
                    article['keywords'],
                    article['category'],
                    article['published_at'],
                    article['view_count'],
                    article['engagement_score']
                ))
            
            conn.commit()
            print("✅ تم إنشاء المقالات التجريبية بنجاح")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المقالات التجريبية: {e}")

async def test_performance_analysis():
    """اختبار تحليل الأداء"""
    print("🧪 بدء اختبار تحليل أداء المقالات...")
    
    try:
        # تشغيل تحليل الأداء
        results = await article_performance_analyzer.analyze_all_articles_performance()
        
        if not results:
            print("❌ لم يتم الحصول على نتائج التحليل")
            return False
        
        print(f"✅ تم تحليل {results.get('total_analyzed', 0)} مقال")
        
        # عرض التحليل العام
        overall = results.get('overall_analysis', {})
        if overall:
            print("\n📊 التحليل العام:")
            averages = overall.get('averages', {})
            print(f"   📈 متوسط CTR: {averages.get('ctr', 0):.2f}%")
            print(f"   ⏱️ متوسط وقت القراءة: {averages.get('read_time', 0):.1f} ثانية")
            print(f"   📉 متوسط معدل الارتداد: {averages.get('bounce_rate', 0):.1f}%")
            print(f"   🎪 متوسط نقاط التفاعل: {averages.get('engagement_score', 0):.1f}")
            print(f"   🔍 متوسط نقاط SEO: {averages.get('seo_score', 0):.1f}")
            print(f"   🏥 الصحة العامة: {overall.get('overall_health', 'غير محدد')}")
        
        # عرض أفضل المقالات
        top_articles = overall.get('top_performing', [])
        if top_articles:
            print("\n🏆 أفضل المقالات أداءً:")
            for i, article in enumerate(top_articles[:3], 1):
                print(f"   {i}. {article.get('title', 'غير محدد')[:50]}... (نقاط: {article.get('score', 0):.1f})")
        
        # عرض أسوأ المقالات
        poor_articles = overall.get('poor_performing', [])
        if poor_articles:
            print("\n⚠️ المقالات التي تحتاج تحسين:")
            for i, article in enumerate(poor_articles[:3], 1):
                print(f"   {i}. {article.get('title', 'غير محدد')[:50]}... (نقاط: {article.get('score', 0):.1f})")
        
        # عرض الاتجاهات المحددة
        trends = overall.get('trends_identified', [])
        if trends:
            print("\n📈 الاتجاهات المحددة:")
            for trend in trends:
                print(f"   • {trend}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحليل الأداء: {e}")
        logger.error("❌ فشل اختبار تحليل الأداء", e)
        return False

async def test_individual_analysis():
    """اختبار تحليل مقال فردي"""
    print("\n🔍 اختبار تحليل مقال فردي...")
    
    try:
        # الحصول على مقال تجريبي
        with sqlite3.connect("data/articles.db") as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, title, content, keywords, published_at, view_count, engagement_score
                FROM published_articles 
                LIMIT 1
            ''')
            
            row = cursor.fetchone()
            if not row:
                print("❌ لا توجد مقالات للاختبار")
                return False
            
            article = {
                'id': row[0],
                'title': row[1],
                'content': row[2],
                'keywords': row[3],
                'published_at': row[4],
                'view_count': row[5],
                'engagement_score': row[6]
            }
        
        # تحليل المقال
        performance = await article_performance_analyzer._analyze_single_article_performance(article)
        
        if performance:
            print(f"✅ تم تحليل المقال: {performance.title[:50]}...")
            print(f"   📊 نقاط التفاعل: {performance.engagement_score:.1f}")
            print(f"   🔍 نقاط SEO: {performance.seo_score:.1f}")
            print(f"   📈 CTR: {performance.ctr:.2f}%")
            print(f"   ⏱️ وقت القراءة: {performance.avg_read_time:.1f} ثانية")
            print(f"   📉 معدل الارتداد: {performance.bounce_rate:.1f}%")
            print(f"   🎖️ درجة الأداء: {performance.performance_grade}")
            
            return True
        else:
            print("❌ فشل في تحليل المقال")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحليل المقال الفردي: {e}")
        return False

async def test_recommendations():
    """اختبار توصيات التحسين"""
    print("\n💡 اختبار توصيات التحسين...")
    
    try:
        # إنشاء مقاييس أداء تجريبية
        from modules.article_performance_analyzer import ArticlePerformanceMetrics
        from datetime import datetime
        
        test_metrics = ArticlePerformanceMetrics(
            article_id=1,
            title="مقال تجريبي",
            published_date=datetime.now(),
            views=50,
            clicks=2,
            impressions=200,
            ctr=1.0,  # منخفض
            avg_read_time=30,  # قصير
            bounce_rate=85,  # مرتفع
            social_shares=0,  # لا توجد مشاركات
            comments_count=0,
            engagement_score=25,  # منخفض
            seo_score=40  # منخفض
        )
        
        # إنشاء التوصيات
        recommendations = article_performance_analyzer._generate_improvement_recommendations(test_metrics)
        
        if recommendations:
            print(f"✅ تم إنشاء {len(recommendations)} توصية للتحسين:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. [{rec['priority'].upper()}] {rec['text']}")
                print(f"      القيمة الحالية: {rec['current_value']:.1f} → الهدف: {rec['target_value']:.1f}")
            
            return True
        else:
            print("❌ لم يتم إنشاء توصيات")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التوصيات: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبارات نظام تحليل أداء المقالات")
    print("=" * 60)
    
    # إنشاء مقالات تجريبية
    create_test_articles()
    
    # اختبار تحليل الأداء العام
    performance_success = await test_performance_analysis()
    
    # اختبار تحليل مقال فردي
    individual_success = await test_individual_analysis()
    
    # اختبار توصيات التحسين
    recommendations_success = await test_recommendations()
    
    print("\n" + "=" * 60)
    print("📋 ملخص نتائج الاختبارات:")
    print(f"📊 تحليل الأداء العام: {'✅ نجح' if performance_success else '❌ فشل'}")
    print(f"🔍 تحليل مقال فردي: {'✅ نجح' if individual_success else '❌ فشل'}")
    print(f"💡 توصيات التحسين: {'✅ نجح' if recommendations_success else '❌ فشل'}")
    
    if performance_success and individual_success and recommendations_success:
        print("\n🎉 جميع الاختبارات نجحت! نظام تحليل الأداء جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")

if __name__ == "__main__":
    asyncio.run(main())
