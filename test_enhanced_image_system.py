#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المحسن لإنشاء الصور
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.smart_image_manager import smart_image_manager
from modules.game_image_analyzer import game_image_analyzer
from modules.manual_image_generator import manual_image_generator
from modules.logger import logger

class EnhancedImageSystemTester:
    """فئة اختبار النظام المحسن للصور"""
    
    def __init__(self):
        self.test_articles = [
            {
                'title': 'Mortal Kombat 1 يحصل على تحديث جديد مع شخصيات إضافية',
                'content': 'لعبة Mortal Kombat 1 الشهيرة تحصل على تحديث كبير يتضمن شخصيات جديدة ومراحل قتال إضافية. التحديث يجلب تحسينات في الجرافيك والأداء.',
                'keywords': ['mortal kombat', 'fighting', 'update', 'characters']
            },
            {
                'title': 'Nintendo Switch Pro: تسريبات جديدة حول المواصفات',
                'content': 'تسريبات جديدة تكشف عن مواصفات Nintendo Switch Pro المنتظر، مع تحسينات في الأداء ودعم 4K.',
                'keywords': ['nintendo', 'switch', 'console', 'specs']
            },
            {
                'title': 'FIFA 25 يقدم تجربة كرة قدم محسنة بالذكاء الاصطناعي',
                'content': 'FIFA 25 يستخدم الذكاء الاصطناعي لتحسين تجربة اللعب وجعل المباريات أكثر واقعية.',
                'keywords': ['fifa', 'football', 'ai', 'sports']
            },
            {
                'title': 'Cyberpunk 2077 Expansion يحصل على تقييمات إيجابية',
                'content': 'التوسعة الجديدة للعبة Cyberpunk 2077 تحصل على تقييمات إيجابية من النقاد واللاعبين.',
                'keywords': ['cyberpunk', 'expansion', 'rpg', 'reviews']
            },
            {
                'title': 'أخبار الألعاب: إعلانات جديدة في معرض الألعاب',
                'content': 'معرض الألعاب يشهد إعلانات مثيرة عن ألعاب جديدة ومنصات قادمة.',
                'keywords': ['gaming', 'news', 'announcements', 'expo']
            }
        ]
    
    async def test_game_image_analyzer(self):
        """اختبار محلل صور الألعاب"""
        print("\n🎮 اختبار محلل صور الألعاب...")
        print("=" * 50)
        
        success_count = 0
        
        for i, article in enumerate(self.test_articles, 1):
            try:
                print(f"\n📝 اختبار {i}: {article['title'][:50]}...")
                
                analysis = await game_image_analyzer.analyze_game_for_image_generation(article)
                
                if analysis:
                    print(f"   ✅ نجح التحليل")
                    print(f"   🎯 اللعبة: {analysis.get('game_name', 'غير محدد')}")
                    print(f"   🎨 النوع: {analysis.get('visual_style', {}).get('genre', 'غير محدد')}")
                    print(f"   📊 الثقة: {analysis.get('confidence_score', 0):.1f}")
                    print(f"   💡 Prompt: {analysis.get('enhanced_prompt', '')[:80]}...")
                    success_count += 1
                else:
                    print(f"   ❌ فشل التحليل")
                
            except Exception as e:
                print(f"   ❌ خطأ في الاختبار: {e}")
        
        print(f"\n📊 نتائج اختبار محلل الألعاب: {success_count}/{len(self.test_articles)} نجح")
        return success_count > 0
    
    async def test_manual_image_generator(self):
        """اختبار مولد الصور اليدوية"""
        print("\n🎨 اختبار مولد الصور اليدوية...")
        print("=" * 50)
        
        # تعيين اسم الموقع للاختبار
        manual_image_generator.set_website_name("Gaming News Test")
        
        success_count = 0
        
        for i, article in enumerate(self.test_articles[:3], 1):  # اختبار 3 مقالات فقط
            try:
                print(f"\n📝 اختبار {i}: {article['title'][:50]}...")
                
                start_time = datetime.now()
                result = await manual_image_generator.generate_manual_image(article)
                end_time = datetime.now()
                
                if result:
                    print(f"   ✅ نجح إنشاء الصورة")
                    print(f"   ⏱️ الوقت: {(end_time - start_time).total_seconds():.2f} ثانية")
                    print(f"   📁 الملف: {result.get('filename', 'غير محدد')}")
                    print(f"   🎯 الموضوع: {result.get('theme', 'عام')}")
                    print(f"   📐 الأبعاد: {result.get('width', 0)}x{result.get('height', 0)}")
                    success_count += 1
                else:
                    print(f"   ❌ فشل إنشاء الصورة")
                
            except Exception as e:
                print(f"   ❌ خطأ في الاختبار: {e}")
        
        print(f"\n📊 نتائج اختبار مولد الصور اليدوية: {success_count}/3 نجح")
        return success_count > 0
    
    async def test_smart_image_manager(self):
        """اختبار مدير الصور الذكي"""
        print("\n🧠 اختبار مدير الصور الذكي...")
        print("=" * 50)
        
        success_count = 0
        
        for i, article in enumerate(self.test_articles[:2], 1):  # اختبار مقالين فقط
            try:
                print(f"\n📝 اختبار {i}: {article['title'][:50]}...")
                
                start_time = datetime.now()
                result = await smart_image_manager.generate_smart_image_for_article(article)
                end_time = datetime.now()
                
                if result:
                    print(f"   ✅ نجح إنشاء الصورة الذكية")
                    print(f"   ⏱️ الوقت: {(end_time - start_time).total_seconds():.2f} ثانية")
                    print(f"   🔗 الرابط: {result.get('url', 'غير محدد')[:60]}...")
                    print(f"   🏷️ المصدر: {result.get('source', 'غير محدد')}")
                    print(f"   🎨 الطريقة: {result.get('generation_method', 'غير محدد')}")
                    
                    # فحص إذا كانت صورة يدوية (بديل)
                    if result.get('fallback_method') == 'manual_generation':
                        print(f"   🔄 تم استخدام النظام اليدوي كبديل")
                    
                    success_count += 1
                else:
                    print(f"   ❌ فشل إنشاء الصورة")
                
            except Exception as e:
                print(f"   ❌ خطأ في الاختبار: {e}")
        
        # عرض الإحصائيات
        stats = smart_image_manager.get_daily_stats()
        print(f"\n📊 إحصائيات مدير الصور الذكي:")
        print(f"   📸 صور منشأة: {stats['images_generated']}/{stats['policy']['max_daily_generations']}")
        print(f"   📦 معدل التخزين المؤقت: {stats['cache_hit_rate']:.1f}%")
        print(f"   🔄 معدل البديل اليدوي: {stats['manual_fallback_rate']:.1f}%")
        print(f"   🎮 معدل نجاح تحليل الألعاب: {stats['game_analysis_success_rate']:.1f}%")
        
        print(f"\n📊 نتائج اختبار مدير الصور الذكي: {success_count}/2 نجح")
        return success_count > 0
    
    async def test_language_support(self):
        """اختبار دعم اللغات"""
        print("\n🌐 اختبار دعم اللغات...")
        print("=" * 50)
        
        # مقالات بلغات مختلفة
        multilingual_articles = [
            {
                'title': 'Call of Duty: Modern Warfare III Gets New Update',
                'content': 'The latest Call of Duty game receives a major update with new maps and weapons.',
                'keywords': ['call of duty', 'fps', 'update']
            },
            {
                'title': 'لعبة فورتنايت تحصل على موسم جديد مع خريطة محدثة',
                'content': 'فورتنايت تطلق موسماً جديداً يتضمن خريطة محدثة وأسلحة جديدة ومهام مثيرة.',
                'keywords': ['فورتنايت', 'باتل رويال', 'موسم جديد']
            }
        ]
        
        success_count = 0
        
        for i, article in enumerate(multilingual_articles, 1):
            try:
                print(f"\n📝 اختبار لغة {i}: {article['title'][:50]}...")
                
                # تحديد اللغة
                language = manual_image_generator.detect_text_language(article['title'])
                print(f"   🔤 اللغة المكتشفة: {language}")
                
                # تحسين النص
                enhanced_title = manual_image_generator.enhance_text_for_display(article['title'], language)
                print(f"   ✨ النص المحسن: {enhanced_title}")
                
                # إنشاء صورة
                result = await manual_image_generator.generate_manual_image(article)
                
                if result:
                    print(f"   ✅ نجح إنشاء الصورة متعددة اللغات")
                    success_count += 1
                else:
                    print(f"   ❌ فشل إنشاء الصورة")
                
            except Exception as e:
                print(f"   ❌ خطأ في الاختبار: {e}")
        
        print(f"\n📊 نتائج اختبار دعم اللغات: {success_count}/{len(multilingual_articles)} نجح")
        return success_count > 0
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار النظام المحسن لإنشاء الصور")
        print("=" * 60)
        
        test_results = []
        
        # اختبار محلل الألعاب
        result1 = await self.test_game_image_analyzer()
        test_results.append(("محلل صور الألعاب", result1))
        
        # اختبار مولد الصور اليدوية
        result2 = await self.test_manual_image_generator()
        test_results.append(("مولد الصور اليدوية", result2))
        
        # اختبار مدير الصور الذكي
        result3 = await self.test_smart_image_manager()
        test_results.append(("مدير الصور الذكي", result3))
        
        # اختبار دعم اللغات
        result4 = await self.test_language_support()
        test_results.append(("دعم اللغات", result4))
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("📋 ملخص نتائج الاختبارات:")
        print("=" * 60)
        
        passed_tests = 0
        for test_name, result in test_results:
            status = "✅ نجح" if result else "❌ فشل"
            print(f"   {test_name}: {status}")
            if result:
                passed_tests += 1
        
        print(f"\n📊 النتيجة الإجمالية: {passed_tests}/{len(test_results)} اختبار نجح")
        
        if passed_tests == len(test_results):
            print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        elif passed_tests >= len(test_results) // 2:
            print("⚠️ معظم الاختبارات نجحت، النظام يعمل بشكل جيد مع بعض المشاكل البسيطة.")
        else:
            print("❌ عدة اختبارات فشلت، يحتاج النظام إلى مراجعة.")
        
        return passed_tests == len(test_results)

async def main():
    """الدالة الرئيسية"""
    tester = EnhancedImageSystemTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        return 0
    else:
        print("\n❌ بعض الاختبارات فشلت!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبارات: {e}")
        sys.exit(1)
