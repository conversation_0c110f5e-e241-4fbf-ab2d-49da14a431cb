#!/usr/bin/env python3
"""
أداة اختبار حلول المشاكل التقنية
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

def test_technical_fixes():
    """اختبار حلول المشاكل التقنية"""
    print("🧪 بدء اختبار حلول المشاكل التقنية...")
    
    success_count = 0
    total_tests = 4
    
    # 1. اختبار كاشف الألعاب المحسن
    print("\n🎮 اختبار كاشف الألعاب المحسن...")
    try:
        from modules.enhanced_game_detector import enhanced_game_detector
        
        test_content = "في هذا المقال سنتحدث عن لعبة Cyberpunk 2077 الجديدة وتحديثات Elden Ring"
        games = enhanced_game_detector.detect_games_in_content("أخبار الألعاب", test_content)
        
        if games and len(games) > 0:
            print(f"✅ تم كشف {len(games)} لعبة")
            for game in games:
                print(f"  - {game['name']} (ثقة: {game['confidence']:.2f})")
            success_count += 1
        else:
            print("❌ لم يتم كشف أي ألعاب")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار كاشف الألعاب: {e}")
    
    # 2. اختبار مراقب المشاكل التقنية
    print("\n🔧 اختبار مراقب المشاكل التقنية...")
    try:
        from modules.technical_issues_monitor import technical_monitor
        
        health_report = technical_monitor.monitor_system_health()
        
        if health_report and 'overall_status' in health_report:
            print(f"✅ حالة النظام: {health_report['overall_status']}")
            print(f"📊 مقاييس الأداء: {len(health_report.get('performance_metrics', {}))}")
            success_count += 1
        else:
            print("❌ فشل في مراقبة النظام")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار مراقب النظام: {e}")
    
    # 3. اختبار YouTube transcript API
    print("\n📺 اختبار YouTube transcript API...")
    try:
        from youtube_transcript_api import YouTubeTranscriptApi
        
        # اختبار بسيط
        test_video_id = "dQw4w9WgXcQ"
        transcript = YouTubeTranscriptApi.get_transcript(test_video_id, languages=['en'])
        
        if transcript and len(transcript) > 0:
            print(f"✅ تم استخراج {len(transcript)} جملة من الفيديو")
            success_count += 1
        else:
            print("❌ لم يتم استخراج أي نص")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار YouTube transcript: {e}")
    
    # 4. اختبار الطرق الاحتياطية
    print("\n🔄 اختبار الطرق الاحتياطية...")
    try:
        # محاولة استيراد المكونات الاحتياطية
        from modules.youtube_analyzer import YouTubeAnalyzer
        
        analyzer = YouTubeAnalyzer()
        
        # فحص وجود الطرق المحسنة
        if hasattr(analyzer, '_enhanced_fallback_extraction'):
            print("✅ الطرق الاحتياطية المحسنة متوفرة")
            success_count += 1
        else:
            print("⚠️ الطرق الاحتياطية المحسنة غير متوفرة")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الطرق الاحتياطية: {e}")
    
    # النتيجة النهائية
    print(f"\n🎯 نتائج الاختبار: {success_count}/{total_tests} اختبار نجح")
    
    if success_count == total_tests:
        print("✅ جميع الحلول التقنية تعمل بشكل ممتاز!")
        return True
    elif success_count >= total_tests * 0.75:
        print("✅ معظم الحلول التقنية تعمل بشكل جيد!")
        return True
    else:
        print("⚠️ بعض الحلول التقنية تحتاج مراجعة")
        return False

if __name__ == "__main__":
    success = test_technical_fixes()
    if success:
        print("\n🚀 النظام جاهز للعمل!")
    else:
        print("\n📋 يرجى مراجعة المشاكل المذكورة أعلاه")
