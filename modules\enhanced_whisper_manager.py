#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام Whisper محسن ومتوافق مع الاستضافة المجانية
Enhanced Whisper Manager for Free Hosting Compatibility
"""

import asyncio
import aiohttp
import json
import tempfile
import os
import time
from typing import Optional, Dict, List, Any
from datetime import datetime

from .logger import logger
from config.settings import BotConfig


class EnhancedWhisperManager:
    """مدير Whisper محسن للتعامل مع الاستضافة المجانية"""
    
    def __init__(self):
        self.config = BotConfig()
        self.whisper_api_url = self.config.WHISPER_API_URL
        self.whisper_api_key = self.config.WHISPER_API_KEY
        self.hf_token = self.config.HF_TOKEN
        
        # إعدادات محسنة للاستضافة المجانية
        self.max_file_size_mb = 20  # حد أقصى 20MB للاستضافة المجانية
        self.timeout_seconds = 120  # 2 دقيقة timeout
        self.max_retries = 3
        
        # قائمة بالطرق البديلة للرفع
        self.upload_methods = [
            self._upload_method_standard,
            self._upload_method_multipart,
            self._upload_method_base64,
            self._upload_method_chunks
        ]
        
        # إحصائيات
        self.transcription_history = []
        
    async def transcribe_audio(self, audio_data: bytes, video_id: str, 
                             video_title: str = "", detected_language: str = "auto") -> Optional[Dict]:
        """تحويل الصوت إلى نص مع معالجة شاملة للأخطاء"""
        
        start_time = time.time()
        logger.info(f"🎤 بدء تحويل الصوت إلى نص - الفيديو: {video_id}")
        logger.info(f"📊 حجم الملف: {len(audio_data) / 1024 / 1024:.2f}MB")
        
        # فحص حجم الملف
        if len(audio_data) > self.max_file_size_mb * 1024 * 1024:
            logger.warning(f"⚠️ الملف كبير جداً ({len(audio_data) / 1024 / 1024:.2f}MB)")
            audio_data = await self._compress_audio(audio_data)
            
        # محاولة التحويل بطرق مختلفة
        result = None
        for attempt, method in enumerate(self.upload_methods, 1):
            try:
                logger.info(f"🔄 المحاولة {attempt}/{len(self.upload_methods)} - {method.__name__}")
                
                async with aiohttp.ClientSession() as session:
                    result = await method(session, audio_data, video_id, detected_language)
                    
                if result and result.get('success'):
                    logger.info(f"✅ نجح التحويل في المحاولة {attempt}")
                    break
                    
            except Exception as e:
                logger.warning(f"⚠️ فشلت المحاولة {attempt}: {e}")
                if attempt < len(self.upload_methods):
                    await asyncio.sleep(2)  # انتظار قبل المحاولة التالية
                    
        # إعداد النتيجة النهائية
        if result and result.get('success'):
            end_time = time.time()
            processing_time = end_time - start_time
            
            final_result = {
                'success': True,
                'text': result.get('text', ''),
                'language': result.get('language', detected_language),
                'word_count': len(result.get('text', '').split()),
                'char_count': len(result.get('text', '')),
                'processing_time': processing_time,
                'video_id': video_id,
                'video_title': video_title,
                'timestamp': datetime.now().isoformat(),
                'file_size_mb': len(audio_data) / 1024 / 1024,
                'method_used': getattr(result, 'method_used', 'unknown')
            }
            
            # حفظ في التاريخ
            self.transcription_history.append(final_result)
            
            # عرض تفاصيل النتيجة
            self._display_transcription_result(final_result)
            
            return final_result
        else:
            logger.error("❌ فشل في جميع طرق التحويل")
            return {
                'success': False,
                'error': 'فشل في جميع طرق التحويل',
                'video_id': video_id,
                'timestamp': datetime.now().isoformat()
            }
    
    async def _upload_method_standard(self, session: aiohttp.ClientSession, 
                                    audio_data: bytes, video_id: str, 
                                    detected_language: str) -> Optional[Dict]:
        """الطريقة القياسية للرفع"""
        try:
            # إنشاء FormData
            data = aiohttp.FormData()
            data.add_field(
                'audio',  # استخدام 'audio' بدلاً من 'file'
                audio_data,
                filename=f'{video_id}.mp3',
                content_type='audio/mpeg'
            )
            
            headers = {
                'X-API-Key': self.whisper_api_key,
                'User-Agent': 'GamingNewsBot/2.0',
                'Accept': 'application/json'
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout_seconds)
            
            async with session.post(
                self.whisper_api_url,
                data=data,
                headers=headers,
                timeout=timeout
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    if result.get('success'):
                        text = result.get('data', {}).get('text', '')
                        if len(text.strip()) > 10:
                            return {
                                'success': True,
                                'text': text.strip(),
                                'language': result.get('data', {}).get('language', detected_language),
                                'method_used': 'standard'
                            }
                    else:
                        logger.warning(f"⚠️ خطأ من API: {result.get('error', 'غير محدد')}")
                        
                else:
                    error_text = await response.text()
                    logger.warning(f"⚠️ خطأ HTTP {response.status}: {error_text}")
                    
        except Exception as e:
            logger.warning(f"⚠️ خطأ في الطريقة القياسية: {e}")
            
        return None
    
    async def _upload_method_multipart(self, session: aiohttp.ClientSession, 
                                     audio_data: bytes, video_id: str, 
                                     detected_language: str) -> Optional[Dict]:
        """طريقة multipart محسنة"""
        try:
            # حفظ الملف مؤقتاً
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as tmp_file:
                tmp_file.write(audio_data)
                tmp_file.flush()
                
                # إنشاء FormData مع الملف
                data = aiohttp.FormData()
                with open(tmp_file.name, 'rb') as f:
                    data.add_field(
                        'audio',
                        f,
                        filename=f'{video_id}.mp3',
                        content_type='audio/mpeg'
                    )
                    
                    headers = {
                        'X-API-Key': self.whisper_api_key,
                        'User-Agent': 'GamingNewsBot/2.0'
                    }
                    
                    timeout = aiohttp.ClientTimeout(total=self.timeout_seconds)
                    
                    async with session.post(
                        self.whisper_api_url,
                        data=data,
                        headers=headers,
                        timeout=timeout
                    ) as response:
                        
                        if response.status == 200:
                            result = await response.json()
                            
                            if result.get('success'):
                                text = result.get('data', {}).get('text', '')
                                if len(text.strip()) > 10:
                                    return {
                                        'success': True,
                                        'text': text.strip(),
                                        'language': result.get('data', {}).get('language', detected_language),
                                        'method_used': 'multipart'
                                    }
                
                # حذف الملف المؤقت
                os.unlink(tmp_file.name)
                
        except Exception as e:
            logger.warning(f"⚠️ خطأ في طريقة multipart: {e}")
            
        return None
    
    async def _upload_method_base64(self, session: aiohttp.ClientSession, 
                                  audio_data: bytes, video_id: str, 
                                  detected_language: str) -> Optional[Dict]:
        """طريقة base64 للملفات الصغيرة"""
        try:
            import base64
            
            # تحويل إلى base64
            audio_b64 = base64.b64encode(audio_data).decode('utf-8')
            
            payload = {
                'audio_data': audio_b64,
                'filename': f'{video_id}.mp3',
                'content_type': 'audio/mpeg'
            }
            
            headers = {
                'Content-Type': 'application/json',
                'X-API-Key': self.whisper_api_key,
                'User-Agent': 'GamingNewsBot/2.0'
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout_seconds)
            
            async with session.post(
                self.whisper_api_url,
                json=payload,
                headers=headers,
                timeout=timeout
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    if result.get('success'):
                        text = result.get('data', {}).get('text', '')
                        if len(text.strip()) > 10:
                            return {
                                'success': True,
                                'text': text.strip(),
                                'language': result.get('data', {}).get('language', detected_language),
                                'method_used': 'base64'
                            }
                            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في طريقة base64: {e}")
            
        return None
    
    async def _upload_method_chunks(self, session: aiohttp.ClientSession, 
                                  audio_data: bytes, video_id: str, 
                                  detected_language: str) -> Optional[Dict]:
        """طريقة تقسيم الملف لقطع صغيرة"""
        try:
            # تقسيم الملف إذا كان كبيراً
            chunk_size = 5 * 1024 * 1024  # 5MB لكل قطعة
            
            if len(audio_data) > chunk_size:
                # تقسيم الملف
                chunks = [audio_data[i:i+chunk_size] for i in range(0, len(audio_data), chunk_size)]
                
                # معالجة كل قطعة
                all_text = []
                for i, chunk in enumerate(chunks):
                    logger.info(f"📦 معالجة القطعة {i+1}/{len(chunks)}")
                    
                    result = await self._upload_method_standard(session, chunk, f"{video_id}_chunk_{i}", detected_language)
                    if result and result.get('success'):
                        all_text.append(result.get('text', ''))
                    
                    await asyncio.sleep(1)  # انتظار بين القطع
                
                if all_text:
                    combined_text = ' '.join(all_text)
                    return {
                        'success': True,
                        'text': combined_text,
                        'language': detected_language,
                        'method_used': 'chunks'
                    }
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في طريقة التقسيم: {e}")

        return None

    async def _compress_audio(self, audio_data: bytes) -> bytes:
        """ضغط الملف الصوتي لتقليل الحجم"""
        try:
            logger.info("📦 ضغط الملف الصوتي...")

            # حفظ الملف مؤقتاً
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as tmp_input:
                tmp_input.write(audio_data)
                tmp_input.flush()

                # ملف الإخراج المضغوط
                with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as tmp_output:

                    # استخدام ffmpeg للضغط (إذا كان متوفراً)
                    try:
                        import subprocess

                        cmd = [
                            'ffmpeg', '-i', tmp_input.name,
                            '-acodec', 'mp3',
                            '-ab', '64k',  # bitrate منخفض
                            '-ar', '16000',  # sample rate منخفض
                            '-y',  # overwrite
                            tmp_output.name
                        ]

                        result = subprocess.run(cmd, capture_output=True, text=True)

                        if result.returncode == 0:
                            with open(tmp_output.name, 'rb') as f:
                                compressed_data = f.read()

                            logger.info(f"✅ تم ضغط الملف من {len(audio_data) / 1024 / 1024:.2f}MB إلى {len(compressed_data) / 1024 / 1024:.2f}MB")

                            # حذف الملفات المؤقتة
                            os.unlink(tmp_input.name)
                            os.unlink(tmp_output.name)

                            return compressed_data

                    except Exception as ffmpeg_error:
                        logger.warning(f"⚠️ فشل ffmpeg: {ffmpeg_error}")

                    # طريقة بديلة - تقليل جودة البيانات
                    # (هذا مجرد تقليل حجم البيانات، ليس ضغط حقيقي)
                    step = max(1, len(audio_data) // (10 * 1024 * 1024))  # تقليل إلى 10MB تقريباً
                    reduced_data = audio_data[::step]

                    logger.info(f"✅ تم تقليل حجم الملف من {len(audio_data) / 1024 / 1024:.2f}MB إلى {len(reduced_data) / 1024 / 1024:.2f}MB")

                    # حذف الملفات المؤقتة
                    os.unlink(tmp_input.name)
                    os.unlink(tmp_output.name)

                    return reduced_data

        except Exception as e:
            logger.warning(f"⚠️ فشل في ضغط الملف: {e}")
            return audio_data  # إرجاع الملف الأصلي

    def _display_transcription_result(self, result: Dict):
        """عرض تفاصيل نتيجة التحويل"""
        logger.info("🎤" * 50)
        logger.info("📝 نتيجة تحويل الصوت إلى نص:")
        logger.info(f"   🆔 معرف الفيديو: {result.get('video_id', 'غير محدد')}")
        logger.info(f"   📹 عنوان الفيديو: {result.get('video_title', 'غير محدد')}")
        logger.info(f"   🌍 اللغة المكتشفة: {result.get('language', 'غير محدد')}")
        logger.info(f"   📊 عدد الكلمات: {result.get('word_count', 0)}")
        logger.info(f"   🔤 عدد الأحرف: {result.get('char_count', 0)}")
        logger.info(f"   ⏱️ وقت المعالجة: {result.get('processing_time', 0):.2f} ثانية")
        logger.info(f"   📁 حجم الملف: {result.get('file_size_mb', 0):.2f}MB")
        logger.info(f"   🔧 الطريقة المستخدمة: {result.get('method_used', 'غير محدد')}")

        # عرض عينة من النص
        text = result.get('text', '')
        if text:
            preview = text[:200] + "..." if len(text) > 200 else text
            logger.info(f"   📄 عينة من النص: {preview}")

        logger.info("🎤" * 50)

    def get_transcription_history(self) -> List[Dict]:
        """الحصول على تاريخ التحويلات"""
        return self.transcription_history.copy()

    def get_transcription_stats(self) -> Dict:
        """الحصول على إحصائيات التحويلات"""
        if not self.transcription_history:
            return {
                'total_transcriptions': 0,
                'successful_transcriptions': 0,
                'failed_transcriptions': 0,
                'success_rate': 0,
                'total_processing_time': 0,
                'average_processing_time': 0,
                'total_words': 0,
                'total_characters': 0,
                'languages_detected': []
            }

        successful = [t for t in self.transcription_history if t.get('success')]
        failed = [t for t in self.transcription_history if not t.get('success')]

        total_processing_time = sum(t.get('processing_time', 0) for t in successful)
        total_words = sum(t.get('word_count', 0) for t in successful)
        total_characters = sum(t.get('char_count', 0) for t in successful)

        languages = list(set(t.get('language', '') for t in successful if t.get('language')))

        return {
            'total_transcriptions': len(self.transcription_history),
            'successful_transcriptions': len(successful),
            'failed_transcriptions': len(failed),
            'success_rate': (len(successful) / len(self.transcription_history)) * 100 if self.transcription_history else 0,
            'total_processing_time': total_processing_time,
            'average_processing_time': total_processing_time / len(successful) if successful else 0,
            'total_words': total_words,
            'total_characters': total_characters,
            'languages_detected': languages
        }


    async def test_whisper_connection(self) -> Dict:
        """اختبار الاتصال مع Whisper API"""
        logger.info("🧪 اختبار الاتصال مع Whisper API...")

        try:
            async with aiohttp.ClientSession() as session:
                # اختبار endpoint الصحة
                health_url = self.whisper_api_url.replace('/api/transcribe', '/health')

                headers = {
                    'X-API-Key': self.whisper_api_key,
                    'User-Agent': 'GamingNewsBot/2.0'
                }

                timeout = aiohttp.ClientTimeout(total=30)

                async with session.get(health_url, headers=headers, timeout=timeout) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info("✅ الاتصال مع Whisper API ناجح")
                        return {
                            'success': True,
                            'status': 'healthy',
                            'api_response': result
                        }
                    else:
                        error_text = await response.text()
                        logger.warning(f"⚠️ مشكلة في الاتصال: {response.status} - {error_text}")
                        return {
                            'success': False,
                            'status': 'unhealthy',
                            'error': f"HTTP {response.status}: {error_text}"
                        }

        except Exception as e:
            logger.error(f"❌ فشل في اختبار الاتصال: {e}")
            return {
                'success': False,
                'status': 'error',
                'error': str(e)
            }


# إنشاء مثيل عام للاستخدام
enhanced_whisper_manager = EnhancedWhisperManager()
