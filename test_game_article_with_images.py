#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة بسيطة لاختبار إنشاء مقال عن لعبة معينة مع الصور المرخصة
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.smart_image_manager import smart_image_manager
from modules.licensed_image_manager import licensed_image_manager
from modules.content_generator import ContentGenerator
from modules.logger import logger

class GameArticleTester:
    """أداة اختبار إنشاء مقال عن لعبة مع الصور"""
    
    def __init__(self):
        self.content_generator = ContentGenerator()
        print("🎮 أداة اختبار مقالات الألعاب مع الصور المرخصة")
        print("=" * 60)
    
    async def test_game_article(self, game_name: str):
        """اختبار إنشاء مقال عن لعبة معينة"""
        print(f"\n🎯 اختبار إنشاء مقال عن: {game_name}")
        print("-" * 40)
        
        # 1. إنشاء مقال تجريبي
        test_article = self.create_test_article(game_name)
        
        # 2. اختبار البحث عن صور مرخصة
        await self.test_licensed_images(game_name)
        
        # 3. اختبار Smart Image Manager
        await self.test_smart_image_generation(test_article)
        
        # 4. اختبار إنشاء المقال الكامل
        await self.test_full_article_generation(test_article)
    
    def create_test_article(self, game_name: str) -> dict:
        """إنشاء مقال تجريبي للاختبار"""
        article = {
            'title': f'مراجعة شاملة للعبة {game_name} - تجربة لا تُنسى',
            'content': f'''
            تعتبر لعبة {game_name} واحدة من أبرز الألعاب في الفترة الأخيرة، حيث تقدم تجربة 
            لعب مميزة تجمع بين الإثارة والتشويق. اللعبة تتميز بجرافيك عالي الجودة وقصة 
            مشوقة تأسر اللاعبين من اللحظة الأولى.
            
            تتضمن اللعبة العديد من الميزات المبتكرة التي تجعلها تبرز عن باقي الألعاب في 
            نفس الفئة. من ناحية اللعب، تقدم {game_name} تحديات متنوعة ومستويات صعوبة 
            مختلفة تناسب جميع أنواع اللاعبين.
            
            الرسوميات في اللعبة مذهلة حقاً، مع تفاصيل دقيقة في البيئات والشخصيات. 
            الصوتيات أيضاً على مستوى عالٍ، مما يضيف للتجربة الإجمالية للعبة.
            ''',
            'keywords': [game_name, 'مراجعة', 'ألعاب', 'gaming', 'review'],
            'content_type': 'مراجعة_لعبة',
            'published_date': datetime.now()
        }
        
        print(f"📝 تم إنشاء مقال تجريبي:")
        print(f"   العنوان: {article['title']}")
        print(f"   الكلمات المفتاحية: {', '.join(article['keywords'])}")
        
        return article
    
    async def test_licensed_images(self, game_name: str):
        """اختبار البحث عن صور مرخصة"""
        print(f"\n🔍 البحث عن صور مرخصة لـ {game_name}...")
        
        try:
            # اختبار كل موفر على حدة
            for provider_name, provider in licensed_image_manager.providers.items():
                print(f"\n📋 اختبار {provider_name.upper()}:")
                
                images = await provider.search_game_images(game_name, 2)
                
                if images:
                    print(f"   ✅ تم العثور على {len(images)} صورة")
                    for i, img in enumerate(images, 1):
                        print(f"   📸 الصورة {i}:")
                        print(f"      • المصدر: {img.source}")
                        print(f"      • النوع: {img.image_type}")
                        print(f"      • الترخيص: {img.license_type}")
                        print(f"      • آمنة لأدسنس: {img.safe_for_adsense}")
                        print(f"      • الرابط: {img.url[:80]}...")
                else:
                    print(f"   ⚠️ لم يتم العثور على صور")
                
                # تأخير بين الموفرين
                await asyncio.sleep(1)
            
            # اختبار المدير الرئيسي
            print(f"\n🎯 اختبار المدير الرئيسي:")
            all_images = await licensed_image_manager.get_licensed_images_for_game(game_name, 3)
            
            if all_images:
                print(f"   ✅ المدير الرئيسي وجد {len(all_images)} صورة مرخصة")
                for i, img in enumerate(all_images, 1):
                    print(f"   🏆 أفضل صورة {i}: {img.source} - {img.image_type}")
            else:
                print(f"   ❌ المدير الرئيسي لم يجد صور مرخصة")
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الصور المرخصة: {e}")
    
    async def test_smart_image_generation(self, article: dict):
        """اختبار Smart Image Manager"""
        print(f"\n🧠 اختبار Smart Image Manager...")
        
        try:
            # الحصول على إحصائيات قبل الاختبار
            stats_before = smart_image_manager.get_daily_stats()
            print(f"   📊 الإحصائيات قبل الاختبار:")
            print(f"      • الصور المولدة اليوم: {stats_before['images_generated']}")
            print(f"      • معدل الصور المرخصة: {stats_before.get('licensed_images_rate', 0):.1f}%")
            
            # اختبار إنشاء صورة ذكية
            print(f"\n   🎨 محاولة إنشاء صورة ذكية...")
            image_result = await smart_image_manager.generate_smart_image_for_article(article)
            
            if image_result:
                print(f"   ✅ تم إنشاء صورة بنجاح!")
                print(f"   📸 تفاصيل الصورة:")
                print(f"      • المصدر: {image_result.get('source', 'غير محدد')}")
                print(f"      • طريقة الإنشاء: {image_result.get('generation_method', 'غير محدد')}")
                print(f"      • API المستخدم: {image_result.get('api_used', 'غير محدد')}")
                print(f"      • آمنة لأدسنس: {image_result.get('safe_for_adsense', 'غير محدد')}")
                print(f"      • الرابط: {image_result.get('url', 'غير متوفر')[:80]}...")
                
                # فحص نوع الصورة
                generation_method = image_result.get('generation_method', '')
                if generation_method == 'licensed_official':
                    print(f"   🎯 ممتاز! تم استخدام صورة مرخصة رسمية")
                elif 'pollinations' in generation_method:
                    print(f"   🤖 تم استخدام الذكاء الاصطناعي (Pollinations)")
                elif 'manual' in generation_method:
                    print(f"   ✋ تم استخدام النظام اليدوي")
                else:
                    print(f"   🔄 تم استخدام طريقة أخرى: {generation_method}")
                
            else:
                print(f"   ❌ فشل في إنشاء صورة")
            
            # الحصول على إحصائيات بعد الاختبار
            stats_after = smart_image_manager.get_daily_stats()
            print(f"\n   📊 الإحصائيات بعد الاختبار:")
            print(f"      • الصور المولدة اليوم: {stats_after['images_generated']}")
            print(f"      • معدل الصور المرخصة: {stats_after.get('licensed_images_rate', 0):.1f}%")
            
            # عرض مصادر الصور
            if 'image_sources' in stats_after:
                sources = stats_after['image_sources']
                print(f"   📈 مصادر الصور اليوم:")
                print(f"      • صور مرخصة رسمية: {sources.get('licensed_official', 0)}")
                print(f"      • ذكاء اصطناعي: {sources.get('ai_generated', 0)}")
                print(f"      • يدوية احتياطية: {sources.get('manual_fallback', 0)}")
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار Smart Image Manager: {e}")
    
    async def test_full_article_generation(self, article: dict):
        """اختبار إنشاء المقال الكامل"""
        print(f"\n📰 اختبار إنشاء المقال الكامل...")
        
        try:
            # محاولة إنشاء المقال الكامل
            print(f"   🔄 إنشاء المقال...")
            
            # استخدام مولد المحتوى
            generated_article = self.content_generator.generate_article(
                article, 
                article['content_type'], 
                'سعودي'  # اللهجة
            )
            
            if generated_article and 'error' not in generated_article:
                print(f"   ✅ تم إنشاء المقال بنجاح!")
                print(f"   📋 تفاصيل المقال:")
                print(f"      • العنوان: {generated_article.get('title', 'غير محدد')[:60]}...")
                print(f"      • عدد الكلمات: {len(generated_article.get('content', '').split())}")
                print(f"      • الكلمات المفتاحية: {len(generated_article.get('keywords', []))}")
                
                # فحص الصور في المقال
                if 'image_urls' in generated_article:
                    images = generated_article['image_urls']
                    print(f"      • عدد الصور: {len(images)}")
                    
                    if 'image_metadata' in generated_article:
                        metadata = generated_article['image_metadata']
                        for i, meta in enumerate(metadata, 1):
                            if isinstance(meta, dict):
                                source = meta.get('source', 'غير محدد')
                                method = meta.get('generation_method', 'غير محدد')
                                print(f"        📸 صورة {i}: {source} ({method})")
                
                # فحص جودة المقال
                if 'quality_review' in generated_article:
                    quality = generated_article['quality_review']
                    approved = quality.get('approved', False)
                    print(f"      • جودة المقال: {'✅ مقبول' if approved else '⚠️ يحتاج تحسين'}")
                
            else:
                print(f"   ❌ فشل في إنشاء المقال")
                if generated_article and 'error' in generated_article:
                    print(f"      خطأ: {generated_article['error']}")
                
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء المقال الكامل: {e}")

async def main():
    """الدالة الرئيسية"""
    tester = GameArticleTester()
    
    # قائمة ألعاب للاختبار
    test_games = [
        "The Witcher 3",
        "Cyberpunk 2077", 
        "League of Legends",
        "Valorant",
        "Assassin's Creed Valhalla",
        "Call of Duty",
        "FIFA 2024",
        "Minecraft"
    ]
    
    print("\n🎮 اختر لعبة للاختبار:")
    for i, game in enumerate(test_games, 1):
        print(f"   {i}. {game}")
    print(f"   {len(test_games) + 1}. إدخال اسم لعبة مخصص")
    
    try:
        choice = input("\nأدخل رقم الاختيار: ").strip()
        
        if choice.isdigit():
            choice_num = int(choice)
            if 1 <= choice_num <= len(test_games):
                selected_game = test_games[choice_num - 1]
            elif choice_num == len(test_games) + 1:
                selected_game = input("أدخل اسم اللعبة: ").strip()
                if not selected_game:
                    print("❌ لم يتم إدخال اسم اللعبة")
                    return
            else:
                print("❌ اختيار غير صحيح")
                return
        else:
            print("❌ يرجى إدخال رقم صحيح")
            return
        
        print(f"\n🚀 بدء اختبار اللعبة: {selected_game}")
        await tester.test_game_article(selected_game)
        
        print(f"\n🎉 اكتمل اختبار اللعبة: {selected_game}")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
