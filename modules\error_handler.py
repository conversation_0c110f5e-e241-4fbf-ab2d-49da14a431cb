# نظام إدارة الأخطاء والمراقبة المتقدم
import os
import asyncio
import time
import traceback
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
import json
import threading
from functools import wraps
from .logger import logger
from .database import db
from config.settings import BotConfig

class RetryMechanism:
    """آلية إعادة المحاولة المتقدمة"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
    
    def exponential_backoff(self, attempt: int) -> float:
        """حساب تأخير متزايد أسي"""
        delay = self.base_delay * (2 ** attempt)
        return min(delay, self.max_delay)
    
    async def async_retry(self, func: Callable, *args, **kwargs) -> Any:
        """إعادة المحاولة للدوال غير المتزامنة"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    delay = self.exponential_backoff(attempt - 1)
                    logger.info(f"🔄 إعادة المحاولة {attempt}/{self.max_retries} بعد {delay:.1f} ثانية")
                    await asyncio.sleep(delay)
                
                result = await func(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(f"✅ نجحت إعادة المحاولة {attempt} للدالة {func.__name__}")
                
                return result
                
            except Exception as e:
                last_exception = e
                logger.warning(f"⚠️ فشلت المحاولة {attempt + 1} للدالة {func.__name__}: {str(e)}")
                
                if attempt == self.max_retries:
                    logger.error(f"❌ فشلت جميع المحاولات للدالة {func.__name__}")
                    break
        
        raise last_exception
    
    def sync_retry(self, func: Callable, *args, **kwargs) -> Any:
        """إعادة المحاولة للدوال المتزامنة"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    delay = self.exponential_backoff(attempt - 1)
                    logger.info(f"🔄 إعادة المحاولة {attempt}/{self.max_retries} بعد {delay:.1f} ثانية")
                    time.sleep(delay)
                
                result = func(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(f"✅ نجحت إعادة المحاولة {attempt} للدالة {func.__name__}")
                
                return result
                
            except Exception as e:
                last_exception = e
                logger.warning(f"⚠️ فشلت المحاولة {attempt + 1} للدالة {func.__name__}: {str(e)}")
                
                if attempt == self.max_retries:
                    logger.error(f"❌ فشلت جميع المحاولات للدالة {func.__name__}")
                    break
        
        raise last_exception

def async_retry_decorator(max_retries: int = 3, base_delay: float = 1.0):
    """مُزخرف إعادة المحاولة للدوال غير المتزامنة"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            retry_mechanism = RetryMechanism(max_retries, base_delay)
            return await retry_mechanism.async_retry(func, *args, **kwargs)
        return wrapper
    return decorator

def sync_retry_decorator(max_retries: int = 3, base_delay: float = 1.0):
    """مُزخرف إعادة المحاولة للدوال المتزامنة"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retry_mechanism = RetryMechanism(max_retries, base_delay)
            return retry_mechanism.sync_retry(func, *args, **kwargs)
        return wrapper
    return decorator

class HealthMonitor:
    """مراقب صحة النظام"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.last_health_check = datetime.now()
        self.health_status = {
            'bot_running': True,
            'database_connected': True,
            'apis_responding': True,
            'last_article_processed': None,
            'error_count_24h': 0,
            'uptime_hours': 0.0
        }
        self.error_threshold = 10  # حد الأخطاء لكل 24 ساعة
        self.monitoring_thread = None
        self.is_monitoring = False
    
    def start_monitoring(self):
        """بدء مراقبة النظام"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            logger.info("🔍 تم بدء مراقبة صحة النظام")
    
    def stop_monitoring(self):
        """إيقاف مراقبة النظام"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("🔍 تم إيقاف مراقبة صحة النظام")
    
    def _monitoring_loop(self):
        """حلقة المراقبة الرئيسية"""
        while self.is_monitoring:
            try:
                self._perform_health_check()
                time.sleep(300)  # فحص كل 5 دقائق
            except Exception as e:
                logger.error("❌ خطأ في حلقة المراقبة", e)
                time.sleep(60)  # انتظار أقل في حالة الخطأ
    
    def _perform_health_check(self):
        """إجراء فحص صحة شامل"""
        try:
            current_time = datetime.now()
            
            # حساب وقت التشغيل
            uptime = current_time - self.start_time
            self.health_status['uptime_hours'] = uptime.total_seconds() / 3600
            
            # فحص قاعدة البيانات
            self.health_status['database_connected'] = self._check_database_health()
            
            # فحص عدد الأخطاء في آخر 24 ساعة
            self.health_status['error_count_24h'] = self._get_error_count_24h()
            
            # فحص آخر مقال تم معالجته
            self.health_status['last_article_processed'] = self._get_last_article_time()
            
            # تحديث وقت آخر فحص
            self.last_health_check = current_time
            
            # التحقق من الحالة العامة
            self._check_overall_health()
            
            logger.debug(f"✅ فحص صحة النظام مكتمل: {self.health_status}")
            
        except Exception as e:
            logger.error("❌ فشل في فحص صحة النظام", e)
            self.health_status['bot_running'] = False
    
    def _check_database_health(self) -> bool:
        """فحص صحة قاعدة البيانات"""
        try:
            # محاولة استعلام بسيط
            stats = db.get_stats_summary(1)
            return bool(stats)
        except Exception as e:
            logger.error("❌ فشل في فحص قاعدة البيانات", e)
            return False
    
    def _get_error_count_24h(self) -> int:
        """الحصول على عدد الأخطاء في آخر 24 ساعة"""
        try:
            yesterday = datetime.now() - timedelta(days=1)
            stats = db.get_stats_summary(1)
            return stats.get('total_errors', 0)
        except Exception:
            return 0
    
    def _get_last_article_time(self) -> Optional[datetime]:
        """الحصول على وقت آخر مقال تم معالجته"""
        try:
            recent_articles = db.get_recent_articles(1)
            if recent_articles:
                return datetime.fromisoformat(recent_articles[0]['published_at'])
            return None
        except Exception:
            return None
    
    def _check_overall_health(self):
        """فحص الحالة العامة للنظام"""
        issues = []
        
        # فحص الأخطاء المتراكمة
        if self.health_status['error_count_24h'] > self.error_threshold:
            issues.append(f"عدد الأخطاء عالي: {self.health_status['error_count_24h']}")
        
        # فحص عدم معالجة مقالات لفترة طويلة
        last_article = self.health_status['last_article_processed']
        if last_article:
            hours_since_last = (datetime.now() - last_article).total_seconds() / 3600
            if hours_since_last > 4:  # لم تتم معالجة مقالات لأكثر من 4 ساعات
                issues.append(f"لم تتم معالجة مقالات منذ {hours_since_last:.1f} ساعة")
        
        # فحص قاعدة البيانات
        if not self.health_status['database_connected']:
            issues.append("فقدان الاتصال بقاعدة البيانات")
        
        if issues:
            logger.warning(f"⚠️ مشاكل في صحة النظام: {'; '.join(issues)}")
            # إرسال تنبيه إذا كانت المشاكل حرجة
            if len(issues) > 1 or "قاعدة البيانات" in str(issues):
                self._send_health_alert(issues)
    
    def _send_health_alert(self, issues: List[str]):
        """إرسال تنبيه حول مشاكل صحة النظام"""
        try:
            alert_message = f"""
🚨 تنبيه صحة النظام - وكيل ماين كرافت

المشاكل المكتشفة:
{chr(10).join([f"- {issue}" for issue in issues])}

الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
وقت التشغيل: {self.health_status['uptime_hours']:.1f} ساعة

يرجى فحص السجلات للمزيد من التفاصيل.
            """
            
            # تسجيل التنبيه في السجلات
            logger.critical(f"🚨 تنبيه صحة النظام: {'; '.join(issues)}")
            
            # هنا يمكن إضافة إرسال إشعار عبر تيليجرام أو البريد الإلكتروني
            
        except Exception as e:
            logger.error("❌ فشل في إرسال تنبيه صحة النظام", e)
    
    def get_health_report(self) -> Dict:
        """الحصول على تقرير صحة شامل"""
        return {
            **self.health_status,
            'last_health_check': self.last_health_check.isoformat(),
            'monitoring_active': self.is_monitoring
        }

class ErrorRecovery:
    """نظام الاستعادة من الأخطاء"""
    
    def __init__(self):
        self.recovery_strategies = {
            'database_error': self._recover_database_error,
            'api_error': self._recover_api_error,
            'telegram_error': self._recover_telegram_error,
            'network_error': self._recover_network_error,
            'memory_error': self._recover_memory_error,
            'general_error': self._recover_general_error
        }
        self.state_file = "data/bot_state.json"
    
    def save_state(self, state_data: Dict):
        """حفظ حالة البوت"""
        try:
            import os
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump({
                    **state_data,
                    'timestamp': datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)
                
            logger.debug("💾 تم حفظ حالة البوت")
            
        except Exception as e:
            logger.error("❌ فشل في حفظ حالة البوت", e)
    
    def load_state(self) -> Optional[Dict]:
        """تحميل حالة البوت المحفوظة"""
        try:
            if not os.path.exists(self.state_file):
                return None
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
                
            logger.info("📂 تم تحميل حالة البوت المحفوظة")
            return state_data
            
        except Exception as e:
            logger.error("❌ فشل في تحميل حالة البوت", e)
            return None
    
    def attempt_recovery(self, error_type: str, error_details: Dict) -> bool:
        """محاولة الاستعادة من خطأ محدد"""
        try:
            recovery_func = self.recovery_strategies.get(error_type, self._recover_general_error)
            
            logger.info(f"🔧 محاولة الاستعادة من خطأ: {error_type}")
            
            success = recovery_func(error_details)
            
            if success:
                logger.info(f"✅ نجحت الاستعادة من خطأ: {error_type}")
            else:
                logger.warning(f"⚠️ فشلت الاستعادة من خطأ: {error_type}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ خطأ أثناء محاولة الاستعادة من {error_type}", e)
            return False
    
    def _recover_database_error(self, error_details: Dict) -> bool:
        """الاستعادة من أخطاء قاعدة البيانات"""
        try:
            # إعادة إنشاء الاتصال بقاعدة البيانات
            db.init_database()
            
            # اختبار الاتصال
            test_stats = db.get_stats_summary(1)
            
            return bool(test_stats)
            
        except Exception as e:
            logger.error("❌ فشل في استعادة قاعدة البيانات", e)
            return False
    
    def _recover_api_error(self, error_details: Dict) -> bool:
        """الاستعادة من أخطاء API"""
        try:
            # انتظار قبل إعادة المحاولة
            time.sleep(30)

            # إعادة تهيئة الاتصالات إذا لزم الأمر
            # هذا يعتمد على نوع API المحدد

            return True

        except Exception as e:
            logger.error("❌ فشل في استعادة API", e)
            return False

    def _recover_telegram_error(self, error_details: Dict) -> bool:
        """الاستعادة من أخطاء تيليجرام"""
        try:
            error_message = error_details.get('error_message', '')

            if "Chat not found" in error_message:
                logger.info("🔧 محاولة إصلاح مشكلة 'Chat not found'...")

                # تشغيل سكريبت إصلاح معرف المدير
                try:
                    import subprocess
                    result = subprocess.run(['python', 'fix_admin_id.py'],
                                          capture_output=True, text=True, timeout=60)

                    if result.returncode == 0:
                        logger.info("✅ تم تشغيل سكريبت إصلاح المعرف بنجاح")
                        return True
                    else:
                        logger.warning(f"⚠️ فشل سكريبت الإصلاح: {result.stderr}")

                except Exception as fix_error:
                    logger.warning(f"⚠️ فشل في تشغيل سكريبت الإصلاح: {fix_error}")

                # إرشادات للمستخدم
                logger.info("💡 لحل مشكلة 'Chat not found' يدوياً:")
                logger.info("   1. شغل: python get_admin_id.py")
                logger.info("   2. أو شغل: python fix_admin_id.py")
                logger.info("   3. تأكد من إرسال رسالة للبوت من حساب المدير")

                return False

            elif "Unauthorized" in error_message:
                logger.info("🔧 مشكلة في توكن تيليجرام...")
                logger.info("💡 تحقق من TELEGRAM_BOT_TOKEN في config/settings.py")
                return False

            elif "Forbidden" in error_message:
                logger.info("🔧 مشكلة في صلاحيات البوت...")
                logger.info("💡 تأكد من إضافة البوت كمدير في القناة")
                return False

            else:
                # انتظار قبل إعادة المحاولة للأخطاء العامة
                time.sleep(30)
                return True

        except Exception as e:
            logger.error("❌ فشل في استعادة تيليجرام", e)
            return False
    
    def _recover_network_error(self, error_details: Dict) -> bool:
        """الاستعادة من أخطاء الشبكة"""
        try:
            # انتظار أطول للشبكة
            time.sleep(60)
            
            # محاولة اتصال تجريبي
            import requests
            response = requests.get("https://www.google.com", timeout=10)
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error("❌ فشل في استعادة الشبكة", e)
            return False
    
    def _recover_memory_error(self, error_details: Dict) -> bool:
        """الاستعادة من أخطاء الذاكرة"""
        try:
            # تنظيف الذاكرة
            import gc
            gc.collect()
            
            # إعادة تعيين المتغيرات الكبيرة إذا أمكن
            
            return True
            
        except Exception as e:
            logger.error("❌ فشل في استعادة الذاكرة", e)
            return False
    
    def _recover_general_error(self, error_details: Dict) -> bool:
        """الاستعادة العامة من الأخطاء"""
        try:
            # انتظار قصير
            time.sleep(10)
            
            # تنظيف عام
            import gc
            gc.collect()
            
            return True
            
        except Exception as e:
            logger.error("❌ فشل في الاستعادة العامة", e)
            return False

class FlexibleScheduler:
    """جدولة مرنة للمهام"""
    
    def __init__(self, base_interval: int = 7200):  # ساعتين بالثواني
        self.base_interval = base_interval
        self.current_interval = base_interval
        self.last_run = None
        self.consecutive_errors = 0
        self.max_interval = base_interval * 4  # حد أقصى 8 ساعات
        self.min_interval = base_interval // 2  # حد أدنى ساعة واحدة
    
    def get_next_run_time(self) -> datetime:
        """الحصول على وقت التشغيل التالي"""
        if self.last_run is None:
            return datetime.now()
        
        return self.last_run + timedelta(seconds=self.current_interval)
    
    def should_run_now(self) -> bool:
        """فحص ما إذا كان يجب التشغيل الآن"""
        return datetime.now() >= self.get_next_run_time()
    
    def mark_successful_run(self):
        """تسجيل تشغيل ناجح"""
        self.last_run = datetime.now()
        self.consecutive_errors = 0
        
        # إعادة تعيين الفترة للقيمة الأساسية إذا كانت مطولة
        if self.current_interval > self.base_interval:
            self.current_interval = max(
                self.base_interval,
                self.current_interval // 2
            )
        
        logger.debug(f"✅ تم تسجيل تشغيل ناجح، الفترة التالية: {self.current_interval} ثانية")
    
    def mark_failed_run(self, error: Exception):
        """تسجيل تشغيل فاشل"""
        self.last_run = datetime.now()
        self.consecutive_errors += 1
        
        # زيادة الفترة الزمنية تدريجياً مع الأخطاء المتتالية
        if self.consecutive_errors > 1:
            self.current_interval = min(
                self.max_interval,
                self.current_interval * 1.5
            )
        
        logger.warning(f"⚠️ تم تسجيل تشغيل فاشل ({self.consecutive_errors} أخطاء متتالية), الفترة التالية: {self.current_interval} ثانية")
    
    def reset_schedule(self):
        """إعادة تعيين الجدولة"""
        self.current_interval = self.base_interval
        self.consecutive_errors = 0
        self.last_run = None
        logger.info("🔄 تم إعادة تعيين الجدولة")

# مثيلات عامة للاستخدام
health_monitor = HealthMonitor()
error_recovery = ErrorRecovery()
retry_mechanism = RetryMechanism()
scheduler = FlexibleScheduler()
