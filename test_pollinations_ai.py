#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار Pollinations.AI - الطريقة الأساسية الجديدة لإنشاء الصور
"""

import asyncio
import sys
import os
import time

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.smart_image_manager import SmartImageManager, ImageGenerationPolicy

async def test_pollinations_basic():
    """اختبار أساسي لـ Pollinations.AI"""
    print("🧪 اختبار أساسي لـ Pollinations.AI...")
    print("=" * 50)
    
    # إنشاء مدير الصور الذكي
    smart_manager = SmartImageManager()
    
    # اختبار prompt بسيط
    test_prompt = {
        'prompt': 'gaming controller, modern setup, high quality, 4k',
        'category': 'test'
    }
    
    try:
        print(f"🎨 اختبار إنشاء صورة بـ Pollinations.AI...")
        print(f"   Prompt: {test_prompt['prompt']}")
        
        start_time = time.time()
        result = await smart_manager._generate_with_pollinations(test_prompt)
        end_time = time.time()
        
        if result:
            print(f"✅ نجح الاختبار!")
            print(f"   الوقت المستغرق: {end_time - start_time:.2f} ثانية")
            print(f"   الرابط: {result.get('url', 'N/A')}")
            print(f"   المصدر: {result.get('source', 'Unknown')}")
            print(f"   الترخيص: {result.get('license', 'Unknown')}")
            print(f"   الأبعاد: {result.get('width', 'N/A')}x{result.get('height', 'N/A')}")
            print(f"   طريقة الإنشاء: {result.get('generation_method', 'Unknown')}")
            return True
        else:
            print(f"❌ فشل الاختبار - لم يتم إرجاع نتيجة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

async def test_pollinations_with_article():
    """اختبار Pollinations.AI مع مقال حقيقي"""
    print(f"\n📰 اختبار Pollinations.AI مع مقال حقيقي...")
    
    # إنشاء سياسة مخصصة للاختبار
    test_policy = ImageGenerationPolicy(
        max_images_per_article=1,
        max_daily_generations=10,
        min_article_quality_score=3.0,
        cache_duration_hours=1,
        reuse_similar_images=False  # تعطيل التخزين المؤقت للاختبار
    )
    
    smart_manager = SmartImageManager(test_policy)
    
    # مقال اختبار
    test_article = {
        'title': 'New PlayStation 6 Gaming Console Announcement - Revolutionary Features',
        'content': 'Sony has officially announced the PlayStation 6 gaming console with groundbreaking features including 8K gaming support, advanced ray tracing technology, ultra-fast SSD storage, and revolutionary haptic feedback. The new console promises to deliver an unprecedented gaming experience with lightning-fast loading times and stunning visual fidelity.',
        'keywords': ['PlayStation 6', 'gaming console', 'Sony', 'announcement', '8K gaming', 'ray tracing']
    }
    
    try:
        print(f"📰 المقال: {test_article['title'][:60]}...")
        
        # حساب جودة المقال
        quality_score = smart_manager._calculate_article_quality_score(test_article)
        print(f"   نقاط الجودة: {quality_score:.1f}/10")
        
        # اختبار إنشاء صورة للمقال
        start_time = time.time()
        result = await smart_manager.generate_smart_image_for_article(test_article)
        end_time = time.time()
        
        if result:
            print(f"✅ تم إنشاء صورة للمقال بنجاح!")
            print(f"   الوقت المستغرق: {end_time - start_time:.2f} ثانية")
            print(f"   الرابط: {result.get('url', 'N/A')[:80]}...")
            print(f"   المصدر: {result.get('source', 'Unknown')}")
            print(f"   API المستخدم: {result.get('api_used', 'Unknown')}")
            print(f"   طريقة الإنشاء: {result.get('generation_method', 'Unknown')}")
            
            # عرض إحصائيات
            stats = smart_manager.get_daily_stats()
            print(f"\n📊 إحصائيات:")
            print(f"   الصور المُنشأة: {stats['images_generated']}")
            print(f"   استخدام Pollinations.AI: {stats['api_calls'].get('pollinations', 0)}")
            print(f"   إجمالي استدعاءات API: {stats['api_calls']['total']}")
            
            return True
        else:
            print(f"❌ فشل في إنشاء صورة للمقال")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المقال: {e}")
        return False

async def test_pollinations_prompts():
    """اختبار أنواع مختلفة من الـ prompts"""
    print(f"\n🎯 اختبار أنواع مختلفة من الـ prompts...")
    
    smart_manager = SmartImageManager()
    
    test_prompts = [
        {
            'prompt': 'gaming news, breaking announcement, modern digital art',
            'description': 'أخبار الألعاب'
        },
        {
            'prompt': 'game review, cinematic screenshot, high quality',
            'description': 'مراجعة لعبة'
        },
        {
            'prompt': 'esports tournament, competitive gaming, arena lighting',
            'description': 'رياضات إلكترونية'
        },
        {
            'prompt': 'indie game, pixel art style, creative design',
            'description': 'ألعاب مستقلة'
        }
    ]
    
    successful_tests = 0
    
    for i, test_data in enumerate(test_prompts, 1):
        try:
            print(f"\n🎨 اختبار {i}: {test_data['description']}")
            print(f"   Prompt: {test_data['prompt']}")
            
            prompt_data = {
                'prompt': test_data['prompt'],
                'category': 'test'
            }
            
            start_time = time.time()
            result = await smart_manager._generate_with_pollinations(prompt_data)
            end_time = time.time()
            
            if result:
                print(f"   ✅ نجح ({end_time - start_time:.2f}s)")
                print(f"   الرابط: {result.get('url', 'N/A')[:60]}...")
                successful_tests += 1
            else:
                print(f"   ❌ فشل")
                
            # تأخير قصير بين الاختبارات
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
    
    print(f"\n📊 نتائج اختبار الـ Prompts:")
    print(f"   نجح: {successful_tests}/{len(test_prompts)}")
    print(f"   معدل النجاح: {(successful_tests/len(test_prompts)*100):.1f}%")
    
    return successful_tests > 0

async def test_prompt_optimization():
    """اختبار تحسين الـ prompts"""
    print(f"\n🔧 اختبار تحسين الـ prompts...")
    
    smart_manager = SmartImageManager()
    
    original_prompts = [
        "A gaming controller, safe for work, family friendly, no violence, professional lighting, detailed artwork",
        "Modern gaming setup with RGB lighting, high quality, 4k, professional, safe for work",
        "Esports tournament scene, family friendly, no violence, professional lighting"
    ]
    
    for i, original in enumerate(original_prompts, 1):
        optimized = smart_manager._optimize_prompt_for_pollinations(original)
        
        print(f"\n🔧 اختبار {i}:")
        print(f"   الأصلي: {original}")
        print(f"   المحسن: {optimized}")
        print(f"   تقليل الطول: {len(original)} → {len(optimized)} حرف")

def test_url_encoding():
    """اختبار ترميز الـ URLs"""
    print(f"\n🔗 اختبار ترميز الـ URLs...")
    
    import urllib.parse
    
    test_prompts = [
        "gaming controller, high quality",
        "modern gaming setup with RGB lighting",
        "esports tournament, competitive gaming"
    ]
    
    for prompt in test_prompts:
        encoded = urllib.parse.quote(prompt)
        url = f"https://image.pollinations.ai/prompt/{encoded}"
        
        print(f"   Prompt: {prompt}")
        print(f"   URL: {url}")
        print(f"   طول URL: {len(url)} حرف")
        print()

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار شامل لـ Pollinations.AI...")
    print("🎯 Pollinations.AI - الطريقة الأساسية الجديدة (مجاني 100%)")
    print("=" * 60)
    
    try:
        # اختبار أساسي
        basic_success = await test_pollinations_basic()
        
        # اختبار مع مقال
        article_success = await test_pollinations_with_article()
        
        # اختبار prompts مختلفة
        prompts_success = await test_pollinations_prompts()
        
        # اختبار تحسين prompts
        await test_prompt_optimization()
        
        # اختبار ترميز URLs
        test_url_encoding()
        
        print("\n" + "=" * 60)
        print("📋 ملخص الاختبار الشامل:")
        print("-" * 30)
        
        if basic_success and article_success and prompts_success:
            print("✅ Pollinations.AI يعمل بنجاح كطريقة أساسية!")
            print("\n💡 المميزات المحققة:")
            print("   • مجاني 100% - لا يحتاج API key")
            print("   • سرعة عالية في الإنشاء")
            print("   • جودة ممتازة (1024x1024)")
            print("   • دعم نموذج Flux المتقدم")
            print("   • تحسين تلقائي للـ prompts")
            print("   • ترخيص مفتوح المصدر")
            
            print("\n🎯 الفوائد:")
            print("   • توفير كامل في تكاليف APIs")
            print("   • لا توجد حدود يومية")
            print("   • استقرار عالي في الخدمة")
            print("   • جودة متسقة للصور")
            
            return True
        else:
            print("⚠️ بعض الاختبارات فشلت، يرجى المراجعة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
