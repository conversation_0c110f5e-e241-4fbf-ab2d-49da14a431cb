#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام البحث العميق باستخدام Gemini
يستخدم فقط عند فشل جميع طرق البحث الأخرى لتوفير الحد اليومي
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import google.generativeai as genai

from modules.logger import logger
from modules.advanced_api_manager import advanced_api_manager
from config.workflow_settings import WorkflowConfig

class SearchDepth(Enum):
    """مستويات عمق البحث"""
    BASIC = "basic"
    STANDARD = "standard"
    ADVANCED = "advanced"
    COMPREHENSIVE = "comprehensive"

class GeminiDeepSearch:
    """نظام البحث العميق باستخدام Gemini"""

    def __init__(self):
        self.daily_usage_limit = 50  # حد يومي منخفض للبحث العميق
        self.current_daily_usage = 0
        self.last_reset_date = datetime.now().date()
        self.is_enabled = False
        self.fallback_only = True  # يستخدم فقط كحل احتياطي

        # إعداد Gemini
        self._setup_gemini()

    def _setup_gemini(self):
        """إعداد Gemini للبحث العميق"""
        try:
            # الحصول على مفتاح من المدير المتقدم
            api_key = advanced_api_manager.get_active_key('gemini')

            if not api_key:
                logger.warning("⚠️ لا يوجد مفتاح Gemini متاح للبحث العميق")
                return

            genai.configure(api_key=api_key)

            # إعداد النموذج للبحث
            self.model = genai.GenerativeModel(
                model_name="gemini-2.5-pro",
                generation_config={
                    "temperature": 0.3,  # أقل إبداعية، أكثر دقة
                    "top_p": 0.8,
                    "top_k": 40,
                    "max_output_tokens": 2048,
                }
            )

            self.is_enabled = True
            logger.info("✅ تم إعداد Gemini للبحث العميق")

        except Exception as e:
            logger.error(f"❌ خطأ في إعداد Gemini للبحث العميق: {e}")
            self.is_enabled = False

    def _check_daily_limit(self) -> bool:
        """فحص الحد اليومي"""
        current_date = datetime.now().date()

        # إعادة تعيين العداد إذا كان يوم جديد
        if current_date > self.last_reset_date:
            self.current_daily_usage = 0
            self.last_reset_date = current_date
            logger.info("🔄 تم إعادة تعيين عداد البحث العميق اليومي")

        return self.current_daily_usage < self.daily_usage_limit

    def _increment_usage(self):
        """زيادة عداد الاستخدام"""
        self.current_daily_usage += 1
        logger.info(f"📊 استخدام البحث العميق: {self.current_daily_usage}/{self.daily_usage_limit}")

    async def search_gaming_news(self, query: str, max_results: int = 3) -> List[Dict]:
        """البحث عن أخبار الألعاب باستخدام Gemini"""
        try:
            # فحص الشروط
            if not self.is_enabled:
                logger.warning("⚠️ البحث العميق غير مفعل")
                return []

            if not self._check_daily_limit():
                logger.warning(f"⚠️ تم تجاوز الحد اليومي للبحث العميق ({self.daily_usage_limit})")
                return []

            logger.info(f"🔍 بدء البحث العميق: {query}")

            # إنشاء prompt للبحث
            search_prompt = self._create_search_prompt(query, max_results)

            # تنفيذ البحث
            response = await self._execute_search(search_prompt)

            if response:
                # زيادة عداد الاستخدام
                self._increment_usage()

                # معالجة النتائج
                results = self._parse_search_results(response)

                logger.info(f"✅ البحث العميق أنتج {len(results)} نتيجة")
                return results

            return []

        except Exception as e:
            logger.error(f"❌ خطأ في البحث العميق: {e}")
            return []

    def _create_search_prompt(self, query: str, max_results: int) -> str:
        """إنشاء prompt للبحث"""
        current_date = datetime.now().strftime("%Y-%m-%d")

        prompt = f"""
أنت محرك بحث متخصص في أخبار الألعاب. مهمتك هي إنشاء {max_results} مقالات إخبارية حديثة ومثيرة للاهتمام حول: "{query}"

المتطلبات:
1. المقالات يجب أن تكون حديثة (تاريخ {current_date})
2. تركز على ألعاب الفيديو والتكنولوجيا
3. تحتوي على معلومات مفيدة وجذابة
4. مناسبة للجمهور العربي
5. تتضمن عناوين جذابة

لكل مقال، قدم:
- عنوان جذاب ومثير
- ملخص قصير (2-3 جمل)
- الفئة (أخبار، مراجعات، دلائل، إلخ)
- كلمات مفتاحية (3-5 كلمات)
- مستوى الأهمية (1-10)

أجب بتنسيق JSON فقط:
{{
  "articles": [
    {{
      "title": "العنوان هنا",
      "summary": "الملخص هنا",
      "category": "الفئة هنا",
      "keywords": ["كلمة1", "كلمة2", "كلمة3"],
      "importance": 8,
      "content_type": "news",
      "estimated_reading_time": "3 دقائق"
    }}
  ]
}}
"""
        return prompt

    async def _execute_search(self, prompt: str) -> Optional[str]:
        """تنفيذ البحث باستخدام Gemini"""
        try:
            # الحصول على مفتاح جديد للتأكد
            api_key = advanced_api_manager.get_active_key('gemini')

            if not api_key:
                logger.warning("⚠️ لا يوجد مفتاح Gemini متاح")
                return None

            # تنفيذ الطلب
            response = self.model.generate_content(prompt)

            if response and response.text:
                # تسجيل النجاح
                advanced_api_manager.report_key_success(api_key, 'gemini')
                return response.text

            return None

        except Exception as e:
            # تسجيل الخطأ
            api_key = advanced_api_manager.get_active_key('gemini')
            if api_key:
                error_type = type(e).__name__
                advanced_api_manager.report_key_error(api_key, 'gemini', error_type, str(e))

            logger.error(f"❌ خطأ في تنفيذ البحث العميق: {e}")
            return None

    def _parse_search_results(self, response_text: str) -> List[Dict]:
        """معالجة نتائج البحث"""
        try:
            # تنظيف النص
            clean_text = response_text.strip()

            # البحث عن JSON في النص
            start_idx = clean_text.find('{')
            end_idx = clean_text.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_text = clean_text[start_idx:end_idx]

                # تحليل JSON
                data = json.loads(json_text)

                if 'articles' in data and isinstance(data['articles'], list):
                    articles = []

                    for article_data in data['articles']:
                        # تحويل إلى تنسيق النظام
                        article = {
                            'title': article_data.get('title', ''),
                            'content': article_data.get('summary', ''),
                            'category': article_data.get('category', 'أخبار'),
                            'keywords': article_data.get('keywords', []),
                            'source': 'Gemini Deep Search',
                            'url': f"https://deep-search-{int(time.time())}.local",
                            'published_date': datetime.now().isoformat(),
                            'importance': article_data.get('importance', 5),
                            'content_type': article_data.get('content_type', 'news'),
                            'reading_time': article_data.get('estimated_reading_time', '3 دقائق'),
                            'is_deep_search': True
                        }

                        # التحقق من جودة المقال
                        if self._validate_article(article):
                            articles.append(article)

                    return articles

            logger.warning("⚠️ لم يتم العثور على JSON صالح في نتائج البحث العميق")
            return []

        except json.JSONDecodeError as e:
            logger.error(f"❌ خطأ في تحليل JSON للبحث العميق: {e}")
            return []
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة نتائج البحث العميق: {e}")
            return []

    def _validate_article(self, article: Dict) -> bool:
        """التحقق من صحة المقال"""
        try:
            # فحص الحقول المطلوبة
            required_fields = ['title', 'content', 'category']

            for field in required_fields:
                if not article.get(field) or not article[field].strip():
                    return False

            # فحص طول العنوان
            title = article['title'].strip()
            if len(title) < 10 or len(title) > 200:
                return False

            # فحص طول المحتوى
            content = article['content'].strip()
            if len(content) < 50:
                return False

            # فحص الكلمات المفتاحية
            keywords = article.get('keywords', [])
            if not isinstance(keywords, list) or len(keywords) == 0:
                article['keywords'] = [title.split()[0], 'ألعاب', 'تكنولوجيا']

            return True

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من صحة المقال: {e}")
            return False

    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        return {
            'daily_usage': self.current_daily_usage,
            'daily_limit': self.daily_usage_limit,
            'remaining_searches': max(0, self.daily_usage_limit - self.current_daily_usage),
            'usage_percentage': (self.current_daily_usage / self.daily_usage_limit) * 100,
            'is_enabled': self.is_enabled,
            'last_reset_date': self.last_reset_date.isoformat()
        }

# إنشاء مثيل عام
gemini_deep_search = GeminiDeepSearch()

