#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج أنماط صور الألعاب باستخدام Gemini 2.5 Pro
لتحليل صور الألعاب وإنشاء prompts لصور حصرية مشابهة
"""

import asyncio
import aiohttp
import base64
import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

from .logger import logger
from config.settings import google_api_manager

@dataclass
class GameImageStyle:
    """نمط صورة اللعبة المستخرج"""
    # معلومات أساسية
    game_title: str
    genre: str
    platform: str
    
    # النمط البصري
    art_style: str
    visual_theme: str
    color_palette: List[str]
    dominant_colors: List[str]
    
    # التكوين والتصميم
    composition_style: str
    lighting_type: str
    perspective: str
    mood: str
    
    # عناصر UI/UX
    ui_style: str
    ui_elements: List[str]
    typography_style: str
    icon_style: str
    
    # عناصر اللعبة
    characters: List[str]
    environment: str
    objects: List[str]
    effects: List[str]
    
    # معلومات تقنية
    resolution_style: str
    rendering_style: str
    texture_quality: str
    
    # Prompts للإنشاء
    creation_prompt: str
    style_prompt: str
    color_prompt: str
    composition_prompt: str
    
    # معلومات إضافية
    confidence_score: float
    analysis_timestamp: datetime
    source_image: str

class GameImageStyleExtractor:
    """مستخرج أنماط صور الألعاب المتقدم"""
    
    def __init__(self):
        self.enabled = bool(google_api_manager and google_api_manager.get_key())
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.model_name = "gemini-2.5-pro"
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_extractions': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'styles_extracted': 0,
            'prompts_generated': 0,
            'daily_usage': 0,
            'last_reset': datetime.now().date()
        }
        
        # قالب التحليل المتقدم
        self.analysis_prompt = """
        Analyze this gaming image in extreme detail to extract its visual style for creating similar exclusive images.

        Provide a comprehensive analysis in this EXACT JSON format:

        {
            "game_title": "Game name if visible or identifiable, otherwise 'Unknown'",
            "genre": "Game genre based on visual clues (FPS, RPG, Racing, Strategy, etc.)",
            "platform": "Platform hints (PC, Console, Mobile) based on UI style",
            
            "art_style": "Detailed art style (Realistic, Cartoon, Pixel Art, Anime, Minimalist, etc.)",
            "visual_theme": "Overall visual theme (Dark/Gritty, Bright/Colorful, Futuristic, Medieval, etc.)",
            "color_palette": ["List of 5-8 specific colors used"],
            "dominant_colors": ["Top 3 most prominent colors"],
            
            "composition_style": "Layout and framing (Centered, Dynamic, Asymmetrical, etc.)",
            "lighting_type": "Lighting style (Dramatic, Soft, Neon, Natural, etc.)",
            "perspective": "Camera angle (First-person, Third-person, Top-down, Side-view, etc.)",
            "mood": "Overall mood and atmosphere",
            
            "ui_style": "UI design approach (Modern, Retro, Minimalist, Complex, etc.)",
            "ui_elements": ["List of visible UI elements"],
            "typography_style": "Font and text style used",
            "icon_style": "Style of icons and symbols",
            
            "characters": ["Description of any visible characters"],
            "environment": "Setting and environment description",
            "objects": ["Notable objects or items visible"],
            "effects": ["Visual effects like particles, glow, etc."],
            
            "resolution_style": "Visual quality (High-res, Pixel art, Low-poly, etc.)",
            "rendering_style": "Rendering approach (3D realistic, 2D flat, Isometric, etc.)",
            "texture_quality": "Texture detail level",
            
            "creation_prompt": "Detailed prompt for creating a similar gaming image",
            "style_prompt": "Specific prompt focusing on the art style and visual approach",
            "color_prompt": "Prompt emphasizing the color scheme and palette",
            "composition_prompt": "Prompt for recreating the layout and composition"
        }

        Ensure all fields are filled with specific, detailed information. Be as descriptive as possible.
        """
        
        logger.info(f"🎨 تم تهيئة مستخرج أنماط صور الألعاب - الحالة: {'مفعل' if self.enabled else 'معطل'}")
    
    async def extract_game_style(self, image_path: str) -> Optional[GameImageStyle]:
        """استخراج النمط البصري من صورة لعبة"""
        
        if not self.enabled:
            logger.warning("⚠️ مستخرج أنماط الصور غير مفعل")
            return None
        
        if not os.path.exists(image_path):
            logger.error(f"❌ الصورة غير موجودة: {image_path}")
            return None
        
        self.usage_stats['total_extractions'] += 1
        self.usage_stats['daily_usage'] += 1
        
        logger.info(f"🎨 بدء استخراج النمط من: {os.path.basename(image_path)}")
        
        try:
            # تحويل الصورة إلى base64
            image_data = await self._encode_image_to_base64(image_path)
            if not image_data:
                raise Exception("فشل في قراءة الصورة")
            
            # تنفيذ التحليل
            analysis_result = await self._perform_style_analysis(image_data, image_path)
            
            if analysis_result['success']:
                # معالجة النتائج
                style = self._process_style_data(analysis_result['response'], image_path)
                
                if style:
                    self.usage_stats['successful_extractions'] += 1
                    self.usage_stats['styles_extracted'] += 1
                    if style.creation_prompt:
                        self.usage_stats['prompts_generated'] += 1
                    
                    logger.info(f"✅ تم استخراج النمط بنجاح: {style.art_style}")
                    return style
                else:
                    raise Exception("فشل في معالجة نتائج التحليل")
            else:
                raise Exception(analysis_result['error'])
                
        except Exception as e:
            self.usage_stats['failed_extractions'] += 1
            logger.error(f"❌ فشل في استخراج النمط من {image_path}: {e}")
            return None
    
    async def _perform_style_analysis(self, image_data: str, image_path: str) -> Dict[str, Any]:
        """تنفيذ تحليل النمط"""
        
        try:
            url = f"{self.base_url}/models/{self.model_name}:generateContent"
            
            headers = {
                'Content-Type': 'application/json'
            }
            
            params = {
                'key': google_api_manager.get_key()
            }
            
            # تحديد نوع الصورة
            image_format = 'image/png' if image_path.lower().endswith('.png') else 'image/jpeg'
            
            payload = {
                "contents": [{
                    "parts": [
                        {
                            "text": self.analysis_prompt
                        },
                        {
                            "inlineData": {
                                "mimeType": image_format,
                                "data": image_data
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "temperature": 0.2,  # دقة عالية جداً
                    "topK": 32,
                    "topP": 0.8,
                    "maxOutputTokens": 3072,
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, params=params, json=payload, timeout=90) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'candidates' in data and data['candidates']:
                            content = data['candidates'][0].get('content', {})
                            parts = content.get('parts', [])
                            
                            if parts and 'text' in parts[0]:
                                response_text = parts[0]['text'].strip()
                                
                                return {
                                    'success': True,
                                    'response': response_text
                                }
                    
                    # في حالة فشل الطلب
                    error_text = await response.text()
                    return {
                        'success': False,
                        'error': f'HTTP {response.status}: {error_text[:200]}'
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _process_style_data(self, response_text: str, image_path: str) -> Optional[GameImageStyle]:
        """معالجة بيانات النمط المستخرجة"""
        
        try:
            # محاولة استخراج JSON من الاستجابة
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                style_data = json.loads(json_text)
                
                # إنشاء كائن GameImageStyle
                style = GameImageStyle(
                    # معلومات أساسية
                    game_title=style_data.get('game_title', 'Unknown'),
                    genre=style_data.get('genre', 'Unknown'),
                    platform=style_data.get('platform', 'Unknown'),
                    
                    # النمط البصري
                    art_style=style_data.get('art_style', 'Unknown'),
                    visual_theme=style_data.get('visual_theme', 'Unknown'),
                    color_palette=style_data.get('color_palette', []),
                    dominant_colors=style_data.get('dominant_colors', []),
                    
                    # التكوين والتصميم
                    composition_style=style_data.get('composition_style', 'Unknown'),
                    lighting_type=style_data.get('lighting_type', 'Unknown'),
                    perspective=style_data.get('perspective', 'Unknown'),
                    mood=style_data.get('mood', 'Unknown'),
                    
                    # عناصر UI/UX
                    ui_style=style_data.get('ui_style', 'Unknown'),
                    ui_elements=style_data.get('ui_elements', []),
                    typography_style=style_data.get('typography_style', 'Unknown'),
                    icon_style=style_data.get('icon_style', 'Unknown'),
                    
                    # عناصر اللعبة
                    characters=style_data.get('characters', []),
                    environment=style_data.get('environment', 'Unknown'),
                    objects=style_data.get('objects', []),
                    effects=style_data.get('effects', []),
                    
                    # معلومات تقنية
                    resolution_style=style_data.get('resolution_style', 'Unknown'),
                    rendering_style=style_data.get('rendering_style', 'Unknown'),
                    texture_quality=style_data.get('texture_quality', 'Unknown'),
                    
                    # Prompts للإنشاء
                    creation_prompt=style_data.get('creation_prompt', ''),
                    style_prompt=style_data.get('style_prompt', ''),
                    color_prompt=style_data.get('color_prompt', ''),
                    composition_prompt=style_data.get('composition_prompt', ''),
                    
                    # معلومات إضافية
                    confidence_score=self._calculate_confidence_score(style_data),
                    analysis_timestamp=datetime.now(),
                    source_image=image_path
                )
                
                return style
            
            else:
                # إذا لم يكن JSON، حاول استخراج المعلومات بطريقة أخرى
                logger.warning("⚠️ لم يتم العثور على JSON صالح، محاولة استخراج بديلة")
                return self._extract_style_from_text(response_text, image_path)
                
        except json.JSONDecodeError as e:
            logger.warning(f"⚠️ خطأ في تحليل JSON: {e}")
            return self._extract_style_from_text(response_text, image_path)
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة بيانات النمط: {e}")
            return None
    
    def _extract_style_from_text(self, text: str, image_path: str) -> Optional[GameImageStyle]:
        """استخراج النمط من النص العادي كبديل"""
        
        try:
            # استخراج أساسي من النص
            lines = text.split('\n')
            
            # قيم افتراضية
            style_data = {
                'art_style': 'Unknown',
                'visual_theme': 'Unknown',
                'color_palette': [],
                'creation_prompt': text[:500] if text else ''
            }
            
            # محاولة استخراج معلومات من النص
            for line in lines:
                line = line.strip().lower()
                if 'style' in line and len(line) < 100:
                    style_data['art_style'] = line
                elif 'color' in line and len(line) < 100:
                    style_data['visual_theme'] = line
            
            return GameImageStyle(
                game_title='Unknown',
                genre='Unknown',
                platform='Unknown',
                art_style=style_data['art_style'],
                visual_theme=style_data['visual_theme'],
                color_palette=style_data['color_palette'],
                dominant_colors=[],
                composition_style='Unknown',
                lighting_type='Unknown',
                perspective='Unknown',
                mood='Unknown',
                ui_style='Unknown',
                ui_elements=[],
                typography_style='Unknown',
                icon_style='Unknown',
                characters=[],
                environment='Unknown',
                objects=[],
                effects=[],
                resolution_style='Unknown',
                rendering_style='Unknown',
                texture_quality='Unknown',
                creation_prompt=style_data['creation_prompt'],
                style_prompt='',
                color_prompt='',
                composition_prompt='',
                confidence_score=0.3,
                analysis_timestamp=datetime.now(),
                source_image=image_path
            )
            
        except Exception as e:
            logger.error(f"❌ فشل في الاستخراج البديل: {e}")
            return None
    
    def _calculate_confidence_score(self, style_data: Dict) -> float:
        """حساب نقاط الثقة بناءً على كمية المعلومات المستخرجة"""
        
        score = 0.0
        total_fields = 0
        
        # فحص الحقول المهمة
        important_fields = [
            'art_style', 'visual_theme', 'color_palette', 'composition_style',
            'lighting_type', 'ui_style', 'creation_prompt'
        ]
        
        for field in important_fields:
            total_fields += 1
            value = style_data.get(field)
            
            if value and value != 'Unknown' and value != []:
                if isinstance(value, list) and len(value) > 0:
                    score += 1
                elif isinstance(value, str) and len(value) > 5:
                    score += 1
        
        return min(score / total_fields, 1.0)
    
    async def _encode_image_to_base64(self, image_path: str) -> Optional[str]:
        """تحويل الصورة إلى base64"""
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                encoded_data = base64.b64encode(image_data).decode('utf-8')
                return encoded_data
        except Exception as e:
            logger.error(f"❌ فشل في تحويل الصورة إلى base64: {e}")
            return None
    
    async def batch_extract_styles(self, image_paths: List[str]) -> List[GameImageStyle]:
        """استخراج الأنماط من مجموعة صور"""
        
        logger.info(f"🎨 بدء استخراج الأنماط من {len(image_paths)} صورة")
        
        styles = []
        for i, image_path in enumerate(image_paths, 1):
            logger.info(f"🎨 استخراج النمط {i}/{len(image_paths)}: {os.path.basename(image_path)}")
            
            style = await self.extract_game_style(image_path)
            if style:
                styles.append(style)
            
            # تأخير بين الصور
            if i < len(image_paths):
                await asyncio.sleep(3)
        
        successful_extractions = len(styles)
        logger.info(f"✅ تم استخراج {successful_extractions}/{len(image_paths)} نمط بنجاح")
        
        return styles
    
    def save_style_to_file(self, style: GameImageStyle, output_path: str):
        """حفظ النمط في ملف JSON"""
        
        try:
            style_dict = asdict(style)
            # تحويل datetime إلى string
            style_dict['analysis_timestamp'] = style.analysis_timestamp.isoformat()
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(style_dict, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 تم حفظ النمط في: {output_path}")
            
        except Exception as e:
            logger.error(f"❌ فشل في حفظ النمط: {e}")
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخدام"""
        
        total_extractions = self.usage_stats['total_extractions']
        success_rate = (self.usage_stats['successful_extractions'] / total_extractions * 100) if total_extractions > 0 else 0
        
        return {
            'enabled': self.enabled,
            'model': self.model_name,
            'total_extractions': total_extractions,
            'successful_extractions': self.usage_stats['successful_extractions'],
            'failed_extractions': self.usage_stats['failed_extractions'],
            'success_rate': round(success_rate, 2),
            'styles_extracted': self.usage_stats['styles_extracted'],
            'prompts_generated': self.usage_stats['prompts_generated'],
            'daily_usage': self.usage_stats['daily_usage'],
            'last_reset': self.usage_stats['last_reset']
        }

# إنشاء مثيل عام
game_style_extractor = GameImageStyleExtractor()
