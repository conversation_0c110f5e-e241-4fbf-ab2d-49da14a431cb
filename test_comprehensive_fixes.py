#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للإصلاحات المطبقة على وكيل أخبار الألعاب
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.smart_image_manager import smart_image_manager
from modules.licensed_image_manager import licensed_image_manager
from modules.content_generator import ContentGenerator
from modules.api_key_manager import ApiKeyManager

class ComprehensiveFixesTest:
    """اختبار شامل للإصلاحات"""
    
    def __init__(self):
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'overall_success': False
        }
        
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🧪 بدء الاختبار الشامل للإصلاحات...")
        
        tests = [
            ('test_game_name_extraction', self.test_game_name_extraction),
            ('test_licensed_images', self.test_licensed_images),
            ('test_title_content_matching', self.test_title_content_matching),
            ('test_api_key_management', self.test_api_key_management),
            ('test_smart_image_generation', self.test_smart_image_generation)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                logger.info(f"🔍 تشغيل اختبار: {test_name}")
                result = await test_func()
                self.test_results['tests'][test_name] = result
                
                if result['success']:
                    passed_tests += 1
                    logger.info(f"✅ نجح اختبار {test_name}")
                else:
                    logger.error(f"❌ فشل اختبار {test_name}: {result.get('error', 'غير محدد')}")
                    
            except Exception as e:
                logger.error(f"💥 خطأ في اختبار {test_name}: {e}")
                self.test_results['tests'][test_name] = {
                    'success': False,
                    'error': str(e)
                }
        
        # حساب النتيجة الإجمالية
        success_rate = passed_tests / total_tests
        self.test_results['overall_success'] = success_rate >= 0.8  # 80% نجاح على الأقل
        self.test_results['success_rate'] = success_rate
        self.test_results['passed_tests'] = passed_tests
        self.test_results['total_tests'] = total_tests
        
        # حفظ النتائج
        await self.save_results()
        
        # عرض الملخص
        self.display_summary()
        
        return self.test_results
    
    async def test_game_name_extraction(self):
        """اختبار استخراج أسماء الألعاب"""
        try:
            test_articles = [
                {
                    'title': 'مراجعة شاملة للعبة Stardew Valley - تجربة استثنائية',
                    'content': 'Stardew Valley هي لعبة محاكاة زراعية رائعة...',
                    'keywords': ['Stardew Valley', 'محاكاة', 'زراعة']
                },
                {
                    'title': 'دليل المبتدئين لـ Minecraft',
                    'content': 'Minecraft هي لعبة بناء وإبداع...',
                    'keywords': ['Minecraft', 'بناء', 'إبداع']
                },
                {
                    'title': 'أفضل ألعاب 2025',
                    'content': 'قائمة بأفضل الألعاب لهذا العام...',
                    'keywords': ['ألعاب', '2025', 'أفضل']
                }
            ]
            
            extracted_games = []
            for article in test_articles:
                game_name = smart_image_manager._extract_game_name_from_article(article)
                extracted_games.append({
                    'title': article['title'],
                    'extracted_game': game_name
                })
            
            # فحص النتائج
            successful_extractions = sum(1 for game in extracted_games if game['extracted_game'])
            success_rate = successful_extractions / len(test_articles)
            
            return {
                'success': success_rate >= 0.6,  # 60% نجاح على الأقل
                'extracted_games': extracted_games,
                'success_rate': success_rate,
                'details': f'تم استخراج {successful_extractions} من {len(test_articles)} أسماء ألعاب'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_licensed_images(self):
        """اختبار نظام الصور المرخصة"""
        try:
            if not licensed_image_manager:
                return {
                    'success': False,
                    'error': 'مدير الصور المرخصة غير متوفر'
                }
            
            # اختبار البحث عن صور لألعاب مختلفة
            test_games = ['Stardew Valley', 'Minecraft', 'The Witcher 3']
            results = {}
            
            for game in test_games:
                try:
                    images = await licensed_image_manager.get_licensed_images_for_game(game, 1)
                    results[game] = {
                        'found_images': len(images),
                        'sources': [img.source for img in images] if images else []
                    }
                except Exception as e:
                    results[game] = {
                        'found_images': 0,
                        'error': str(e)
                    }
            
            # فحص النتائج
            total_images_found = sum(result.get('found_images', 0) for result in results.values())
            
            return {
                'success': total_images_found > 0,  # على الأقل صورة واحدة
                'results': results,
                'total_images_found': total_images_found,
                'details': f'تم العثور على {total_images_found} صورة مرخصة'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_title_content_matching(self):
        """اختبار خوارزمية التطابق بين العنوان والمحتوى"""
        try:
            content_generator = ContentGenerator()
            
            test_cases = [
                {
                    'title': 'دليل المبتدئين الشامل لدخول عالم ألعاب الفيديو',
                    'content': 'هذا دليل شامل للمبتدئين الذين يريدون دخول عالم ألعاب الفيديو. سنتحدث عن الأساسيات والنصائح للمبتدئين.',
                    'expected_match': True
                },
                {
                    'title': 'مراجعة لعبة Cyberpunk 2077',
                    'content': 'Cyberpunk 2077 هي لعبة رائعة مع قصة مثيرة. تقييمنا للعبة إيجابي جداً.',
                    'expected_match': True
                },
                {
                    'title': 'نصائح للمبتدئين في الألعاب',
                    'content': 'هذا مقال عن تطوير الألعاب والبرمجة.',  # لا يتطابق
                    'expected_match': False
                }
            ]
            
            correct_predictions = 0
            results = []
            
            for case in test_cases:
                analysis = content_generator._analyze_title_content_match(
                    case['title'], 
                    case['content']
                )
                
                predicted_match = analysis['match']
                is_correct = predicted_match == case['expected_match']
                
                if is_correct:
                    correct_predictions += 1
                
                results.append({
                    'title': case['title'],
                    'predicted_match': predicted_match,
                    'expected_match': case['expected_match'],
                    'is_correct': is_correct,
                    'match_ratio': analysis['match_ratio'],
                    'missing_concepts': analysis.get('missing_concepts', [])
                })
            
            accuracy = correct_predictions / len(test_cases)
            
            return {
                'success': accuracy >= 0.8,  # 80% دقة على الأقل
                'accuracy': accuracy,
                'results': results,
                'details': f'دقة التطابق: {accuracy:.1%}'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_api_key_management(self):
        """اختبار نظام إدارة مفاتيح API"""
        try:
            # إنشاء مدير مفاتيح للاختبار
            test_keys = ['key1', 'key2', 'key3']
            api_manager = ApiKeyManager(
                api_keys=test_keys,
                service_name='test_service',
                daily_limit_per_key=100,
                rate_limit_per_minute=10
            )
            
            # اختبار الحصول على مفتاح
            key1 = api_manager.get_key()
            assert key1 in test_keys
            
            # اختبار التبديل عند الفشل
            api_manager.mark_key_failed(key1)
            key2 = api_manager.get_key()
            assert key2 != key1
            
            # اختبار الإحصائيات
            stats = api_manager.get_usage_stats()
            assert 'daily_usage' in stats
            assert 'conservation_mode' in stats['daily_usage']
            
            return {
                'success': True,
                'details': 'جميع اختبارات إدارة المفاتيح نجحت',
                'stats': stats
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_smart_image_generation(self):
        """اختبار نظام إنشاء الصور الذكي"""
        try:
            test_article = {
                'title': 'مراجعة لعبة Stardew Valley',
                'content': 'Stardew Valley هي لعبة محاكاة زراعية رائعة تقدم تجربة مريحة ومسلية.',
                'keywords': ['Stardew Valley', 'محاكاة', 'زراعة']
            }
            
            # اختبار تقييم جودة المقال
            quality_score = smart_image_manager._calculate_article_quality_score(test_article)
            
            # اختبار فحص إمكانية إنشاء صورة
            should_generate, reason = smart_image_manager._should_generate_image(test_article)
            
            # اختبار إنشاء prompt
            prompt = smart_image_manager._create_high_quality_prompt(test_article)
            
            return {
                'success': True,
                'quality_score': quality_score,
                'should_generate': should_generate,
                'reason': reason,
                'prompt_created': bool(prompt.get('prompt')),
                'details': 'نظام الصور الذكي يعمل بشكل صحيح'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def save_results(self):
        """حفظ نتائج الاختبار"""
        try:
            filename = f"test_comprehensive_fixes_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 تم حفظ نتائج الاختبار في: {filename}")
        except Exception as e:
            logger.error(f"❌ فشل في حفظ النتائج: {e}")
    
    def display_summary(self):
        """عرض ملخص النتائج"""
        print("\n" + "="*60)
        print("📊 ملخص نتائج الاختبار الشامل")
        print("="*60)
        
        success_rate = self.test_results['success_rate']
        passed = self.test_results['passed_tests']
        total = self.test_results['total_tests']
        
        print(f"🎯 معدل النجاح الإجمالي: {success_rate:.1%}")
        print(f"✅ الاختبارات الناجحة: {passed}/{total}")
        
        if self.test_results['overall_success']:
            print("🎉 النتيجة الإجمالية: نجح!")
        else:
            print("⚠️ النتيجة الإجمالية: يحتاج تحسين")
        
        print("\n📋 تفاصيل الاختبارات:")
        for test_name, result in self.test_results['tests'].items():
            status = "✅" if result['success'] else "❌"
            print(f"  {status} {test_name}: {result.get('details', result.get('error', 'غير محدد'))}")
        
        print("="*60)

async def main():
    """الدالة الرئيسية"""
    tester = ComprehensiveFixesTest()
    results = await tester.run_all_tests()
    
    if results['overall_success']:
        print("\n🎉 جميع الإصلاحات تعمل بشكل صحيح!")
        return 0
    else:
        print("\n⚠️ بعض الإصلاحات تحتاج مراجعة.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
