#!/usr/bin/env python3
"""
أداة اختبار نظام SEO المحسن
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

def test_seo_system():
    """اختبار نظام SEO المحسن"""
    print("🧪 بدء اختبار نظام SEO المحسن...")
    
    try:
        from modules.enhanced_seo_analyzer import enhanced_seo_analyzer
        from modules.seo_performance_booster import seo_performance_booster
        
        # مقال اختبار ضعيف
        test_article = {
            'title': 'لعبة جديدة',  # عنوان ضعيف
            'content': 'هذه لعبة جديدة جيدة.',  # محتوى قصير
            'keywords': ['لعبة'],  # كلمات مفتاحية قليلة
        }
        
        print("\n📊 تحليل المقال الأصلي:")
        original_analysis = enhanced_seo_analyzer.calculate_comprehensive_seo_score(test_article)
        print(f"النقاط الأصلية: {original_analysis['overall_score']}/100")
        
        # تطبيق التحسينات
        print("\n🚀 تطبيق التحسينات...")
        boosted_article = seo_performance_booster.boost_article_seo(test_article)
        
        # تحليل بعد التحسين
        print("\n📈 تحليل المقال بعد التحسين:")
        boosted_analysis = enhanced_seo_analyzer.calculate_comprehensive_seo_score(boosted_article)
        print(f"النقاط بعد التحسين: {boosted_analysis['overall_score']}/100")
        
        improvement = boosted_analysis['overall_score'] - original_analysis['overall_score']
        print(f"التحسن: +{improvement:.1f} نقطة")
        
        # عرض التفاصيل
        print("\n🔍 تفاصيل النقاط:")
        for component, score in boosted_analysis['component_scores'].items():
            print(f"  {component}: {score}/100")
        
        if boosted_analysis['recommendations']:
            print("\n💡 التوصيات:")
            for rec in boosted_analysis['recommendations']:
                print(f"  - {rec}")
        
        # تقييم النجاح
        if boosted_analysis['overall_score'] >= 65:
            print("\n✅ نجح الاختبار! النظام يحقق نقاط جيدة")
            return True
        elif boosted_analysis['overall_score'] >= 60:
            print("\n✅ نجح الاختبار جزئياً! النظام يحقق نقاط مقبولة")
            return True
        else:
            print("\n⚠️ النظام يحتاج مزيد من التحسين")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار SEO: {e}")
        return False

if __name__ == "__main__":
    success = test_seo_system()
    if success:
        print("\n🎯 نظام SEO يعمل بشكل ممتاز!")
    else:
        print("\n📋 نظام SEO يحتاج مراجعة")
