#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة ويب لإدارة الفهرسة الذكية
Web Interface for Intelligent Indexing Management
"""

from flask import Flask, render_template_string, jsonify, request
import json
import asyncio
from datetime import datetime
from typing import Dict, List

from modules.intelligent_indexing_manager import get_indexing_manager
from modules.logger import logger

app = Flask(__name__)

# HTML Template للواجهة الرئيسية
HTML_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 إدارة الفهرسة الذكية</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: linear-gradient(45deg, #f8f9ff, #e8f4fd);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #667eea;
        }
        .status-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .status-label {
            font-size: 0.9em;
            color: #666;
        }
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        button.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        button.success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }
        .issues-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .issue-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #e74c3c;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .issue-item.fixed {
            border-left-color: #27ae60;
        }
        .issue-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .issue-title {
            font-weight: bold;
            color: #333;
        }
        .issue-severity {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .severity-high { background: #e74c3c; color: white; }
        .severity-medium { background: #f39c12; color: white; }
        .severity-low { background: #3498db; color: white; }
        .issue-description {
            color: #666;
            margin-bottom: 10px;
        }
        .affected-pages {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .recommendations-section {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .recommendation-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #27ae60;
        }
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
        .loading {
            text-align: center;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .health-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .health-good { background: #27ae60; }
        .health-warning { background: #f39c12; }
        .health-danger { background: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 إدارة الفهرسة الذكية</h1>
        
        <!-- حالة النظام -->
        <div class="status-grid" id="statusGrid">
            <div class="status-card">
                <div class="status-number" id="totalIssues">0</div>
                <div class="status-label">إجمالي المشاكل</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="fixedIssues">0</div>
                <div class="status-label">مشاكل تم حلها</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="pendingIssues">0</div>
                <div class="status-label">مشاكل معلقة</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="successRate">0%</div>
                <div class="status-label">معدل النجاح</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="healthStatus">
                    <span id="healthText">جيد</span>
                    <span class="health-indicator health-good" id="healthIndicator"></span>
                </div>
                <div class="status-label">حالة الموقع</div>
            </div>
        </div>

        <!-- أدوات التحكم -->
        <div class="controls">
            <button onclick="startMonitoring()" class="success">🚀 بدء المراقبة</button>
            <button onclick="stopMonitoring()" class="danger">⏹️ إيقاف المراقبة</button>
            <button onclick="runFullScan()">🔍 فحص شامل</button>
            <button onclick="autoFixIssues()" class="success">🔧 إصلاح تلقائي</button>
            <button onclick="refreshData()">🔄 تحديث البيانات</button>
            <button onclick="downloadReport()">📊 تحميل التقرير</button>
        </div>

        <!-- التبويبات -->
        <div class="tabs">
            <div class="tab active" onclick="showTab('issues')">🚨 المشاكل الحالية</div>
            <div class="tab" onclick="showTab('fixed')">✅ المشاكل المحلولة</div>
            <div class="tab" onclick="showTab('recommendations')">💡 التوصيات</div>
            <div class="tab" onclick="showTab('logs')">📋 السجلات</div>
        </div>

        <!-- رسائل النظام -->
        <div id="systemMessage"></div>

        <!-- محتوى التبويبات -->
        <div id="issues" class="tab-content active">
            <div class="issues-section">
                <h3>🚨 المشاكل الحالية</h3>
                <div id="currentIssuesList">
                    <div class="loading">⏳ جاري تحميل المشاكل...</div>
                </div>
            </div>
        </div>

        <div id="fixed" class="tab-content">
            <div class="issues-section">
                <h3>✅ المشاكل المحلولة</h3>
                <div id="fixedIssuesList">
                    <div class="loading">⏳ جاري تحميل المشاكل المحلولة...</div>
                </div>
            </div>
        </div>

        <div id="recommendations" class="tab-content">
            <div class="recommendations-section">
                <h3>💡 توصيات التحسين</h3>
                <div id="recommendationsList">
                    <div class="loading">⏳ جاري تحميل التوصيات...</div>
                </div>
            </div>
        </div>

        <div id="logs" class="tab-content">
            <div class="issues-section">
                <h3>📋 سجلات النظام</h3>
                <div id="systemLogs">
                    <div class="loading">⏳ جاري تحميل السجلات...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentData = {};
        let monitoringActive = false;

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            setInterval(refreshData, 30000); // تحديث كل 30 ثانية
        });

        async function refreshData() {
            try {
                showMessage('⏳ جاري تحميل البيانات...', 'loading');
                
                const response = await fetch('/api/indexing-status');
                const data = await response.json();
                
                if (data.success) {
                    currentData = data;
                    updateStatusCards(data.status);
                    updateIssuesList(data.current_issues);
                    updateFixedIssuesList(data.fixed_issues);
                    updateRecommendations(data.recommendations);
                    
                    showMessage('✅ تم تحديث البيانات بنجاح', 'success');
                } else {
                    showMessage('❌ خطأ في تحميل البيانات: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        function updateStatusCards(status) {
            document.getElementById('totalIssues').textContent = status.total_issues || 0;
            document.getElementById('fixedIssues').textContent = status.fixed_issues || 0;
            document.getElementById('pendingIssues').textContent = status.pending_issues || 0;
            document.getElementById('successRate').textContent = (status.success_rate || 0).toFixed(1) + '%';
            
            // تحديث حالة الصحة
            const healthText = document.getElementById('healthText');
            const healthIndicator = document.getElementById('healthIndicator');
            
            healthText.textContent = status.health_status || 'غير معروف';
            
            // تحديث مؤشر الصحة
            healthIndicator.className = 'health-indicator ';
            if (status.health_status === 'جيد') {
                healthIndicator.className += 'health-good';
            } else if (status.health_status === 'يحتاج انتباه') {
                healthIndicator.className += 'health-warning';
            } else {
                healthIndicator.className += 'health-danger';
            }
            
            monitoringActive = status.monitoring || false;
        }

        function updateIssuesList(issues) {
            const container = document.getElementById('currentIssuesList');

            if (!issues || Object.keys(issues).length === 0) {
                container.innerHTML = '<div class="no-data">🎉 لا توجد مشاكل حالياً!</div>';
                return;
            }

            let html = '';
            for (const [issueType, issueData] of Object.entries(issues)) {
                const severityClass = `severity-${issueData.severity || 'medium'}`;
                const affectedCount = issueData.affected_pages ? issueData.affected_pages.length : 0;

                html += `
                    <div class="issue-item">
                        <div class="issue-header">
                            <div class="issue-title">${issueData.description || issueType}</div>
                            <div class="issue-severity ${severityClass}">${issueData.severity || 'متوسط'}</div>
                        </div>
                        <div class="issue-description">
                            تم اكتشافها في: ${new Date(issueData.detected_at).toLocaleString('ar-SA')}
                        </div>
                        <div class="affected-pages">
                            📄 الصفحات المتأثرة: ${affectedCount}
                            <button onclick="fixIssue('${issueType}')" style="margin-right: 10px; padding: 5px 10px; font-size: 0.8em;">
                                🔧 إصلاح
                            </button>
                        </div>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        function updateFixedIssuesList(fixedIssues) {
            const container = document.getElementById('fixedIssuesList');

            if (!fixedIssues || Object.keys(fixedIssues).length === 0) {
                container.innerHTML = '<div class="no-data">لم يتم إصلاح أي مشاكل بعد</div>';
                return;
            }

            let html = '';
            for (const [issueType, fixData] of Object.entries(fixedIssues)) {
                html += `
                    <div class="issue-item fixed">
                        <div class="issue-header">
                            <div class="issue-title">✅ ${fixData.issue_data.description || issueType}</div>
                            <div class="issue-severity severity-low">محلولة</div>
                        </div>
                        <div class="issue-description">
                            تم الإصلاح في: ${new Date(fixData.fixed_at).toLocaleString('ar-SA')}
                        </div>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        function updateRecommendations(recommendations) {
            const container = document.getElementById('recommendationsList');

            if (!recommendations || recommendations.length === 0) {
                container.innerHTML = '<div class="no-data">لا توجد توصيات حالياً</div>';
                return;
            }

            let html = '';
            recommendations.forEach(rec => {
                const priorityClass = `severity-${rec.priority || 'medium'}`;

                html += `
                    <div class="recommendation-item">
                        <div class="issue-header">
                            <div class="issue-title">${rec.title}</div>
                            <div class="issue-severity ${priorityClass}">${rec.priority || 'متوسط'}</div>
                        </div>
                        <div class="issue-description">${rec.description}</div>
                        <div style="margin-top: 10px; font-weight: bold; color: #27ae60;">
                            💡 الإجراء المقترح: ${rec.action}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        async function startMonitoring() {
            try {
                showMessage('🚀 بدء المراقبة...', 'loading');

                const response = await fetch('/api/start-monitoring', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage('✅ تم بدء المراقبة بنجاح', 'success');
                    monitoringActive = true;
                    refreshData();
                } else {
                    showMessage('❌ خطأ في بدء المراقبة: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function stopMonitoring() {
            try {
                showMessage('⏹️ إيقاف المراقبة...', 'loading');

                const response = await fetch('/api/stop-monitoring', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage('✅ تم إيقاف المراقبة', 'success');
                    monitoringActive = false;
                    refreshData();
                } else {
                    showMessage('❌ خطأ في إيقاف المراقبة: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function runFullScan() {
            try {
                showMessage('🔍 بدء الفحص الشامل...', 'loading');

                const response = await fetch('/api/full-scan', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage('✅ تم إكمال الفحص الشامل', 'success');
                    refreshData();
                } else {
                    showMessage('❌ خطأ في الفحص: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function autoFixIssues() {
            try {
                showMessage('🔧 بدء الإصلاح التلقائي...', 'loading');

                const response = await fetch('/api/auto-fix', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage(`✅ تم إصلاح ${data.fixed_count} مشكلة`, 'success');
                    refreshData();
                } else {
                    showMessage('❌ خطأ في الإصلاح: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function fixIssue(issueType) {
            try {
                showMessage(`🔧 إصلاح مشكلة ${issueType}...`, 'loading');

                const response = await fetch(`/api/fix-issue/${issueType}`, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage('✅ تم إصلاح المشكلة بنجاح', 'success');
                    refreshData();
                } else {
                    showMessage('❌ خطأ في الإصلاح: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function downloadReport() {
            try {
                const response = await fetch('/api/download-report');
                const blob = await response.blob();

                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `indexing_report_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                showMessage('✅ تم تحميل التقرير بنجاح', 'success');
            } catch (error) {
                showMessage('❌ خطأ في تحميل التقرير: ' + error.message, 'error');
            }
        }

        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('systemMessage');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;

            // إخفاء الرسالة بعد 5 ثوان (عدا رسائل التحميل)
            if (type !== 'loading') {
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 5000);
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/indexing-status')
def get_indexing_status():
    """الحصول على حالة الفهرسة"""
    try:
        # الحصول على مدير الفهرسة
        site_url = request.args.get('site_url', 'https://modetaris.com')
        indexing_manager = get_indexing_manager(site_url)

        if not indexing_manager:
            return jsonify({
                'success': False,
                'error': 'لم يتم تهيئة مدير الفهرسة'
            }), 500

        # الحصول على الحالة السريعة
        status = indexing_manager.get_quick_status()

        # الحصول على التقرير الشامل
        report = indexing_manager.get_indexing_report()

        # توليد التوصيات
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        recommendations = loop.run_until_complete(indexing_manager.generate_seo_recommendations())
        loop.close()

        return jsonify({
            'success': True,
            'status': status,
            'current_issues': report.get('current_issues', {}),
            'fixed_issues': report.get('fixed_issues', {}),
            'recommendations': recommendations,
            'last_updated': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على حالة الفهرسة: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/start-monitoring', methods=['POST'])
def start_monitoring():
    """بدء مراقبة الفهرسة"""
    try:
        site_url = request.json.get('site_url', 'https://modetaris.com') if request.json else 'https://modetaris.com'
        indexing_manager = get_indexing_manager(site_url)

        if not indexing_manager:
            return jsonify({
                'success': False,
                'error': 'لم يتم تهيئة مدير الفهرسة'
            }), 500

        # بدء المراقبة في خيط منفصل
        import threading

        def run_monitoring():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(indexing_manager.start_intelligent_monitoring())
            loop.close()

        monitoring_thread = threading.Thread(target=run_monitoring, daemon=True)
        monitoring_thread.start()

        logger.info("🚀 تم بدء مراقبة الفهرسة")

        return jsonify({
            'success': True,
            'message': 'تم بدء المراقبة بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في بدء المراقبة: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stop-monitoring', methods=['POST'])
def stop_monitoring():
    """إيقاف مراقبة الفهرسة"""
    try:
        indexing_manager = get_indexing_manager()

        if not indexing_manager:
            return jsonify({
                'success': False,
                'error': 'مدير الفهرسة غير متاح'
            }), 500

        # إيقاف المراقبة
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(indexing_manager.stop_monitoring())
        loop.close()

        logger.info("⏹️ تم إيقاف مراقبة الفهرسة")

        return jsonify({
            'success': True,
            'message': 'تم إيقاف المراقبة'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إيقاف المراقبة: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/full-scan', methods=['POST'])
def run_full_scan():
    """تشغيل فحص شامل للموقع"""
    try:
        indexing_manager = get_indexing_manager()

        if not indexing_manager:
            return jsonify({
                'success': False,
                'error': 'مدير الفهرسة غير متاح'
            }), 500

        # تشغيل الفحص الشامل
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(indexing_manager.comprehensive_site_analysis())
        loop.close()

        logger.info("🔍 تم إكمال الفحص الشامل")

        return jsonify({
            'success': True,
            'message': 'تم إكمال الفحص الشامل'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الفحص الشامل: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/auto-fix', methods=['POST'])
def auto_fix_issues():
    """إصلاح جميع المشاكل تلقائياً"""
    try:
        indexing_manager = get_indexing_manager()

        if not indexing_manager:
            return jsonify({
                'success': False,
                'error': 'مدير الفهرسة غير متاح'
            }), 500

        # الحصول على المشاكل الحالية
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        issues = loop.run_until_complete(indexing_manager.detect_indexing_issues())
        fixed_count = 0

        # إصلاح كل مشكلة
        for issue in issues:
            try:
                loop.run_until_complete(indexing_manager.auto_fix_indexing_issue(issue))
                fixed_count += 1
            except Exception as e:
                logger.warning(f"⚠️ فشل في إصلاح مشكلة: {e}")
                continue

        loop.close()

        logger.info(f"🔧 تم إصلاح {fixed_count} مشكلة تلقائياً")

        return jsonify({
            'success': True,
            'message': f'تم إصلاح {fixed_count} مشكلة',
            'fixed_count': fixed_count
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الإصلاح التلقائي: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/fix-issue/<issue_type>', methods=['POST'])
def fix_specific_issue(issue_type):
    """إصلاح مشكلة محددة"""
    try:
        indexing_manager = get_indexing_manager()

        if not indexing_manager:
            return jsonify({
                'success': False,
                'error': 'مدير الفهرسة غير متاح'
            }), 500

        # البحث عن المشكلة المحددة
        if issue_type not in indexing_manager.indexing_issues:
            return jsonify({
                'success': False,
                'error': 'المشكلة غير موجودة'
            }), 404

        issue = indexing_manager.indexing_issues[issue_type]

        # إصلاح المشكلة
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(indexing_manager.auto_fix_indexing_issue(issue))
        loop.close()

        logger.info(f"🔧 تم إصلاح مشكلة: {issue_type}")

        return jsonify({
            'success': True,
            'message': f'تم إصلاح مشكلة {issue_type}'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إصلاح المشكلة {issue_type}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/download-report')
def download_report():
    """تحميل تقرير شامل للفهرسة"""
    try:
        indexing_manager = get_indexing_manager()

        if not indexing_manager:
            return jsonify({
                'success': False,
                'error': 'مدير الفهرسة غير متاح'
            }), 500

        # إنشاء التقرير الشامل
        report = indexing_manager.get_indexing_report()

        # إضافة التوصيات
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        recommendations = loop.run_until_complete(indexing_manager.generate_seo_recommendations())
        loop.close()

        report['recommendations'] = recommendations
        report['generated_at'] = datetime.now().isoformat()

        # إنشاء استجابة JSON للتحميل
        from flask import Response

        response = Response(
            json.dumps(report, ensure_ascii=False, indent=2),
            mimetype='application/json',
            headers={
                'Content-Disposition': f'attachment; filename=indexing_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            }
        )

        return response

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل التقرير: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/site-health')
def get_site_health():
    """الحصول على صحة الموقع العامة"""
    try:
        indexing_manager = get_indexing_manager()

        if not indexing_manager:
            return jsonify({
                'success': False,
                'error': 'مدير الفهرسة غير متاح'
            }), 500

        status = indexing_manager.get_quick_status()

        # حساب نقاط الصحة
        health_score = 100

        if status['total_issues'] > 0:
            health_score -= status['total_issues'] * 10

        if status['success_rate'] < 80:
            health_score -= 20

        health_score = max(0, health_score)

        # تحديد مستوى الصحة
        if health_score >= 90:
            health_level = 'ممتاز'
            health_color = '#27ae60'
        elif health_score >= 70:
            health_level = 'جيد'
            health_color = '#f39c12'
        else:
            health_level = 'يحتاج تحسين'
            health_color = '#e74c3c'

        return jsonify({
            'success': True,
            'health_score': health_score,
            'health_level': health_level,
            'health_color': health_color,
            'status': status
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على صحة الموقع: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("🚀 بدء واجهة إدارة الفهرسة الذكية...")
    logger.info("🌐 الواجهة متاحة على: http://localhost:5002")

    app.run(host='0.0.0.0', port=5002, debug=False, use_reloader=False)
