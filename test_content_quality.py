#!/usr/bin/env python3
"""
أداة اختبار جودة المحتوى المولد
"""

import sys
import json
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from modules.content_generator import ContentGenerator

def test_content_quality():
    """اختبار جودة المحتوى المولد"""
    print("🧪 بدء اختبار جودة المحتوى...")
    
    # إنشاء مولد المحتوى
    generator = ContentGenerator()
    
    # محتوى اختبار
    test_content = {
        'title': 'دليل شامل للعبة Cyberpunk 2077: نصائح وحيل للمبتدئين',
        'content': 'لعبة Cyberpunk 2077 هي واحدة من أكثر الألعاب إثارة في السنوات الأخيرة. تقدم اللعبة عالماً مفتوحاً واسعاً مليئاً بالمهام والتحديات.',
        'keywords': ['Cyberpunk 2077', 'دليل', 'نصائح', 'ألعاب']
    }
    
    try:
        # اختبار النظام المحسن إذا كان متوفراً
        if hasattr(generator, '_enhanced_quality_review'):
            print("✅ استخدام نظام المراجعة المحسن")
            quality_review = generator._enhanced_quality_review(test_content)
        else:
            print("⚠️ استخدام نظام المراجعة التقليدي")
            quality_review = generator._review_article_quality(test_content)
        
        # عرض النتائج
        print(f"\n📊 نتائج مراجعة الجودة:")
        print(f"النقاط: {quality_review.get('quality_score', 'غير محدد')}/100")
        print(f"الموافقة: {'✅ نعم' if quality_review.get('approved', False) else '❌ لا'}")
        print(f"التطابق: {'✅ نعم' if quality_review.get('title_content_match', True) else '❌ لا'}")
        
        if quality_review.get('issues'):
            print(f"\n🔍 المشاكل المكتشفة:")
            for issue in quality_review['issues']:
                print(f"  - {issue}")
        
        if quality_review.get('suggestions'):
            print(f"\n💡 اقتراحات التحسين:")
            for suggestion in quality_review['suggestions']:
                print(f"  - {suggestion}")
        
        if quality_review.get('warnings'):
            print(f"\n⚠️ تحذيرات:")
            for warning in quality_review['warnings']:
                print(f"  - {warning}")
        
        return quality_review.get('approved', False)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجودة: {e}")
        return False

if __name__ == "__main__":
    success = test_content_quality()
    if success:
        print("\n✅ اختبار الجودة نجح!")
    else:
        print("\n❌ اختبار الجودة فشل!")
