#!/usr/bin/env python3
"""
إصلاح شامل لمشاكل جودة المحتوى المولد
"""

import sys
import os
import logging
import json
import re
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def enhance_content_generator_prompts():
    """تحسين prompts في مولد المحتوى"""
    try:
        content_generator_path = Path("modules/content_generator.py")
        if not content_generator_path.exists():
            logger.error("❌ ملف content_generator.py غير موجود")
            return False
        
        with open(content_generator_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحسين البرومبت الأساسي
        enhanced_prompt_section = '''
    def _build_enhanced_article_prompt(self, source_content: Dict, content_type: str, dialect: str, search_results: str = "") -> str:
        """بناء برومبت محسن لضمان التطابق بين العنوان والمحتوى"""
        
        # استخراج المعلومات الأساسية
        original_title = source_content.get('title', '')
        original_content = source_content.get('content', '')
        keywords = source_content.get('keywords', [])
        
        # تحليل العنوان لاستخراج المفاهيم الرئيسية
        title_concepts = self._extract_title_concepts(original_title)
        
        # بناء البرومبت المحسن
        enhanced_prompt = f"""
أنت كاتب محتوى احترافي متخصص في أخبار الألعاب. مهمتك كتابة مقال عالي الجودة يحقق التطابق الكامل بين العنوان والمحتوى.

**قواعد التطابق الإجبارية:**
1. يجب أن يغطي المحتوى جميع المفاهيم المذكورة في العنوان
2. لا تضع عنواناً يعد بمعلومات غير موجودة في المحتوى
3. إذا كان العنوان يذكر "دليل"، يجب أن يحتوي المحتوى على خطوات واضحة
4. إذا كان العنوان يذكر "مراجعة"، يجب أن يحتوي المحتوى على تقييم فعلي
5. إذا كان العنوان يذكر لعبة معينة، يجب أن يكون المحتوى عنها بالكامل

**المحتوى المصدر:**
العنوان الأصلي: {original_title}
المحتوى الأصلي: {original_content[:1000]}...
الكلمات المفتاحية: {', '.join(keywords)}

**المفاهيم الرئيسية التي يجب تغطيتها:**
{', '.join(title_concepts)}

**نتائج البحث الإضافية:**
{search_results}

**المطلوب:**
اكتب مقالاً باللهجة {dialect} يحتوي على:

1. **عنوان جذاب ودقيق** (30-60 حرف):
   - يعكس المحتوى الفعلي بدقة 100%
   - لا يعد بمعلومات غير موجودة
   - يحتوي على كلمات مفتاحية مهمة

2. **محتوى شامل** (800-1200 كلمة):
   - يغطي جميع المفاهيم المذكورة في العنوان
   - يحتوي على معلومات دقيقة ومفيدة
   - مكتوب بأسلوب شيق وسهل القراءة
   - يتضمن أسماء الألعاب بالإنجليزية والعربية

3. **ملخص قصير** (100-150 كلمة):
   - يلخص المحتوى الرئيسي
   - يحتوي على الكلمات المفتاحية

4. **كلمات مفتاحية** (5-8 كلمات):
   - مرتبطة بالمحتوى الفعلي
   - تساعد في SEO

5. **دعوة للعمل**:
   - تشجع القراء على التفاعل

**تحذير مهم:** سيتم رفض المقال إذا لم يحقق التطابق الكامل بين العنوان والمحتوى.

أرجع النتيجة بصيغة JSON:
{{
    "title": "العنوان المحسن",
    "content": "المحتوى الكامل",
    "summary": "الملخص",
    "keywords": ["كلمة1", "كلمة2", ...],
    "call_to_action": "دعوة للعمل"
}}
"""
        return enhanced_prompt
    
    def _extract_title_concepts(self, title: str) -> list:
        """استخراج المفاهيم الرئيسية من العنوان"""
        concepts = []
        
        # كلمات مفتاحية مهمة
        important_keywords = [
            'دليل', 'مراجعة', 'تحديث', 'إصدار', 'لعبة', 'ألعاب',
            'نصائح', 'حيل', 'استراتيجية', 'مقارنة', 'أفضل',
            'جديد', 'قادم', 'مجاني', 'مدفوع', 'تحميل'
        ]
        
        title_lower = title.lower()
        
        # استخراج الكلمات المهمة
        for keyword in important_keywords:
            if keyword in title_lower:
                concepts.append(keyword)
        
        # استخراج أسماء الألعاب (كلمات بالإنجليزية)
        english_words = re.findall(r'\\b[A-Z][a-zA-Z]+\\b', title)
        concepts.extend(english_words)
        
        # استخراج الأرقام (إصدارات، سنوات، إلخ)
        numbers = re.findall(r'\\d+', title)
        concepts.extend(numbers)
        
        return list(set(concepts))  # إزالة التكرار
'''
        
        # إضافة الدوال المحسنة إذا لم تكن موجودة
        if "_build_enhanced_article_prompt" not in content:
            # البحث عن مكان مناسب للإضافة
            if "def _build_article_prompt" in content:
                content = content.replace(
                    "def _build_article_prompt",
                    enhanced_prompt_section + "\n    def _build_article_prompt"
                )
            else:
                # إضافة في نهاية الكلاس
                content = content.replace(
                    "class ContentGenerator:",
                    f"class ContentGenerator:{enhanced_prompt_section}"
                )
        
        # حفظ الملف
        with open(content_generator_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ تم تحسين prompts في مولد المحتوى")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في تحسين prompts: {e}")
        return False

def enhance_quality_review_system():
    """تحسين نظام مراجعة الجودة"""
    try:
        content_generator_path = Path("modules/content_generator.py")
        if not content_generator_path.exists():
            logger.error("❌ ملف content_generator.py غير موجود")
            return False
        
        with open(content_generator_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحسين دالة مراجعة الجودة
        enhanced_quality_review = '''
    def _enhanced_quality_review(self, article: Dict) -> Dict:
        """مراجعة جودة محسنة مع فحص دقيق للتطابق"""
        issues = []
        suggestions = []
        warnings = []

        title = article.get('title', '')
        content = article.get('content', '')
        
        # 1. فحص التطابق المحسن بين العنوان والمحتوى
        title_content_analysis = self._analyze_title_content_match(title, content)
        
        if not title_content_analysis['match']:
            issues.append(f"Title-Content Mismatch - Missing: {', '.join(title_content_analysis['missing_concepts'])}")
            suggestions.append("تأكد من أن المحتوى يغطي جميع المواضيع المذكورة في العنوان")
            warnings.append("هذا المقال سيتم رفضه بسبب عدم التطابق")
        
        # 2. فحص جودة العنوان
        title_quality = self._analyze_title_quality(title)
        if title_quality['score'] < 7:
            issues.extend(title_quality['issues'])
            suggestions.extend(title_quality['suggestions'])
        
        # 3. فحص جودة المحتوى
        content_quality = self._analyze_content_quality(content)
        if content_quality['score'] < 7:
            issues.extend(content_quality['issues'])
            suggestions.extend(content_quality['suggestions'])
        
        # 4. فحص SEO
        seo_analysis = self._analyze_seo_quality(article)
        if seo_analysis['score'] < 6:
            issues.extend(seo_analysis['issues'])
            suggestions.extend(seo_analysis['suggestions'])
        
        # حساب النقاط النهائية
        base_score = 100
        
        # خصم كبير لعدم التطابق
        if not title_content_analysis['match']:
            base_score -= 50
        
        # خصم للمشاكل الأخرى
        base_score -= len(issues) * 5
        
        final_score = max(0, base_score)
        
        # قرار الموافقة
        approved = (
            title_content_analysis['match'] and  # يجب أن يكون هناك تطابق
            final_score >= 70 and  # نقاط كافية
            len(issues) <= 2  # مشاكل قليلة
        )
        
        return {
            'quality_score': final_score,
            'issues': issues,
            'suggestions': suggestions,
            'warnings': warnings,
            'approved': approved,
            'title_content_match': title_content_analysis['match'],
            'detailed_analysis': {
                'title_content': title_content_analysis,
                'title_quality': title_quality,
                'content_quality': content_quality,
                'seo_analysis': seo_analysis
            }
        }
    
    def _analyze_title_content_match(self, title: str, content: str) -> Dict:
        """تحليل دقيق للتطابق بين العنوان والمحتوى"""
        title_lower = title.lower()
        content_lower = content.lower()
        
        # استخراج المفاهيم من العنوان
        concepts = self._extract_title_concepts(title)
        
        missing_concepts = []
        found_concepts = []
        
        for concept in concepts:
            concept_lower = concept.lower()
            
            # فحص وجود المفهوم في المحتوى
            if concept_lower in content_lower:
                found_concepts.append(concept)
            else:
                # فحص مرادفات أو أشكال مختلفة
                synonyms = self._get_concept_synonyms(concept_lower)
                found_synonym = False
                
                for synonym in synonyms:
                    if synonym in content_lower:
                        found_concepts.append(concept)
                        found_synonym = True
                        break
                
                if not found_synonym:
                    missing_concepts.append(concept)
        
        # حساب نسبة التطابق
        if concepts:
            match_ratio = len(found_concepts) / len(concepts)
        else:
            match_ratio = 1.0  # إذا لم توجد مفاهيم محددة
        
        return {
            'match': match_ratio >= 0.8,  # 80% على الأقل
            'match_ratio': match_ratio,
            'total_concepts': len(concepts),
            'found_concepts': found_concepts,
            'missing_concepts': missing_concepts
        }
    
    def _get_concept_synonyms(self, concept: str) -> list:
        """الحصول على مرادفات المفهوم"""
        synonyms_map = {
            'دليل': ['شرح', 'تعليم', 'كيفية', 'طريقة', 'خطوات'],
            'مراجعة': ['تقييم', 'رأي', 'تحليل', 'نقد'],
            'تحديث': ['إصدار', 'نسخة', 'تطوير', 'تحسين'],
            'نصائح': ['حيل', 'أسرار', 'استراتيجيات', 'طرق'],
            'أفضل': ['أحسن', 'أجمل', 'أقوى', 'أروع'],
            'جديد': ['حديث', 'أحدث', 'جديدة', 'حديثة'],
            'مجاني': ['مجانية', 'بلا مقابل', 'مفتوح'],
        }
        
        return synonyms_map.get(concept, [])
    
    def _analyze_title_quality(self, title: str) -> Dict:
        """تحليل جودة العنوان"""
        issues = []
        suggestions = []
        score = 10
        
        # فحص الطول
        if len(title) < 20:
            issues.append("العنوان قصير جداً")
            suggestions.append("اجعل العنوان أكثر وصفية")
            score -= 2
        elif len(title) > 80:
            issues.append("العنوان طويل جداً")
            suggestions.append("اختصر العنوان")
            score -= 1
        
        # فحص وجود كلمات جذابة
        attractive_words = ['أفضل', 'جديد', 'حصري', 'مجاني', 'سري', 'مذهل']
        if not any(word in title for word in attractive_words):
            suggestions.append("أضف كلمات جذابة للعنوان")
            score -= 1
        
        # فحص وجود أسماء ألعاب
        english_games = re.findall(r'\\b[A-Z][a-zA-Z\\s]+\\b', title)
        if not english_games:
            suggestions.append("أضف اسم اللعبة بالإنجليزية")
            score -= 1
        
        return {
            'score': max(0, score),
            'issues': issues,
            'suggestions': suggestions
        }
    
    def _analyze_content_quality(self, content: str) -> Dict:
        """تحليل جودة المحتوى"""
        issues = []
        suggestions = []
        score = 10
        
        # فحص الطول
        if len(content) < 500:
            issues.append("المحتوى قصير جداً")
            suggestions.append("أضف المزيد من التفاصيل")
            score -= 3
        elif len(content) > 2000:
            suggestions.append("المحتوى طويل، تأكد من أنه مفيد")
        
        # فحص التنوع في الجمل
        sentences = content.split('.')
        if len(sentences) < 5:
            issues.append("قلة الجمل")
            suggestions.append("قسم المحتوى لجمل أكثر")
            score -= 1
        
        # فحص وجود دعوة للعمل
        cta_keywords = ['شاركنا', 'رأيك', 'تعليق', 'ما رأيكم']
        if not any(keyword in content for keyword in cta_keywords):
            suggestions.append("أضف دعوة للقراء للتفاعل")
            score -= 1
        
        return {
            'score': max(0, score),
            'issues': issues,
            'suggestions': suggestions
        }
    
    def _analyze_seo_quality(self, article: Dict) -> Dict:
        """تحليل جودة SEO"""
        issues = []
        suggestions = []
        score = 10
        
        title = article.get('title', '')
        content = article.get('content', '')
        keywords = article.get('keywords', [])
        
        # فحص الكلمات المفتاحية
        if len(keywords) < 3:
            issues.append("قلة الكلمات المفتاحية")
            suggestions.append("أضف المزيد من الكلمات المفتاحية")
            score -= 2
        
        # فحص وجود الكلمات المفتاحية في المحتوى
        keyword_density = 0
        for keyword in keywords:
            if keyword.lower() in content.lower():
                keyword_density += 1
        
        if keyword_density < len(keywords) * 0.5:
            issues.append("الكلمات المفتاحية غير موجودة في المحتوى")
            suggestions.append("تأكد من وجود الكلمات المفتاحية في المحتوى")
            score -= 2
        
        return {
            'score': max(0, score),
            'issues': issues,
            'suggestions': suggestions
        }
'''
        
        # إضافة الدوال المحسنة
        if "_enhanced_quality_review" not in content:
            # البحث عن مكان مناسب للإضافة
            if "def _review_article_quality" in content:
                content = content.replace(
                    "def _review_article_quality",
                    enhanced_quality_review + "\n    def _review_article_quality"
                )
        
        # حفظ الملف
        with open(content_generator_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ تم تحسين نظام مراجعة الجودة")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في تحسين نظام مراجعة الجودة: {e}")
        return False

def create_content_quality_tester():
    """إنشاء أداة اختبار جودة المحتوى"""
    tester_code = '''#!/usr/bin/env python3
"""
أداة اختبار جودة المحتوى المولد
"""

import sys
import json
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from modules.content_generator import ContentGenerator

def test_content_quality():
    """اختبار جودة المحتوى المولد"""
    print("🧪 بدء اختبار جودة المحتوى...")
    
    # إنشاء مولد المحتوى
    generator = ContentGenerator()
    
    # محتوى اختبار
    test_content = {
        'title': 'دليل شامل للعبة Cyberpunk 2077: نصائح وحيل للمبتدئين',
        'content': 'لعبة Cyberpunk 2077 هي واحدة من أكثر الألعاب إثارة في السنوات الأخيرة. تقدم اللعبة عالماً مفتوحاً واسعاً مليئاً بالمهام والتحديات.',
        'keywords': ['Cyberpunk 2077', 'دليل', 'نصائح', 'ألعاب']
    }
    
    try:
        # اختبار النظام المحسن إذا كان متوفراً
        if hasattr(generator, '_enhanced_quality_review'):
            print("✅ استخدام نظام المراجعة المحسن")
            quality_review = generator._enhanced_quality_review(test_content)
        else:
            print("⚠️ استخدام نظام المراجعة التقليدي")
            quality_review = generator._review_article_quality(test_content)
        
        # عرض النتائج
        print(f"\\n📊 نتائج مراجعة الجودة:")
        print(f"النقاط: {quality_review.get('quality_score', 'غير محدد')}/100")
        print(f"الموافقة: {'✅ نعم' if quality_review.get('approved', False) else '❌ لا'}")
        print(f"التطابق: {'✅ نعم' if quality_review.get('title_content_match', True) else '❌ لا'}")
        
        if quality_review.get('issues'):
            print(f"\\n🔍 المشاكل المكتشفة:")
            for issue in quality_review['issues']:
                print(f"  - {issue}")
        
        if quality_review.get('suggestions'):
            print(f"\\n💡 اقتراحات التحسين:")
            for suggestion in quality_review['suggestions']:
                print(f"  - {suggestion}")
        
        if quality_review.get('warnings'):
            print(f"\\n⚠️ تحذيرات:")
            for warning in quality_review['warnings']:
                print(f"  - {warning}")
        
        return quality_review.get('approved', False)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجودة: {e}")
        return False

if __name__ == "__main__":
    success = test_content_quality()
    if success:
        print("\\n✅ اختبار الجودة نجح!")
    else:
        print("\\n❌ اختبار الجودة فشل!")
'''
    
    try:
        with open("test_content_quality.py", 'w', encoding='utf-8') as f:
            f.write(tester_code)
        logger.info("✅ تم إنشاء أداة اختبار جودة المحتوى")
        return True
    except Exception as e:
        logger.error(f"❌ فشل في إنشاء أداة الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية لإصلاح مشاكل جودة المحتوى"""
    logger.info("🚀 بدء إصلاح مشاكل جودة المحتوى المولد...")
    
    success_count = 0
    total_steps = 3
    
    # 1. تحسين prompts
    logger.info("\\n🎯 الخطوة 1: تحسين prompts في مولد المحتوى...")
    if enhance_content_generator_prompts():
        success_count += 1
    
    # 2. تحسين نظام مراجعة الجودة
    logger.info("\\n🔍 الخطوة 2: تحسين نظام مراجعة الجودة...")
    if enhance_quality_review_system():
        success_count += 1
    
    # 3. إنشاء أداة اختبار
    logger.info("\\n🧪 الخطوة 3: إنشاء أداة اختبار جودة المحتوى...")
    if create_content_quality_tester():
        success_count += 1
    
    # النتيجة النهائية
    logger.info(f"\\n🎯 اكتمل الإصلاح: {success_count}/{total_steps} خطوات نجحت")
    
    if success_count == total_steps:
        logger.info("✅ تم إصلاح جميع مشاكل جودة المحتوى بنجاح!")
        logger.info("🔄 يرجى إعادة تشغيل الوكيل لتطبيق التحديثات")
        logger.info("🧪 يمكنك اختبار التحسينات باستخدام: python test_content_quality.py")
    else:
        logger.warning(f"⚠️ تم إصلاح {success_count} من أصل {total_steps} مشاكل")
        logger.info("📋 يرجى مراجعة الأخطاء أعلاه وإصلاحها يدوياً")

if __name__ == "__main__":
    main()
