# تكامل نظام البحث الذكي مع النظام الحالي
import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# استيراد النظام الجديد
from modules.unified_intelligent_search import (
    unified_intelligent_search,
    UnifiedSearchRequest,
    SearchMode,
    SearchPriority,
    SearchContext
)

# استيراد النظام الحالي
from modules.enhanced_search_manager import enhanced_search_manager
from modules.smart_search_manager import smart_search_manager
from modules.logger import logger

class IntelligentSearchIntegrator:
    """فئة تكامل النظام الذكي مع النظام الحالي"""
    
    def __init__(self):
        self.integration_config = {
            'use_intelligent_search': True,
            'fallback_to_legacy': True,
            'performance_threshold': 10.0,  # ثواني
            'quality_threshold': 0.6,
            'enable_comparison': True
        }
        
        self.integration_stats = {
            'intelligent_searches': 0,
            'legacy_searches': 0,
            'fallback_uses': 0,
            'performance_comparisons': [],
            'quality_comparisons': []
        }
        
        logger.info("🔗 تم تهيئة مكامل البحث الذكي")
    
    async def integrated_search(self, 
                              query: str, 
                              max_results: int = 10,
                              mode: str = "intelligent",
                              priority: str = "normal") -> Dict[str, Any]:
        """البحث المتكامل الرئيسي"""
        
        try:
            # تحويل المعاملات
            search_mode = self._convert_mode(mode)
            search_priority = self._convert_priority(priority)
            
            # محاولة البحث الذكي أولاً
            if self.integration_config['use_intelligent_search']:
                intelligent_result = await self._try_intelligent_search(
                    query, max_results, search_mode, search_priority
                )
                
                if intelligent_result and self._is_result_acceptable(intelligent_result):
                    self.integration_stats['intelligent_searches'] += 1
                    return self._format_intelligent_result(intelligent_result)
            
            # العودة للنظام التقليدي إذا فشل الذكي
            if self.integration_config['fallback_to_legacy']:
                logger.info("🔄 العودة للنظام التقليدي")
                legacy_result = await self._try_legacy_search(query, max_results)
                
                if legacy_result:
                    self.integration_stats['legacy_searches'] += 1
                    self.integration_stats['fallback_uses'] += 1
                    return self._format_legacy_result(legacy_result, query)
            
            # إذا فشل كل شيء
            return {
                'success': False,
                'results': [],
                'message': 'فشل في جميع أنظمة البحث',
                'search_method': 'failed',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث المتكامل: {e}")
            return {
                'success': False,
                'results': [],
                'message': f'خطأ في البحث: {str(e)}',
                'search_method': 'error',
                'timestamp': datetime.now().isoformat()
            }
    
    async def _try_intelligent_search(self, 
                                    query: str, 
                                    max_results: int,
                                    mode: SearchMode,
                                    priority: SearchPriority) -> Optional[Any]:
        """محاولة البحث الذكي"""
        try:
            request = UnifiedSearchRequest(
                query=query,
                mode=mode,
                priority=priority,
                max_results=max_results,
                timeout=self.integration_config['performance_threshold']
            )
            
            result = await unified_intelligent_search.search(request)
            return result
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث الذكي: {e}")
            return None
    
    async def _try_legacy_search(self, query: str, max_results: int) -> Optional[Dict]:
        """محاولة البحث التقليدي"""
        try:
            # محاولة النظام المحسن أولاً
            if enhanced_search_manager:
                result = await enhanced_search_manager.comprehensive_search(query, max_results)
                if result and result.get('results'):
                    return result
            
            # محاولة النظام الذكي القديم
            if smart_search_manager:
                from modules.smart_search_manager import SearchRequest, SearchPriority as LegacyPriority
                
                legacy_request = SearchRequest(
                    query=query,
                    max_results=max_results,
                    priority=LegacyPriority.FREE
                )
                
                results = await smart_search_manager.search(legacy_request)
                if results:
                    return {
                        'results': [result.__dict__ for result in results],
                        'total_results': len(results),
                        'search_method': 'smart_legacy'
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث التقليدي: {e}")
            return None
    
    def _is_result_acceptable(self, result: Any) -> bool:
        """فحص ما إذا كانت النتيجة مقبولة"""
        if not result:
            return False
        
        # فحص الأداء
        if result.execution_time > self.integration_config['performance_threshold']:
            logger.warning(f"⚠️ أداء بطيء: {result.execution_time:.2f}ث")
            return False
        
        # فحص الجودة
        if result.quality_score < self.integration_config['quality_threshold']:
            logger.warning(f"⚠️ جودة منخفضة: {result.quality_score:.2f}")
            return False
        
        # فحص وجود نتائج
        if result.total_results == 0:
            logger.warning("⚠️ لا توجد نتائج")
            return False
        
        return True
    
    def _format_intelligent_result(self, result: Any) -> Dict[str, Any]:
        """تنسيق نتيجة البحث الذكي"""
        return {
            'success': True,
            'results': result.results,
            'total_results': result.total_results,
            'execution_time': result.execution_time,
            'quality_score': result.quality_score,
            'relevance_score': result.relevance_score,
            'search_method': 'intelligent',
            'search_strategy': result.search_strategy,
            'engines_used': result.engines_used,
            'recommendations': result.recommendations,
            'metadata': result.metadata,
            'timestamp': datetime.now().isoformat()
        }
    
    def _format_legacy_result(self, result: Dict, query: str) -> Dict[str, Any]:
        """تنسيق نتيجة البحث التقليدي"""
        return {
            'success': True,
            'results': result.get('results', []),
            'total_results': result.get('total_results', len(result.get('results', []))),
            'execution_time': result.get('execution_time', 0),
            'quality_score': result.get('quality_score', 0.5),
            'relevance_score': result.get('relevance_score', 0.5),
            'search_method': result.get('search_method', 'legacy'),
            'search_strategy': 'legacy_fallback',
            'engines_used': result.get('engines_used', ['legacy']),
            'recommendations': ['تم استخدام النظام التقليدي'],
            'metadata': {
                'query': query,
                'fallback_reason': 'intelligent_search_failed'
            },
            'timestamp': datetime.now().isoformat()
        }
    
    def _convert_mode(self, mode: str) -> SearchMode:
        """تحويل نمط البحث"""
        mode_mapping = {
            'quick': SearchMode.QUICK,
            'fast': SearchMode.QUICK,
            'comprehensive': SearchMode.COMPREHENSIVE,
            'detailed': SearchMode.COMPREHENSIVE,
            'intelligent': SearchMode.INTELLIGENT,
            'smart': SearchMode.INTELLIGENT,
            'semantic': SearchMode.SEMANTIC,
            'adaptive': SearchMode.ADAPTIVE,
            'expert': SearchMode.EXPERT
        }
        
        return mode_mapping.get(mode.lower(), SearchMode.INTELLIGENT)
    
    def _convert_priority(self, priority: str) -> SearchPriority:
        """تحويل أولوية البحث"""
        priority_mapping = {
            'low': SearchPriority.LOW,
            'normal': SearchPriority.NORMAL,
            'high': SearchPriority.HIGH,
            'urgent': SearchPriority.URGENT,
            'critical': SearchPriority.CRITICAL
        }
        
        return priority_mapping.get(priority.lower(), SearchPriority.NORMAL)
    
    async def compare_search_methods(self, query: str, max_results: int = 10) -> Dict[str, Any]:
        """مقارنة طرق البحث المختلفة"""
        if not self.integration_config['enable_comparison']:
            return {'comparison_disabled': True}
        
        comparison_results = {
            'query': query,
            'intelligent_result': None,
            'legacy_result': None,
            'comparison': {},
            'recommendation': ''
        }
        
        try:
            # تشغيل البحث الذكي
            intelligent_start = asyncio.get_event_loop().time()
            intelligent_result = await self._try_intelligent_search(
                query, max_results, SearchMode.INTELLIGENT, SearchPriority.NORMAL
            )
            intelligent_time = asyncio.get_event_loop().time() - intelligent_start
            
            # تشغيل البحث التقليدي
            legacy_start = asyncio.get_event_loop().time()
            legacy_result = await self._try_legacy_search(query, max_results)
            legacy_time = asyncio.get_event_loop().time() - legacy_start
            
            # تسجيل النتائج
            if intelligent_result:
                comparison_results['intelligent_result'] = {
                    'execution_time': intelligent_time,
                    'results_count': intelligent_result.total_results,
                    'quality_score': intelligent_result.quality_score,
                    'relevance_score': intelligent_result.relevance_score
                }
            
            if legacy_result:
                comparison_results['legacy_result'] = {
                    'execution_time': legacy_time,
                    'results_count': len(legacy_result.get('results', [])),
                    'quality_score': legacy_result.get('quality_score', 0.5),
                    'relevance_score': legacy_result.get('relevance_score', 0.5)
                }
            
            # إجراء المقارنة
            comparison_results['comparison'] = self._analyze_comparison(
                comparison_results['intelligent_result'],
                comparison_results['legacy_result']
            )
            
            # حفظ في الإحصائيات
            if comparison_results['intelligent_result'] and comparison_results['legacy_result']:
                self.integration_stats['performance_comparisons'].append({
                    'intelligent_time': intelligent_time,
                    'legacy_time': legacy_time,
                    'timestamp': datetime.now().isoformat()
                })
                
                self.integration_stats['quality_comparisons'].append({
                    'intelligent_quality': intelligent_result.quality_score if intelligent_result else 0,
                    'legacy_quality': legacy_result.get('quality_score', 0) if legacy_result else 0,
                    'timestamp': datetime.now().isoformat()
                })
            
            return comparison_results
            
        except Exception as e:
            logger.error(f"❌ فشل في مقارنة طرق البحث: {e}")
            comparison_results['error'] = str(e)
            return comparison_results
    
    def _analyze_comparison(self, intelligent: Optional[Dict], legacy: Optional[Dict]) -> Dict[str, Any]:
        """تحليل المقارنة بين الطرق"""
        if not intelligent or not legacy:
            return {'insufficient_data': True}
        
        analysis = {}
        
        # مقارنة الأداء
        if intelligent['execution_time'] < legacy['execution_time']:
            analysis['performance_winner'] = 'intelligent'
            analysis['performance_improvement'] = (
                (legacy['execution_time'] - intelligent['execution_time']) / 
                legacy['execution_time'] * 100
            )
        else:
            analysis['performance_winner'] = 'legacy'
            analysis['performance_improvement'] = (
                (intelligent['execution_time'] - legacy['execution_time']) / 
                intelligent['execution_time'] * 100
            )
        
        # مقارنة الجودة
        if intelligent['quality_score'] > legacy['quality_score']:
            analysis['quality_winner'] = 'intelligent'
            analysis['quality_improvement'] = (
                intelligent['quality_score'] - legacy['quality_score']
            ) * 100
        else:
            analysis['quality_winner'] = 'legacy'
            analysis['quality_improvement'] = (
                legacy['quality_score'] - intelligent['quality_score']
            ) * 100
        
        # مقارنة عدد النتائج
        analysis['results_comparison'] = {
            'intelligent_count': intelligent['results_count'],
            'legacy_count': legacy['results_count'],
            'difference': intelligent['results_count'] - legacy['results_count']
        }
        
        # التوصية العامة
        intelligent_score = (
            (1 if analysis['performance_winner'] == 'intelligent' else 0) +
            (1 if analysis['quality_winner'] == 'intelligent' else 0) +
            (1 if intelligent['results_count'] >= legacy['results_count'] else 0)
        )
        
        if intelligent_score >= 2:
            analysis['recommendation'] = 'استخدم النظام الذكي'
        else:
            analysis['recommendation'] = 'استخدم النظام التقليدي'
        
        return analysis
    
    def get_integration_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التكامل"""
        total_searches = (
            self.integration_stats['intelligent_searches'] + 
            self.integration_stats['legacy_searches']
        )
        
        stats = {
            'total_searches': total_searches,
            'intelligent_usage_rate': (
                self.integration_stats['intelligent_searches'] / max(total_searches, 1)
            ),
            'legacy_usage_rate': (
                self.integration_stats['legacy_searches'] / max(total_searches, 1)
            ),
            'fallback_rate': (
                self.integration_stats['fallback_uses'] / max(total_searches, 1)
            ),
            'performance_comparisons_count': len(self.integration_stats['performance_comparisons']),
            'quality_comparisons_count': len(self.integration_stats['quality_comparisons'])
        }
        
        # حساب متوسط التحسينات
        if self.integration_stats['performance_comparisons']:
            avg_intelligent_time = sum(
                c['intelligent_time'] for c in self.integration_stats['performance_comparisons']
            ) / len(self.integration_stats['performance_comparisons'])
            
            avg_legacy_time = sum(
                c['legacy_time'] for c in self.integration_stats['performance_comparisons']
            ) / len(self.integration_stats['performance_comparisons'])
            
            stats['average_performance_improvement'] = (
                (avg_legacy_time - avg_intelligent_time) / avg_legacy_time * 100
                if avg_legacy_time > 0 else 0
            )
        
        if self.integration_stats['quality_comparisons']:
            avg_intelligent_quality = sum(
                c['intelligent_quality'] for c in self.integration_stats['quality_comparisons']
            ) / len(self.integration_stats['quality_comparisons'])
            
            avg_legacy_quality = sum(
                c['legacy_quality'] for c in self.integration_stats['quality_comparisons']
            ) / len(self.integration_stats['quality_comparisons'])
            
            stats['average_quality_improvement'] = (
                (avg_intelligent_quality - avg_legacy_quality) * 100
            )
        
        return stats

# إنشاء مثيل عام
intelligent_search_integrator = IntelligentSearchIntegrator()

# دوال مساعدة للتكامل السهل
async def search_with_intelligence(query: str, **kwargs) -> Dict[str, Any]:
    """دالة بحث مبسطة مع الذكاء الاصطناعي"""
    return await intelligent_search_integrator.integrated_search(query, **kwargs)

async def compare_search_performance(query: str, max_results: int = 10) -> Dict[str, Any]:
    """دالة مقارنة أداء البحث"""
    return await intelligent_search_integrator.compare_search_methods(query, max_results)
