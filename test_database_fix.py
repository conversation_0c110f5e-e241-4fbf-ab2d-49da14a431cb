#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح قاعدة البيانات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.database import db
from modules.logger import logger

def test_database_operations():
    """اختبار عمليات قاعدة البيانات الأساسية"""
    
    print("🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        # اختبار 1: الحصول على المقالات الحديثة
        print("1️⃣ اختبار get_recent_articles...")
        recent_articles = db.get_recent_articles(limit=5)
        print(f"✅ تم العثور على {len(recent_articles)} مقال")
        
        # اختبار 2: فحص التكرار
        print("2️⃣ اختبار is_duplicate_content...")
        test_content = "هذا محتوى تجريبي للاختبار"
        test_title = "عنوان تجريبي"
        test_keywords = ["اختبار", "تجريبي"]
        
        is_duplicate, reason = db.is_duplicate_content(test_content, test_title, test_keywords)
        print(f"✅ نتيجة فحص التكرار: {is_duplicate} - {reason}")
        
        # اختبار 3: إضافة مقال تجريبي
        print("3️⃣ اختبار save_article...")
        test_article = {
            'title': 'مقال اختبار قاعدة البيانات',
            'content': 'محتوى تجريبي للاختبار',
            'source_url': 'https://test.com',
            'source_type': 'test',
            'keywords': ['اختبار', 'قاعدة بيانات'],
            'category': 'اختبار',
            'dialect': 'egyptian'
        }
        
        article_id = db.save_article(test_article)
        if article_id:
            print(f"✅ تم حفظ المقال بنجاح - ID: {article_id}")
        else:
            print("⚠️ لم يتم حفظ المقال (ربما مكرر)")
        
        # اختبار 4: الحصول على إحصائيات
        print("4️⃣ اختبار get_stats_summary...")
        stats = db.get_stats_summary(days=7)
        print(f"✅ إحصائيات آخر 7 أيام: {stats}")
        
        print("🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ فشل في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_personality():
    """اختبار شخصية أليكس"""
    print("\n🤖 اختبار شخصية أليكس...")
    
    try:
        from modules.ai_personality import ai_personality
        
        # اختبار اتخاذ قرار
        decision = ai_personality.make_strategic_decision("content_selection", {
            'available_sources': ['newsdata', 'youtube', 'websites'],
            'recent_performance': {'success_rate': 0.8}
        })
        
        print(f"✅ قرار أليكس: {decision.get('decision', 'غير محدد')}")
        print(f"📝 السبب: {decision.get('reasoning', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار أليكس: {e}")
        return False

def test_content_scraper():
    """اختبار مستخرج المحتوى"""
    print("\n📰 اختبار مستخرج المحتوى...")
    
    try:
        from modules.content_scraper import ContentScraper
        
        scraper = ContentScraper()
        
        # اختبار بحث بسيط
        print("🔍 اختبار البحث...")
        results = scraper.search_enhanced("gaming news", num_results=2)
        
        print(f"✅ تم العثور على {len(results)} نتيجة")
        
        if results:
            print(f"📰 أول نتيجة: {results[0].get('title', 'بدون عنوان')}")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار مستخرج المحتوى: {e}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار شامل لإصلاحات الوكيل")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # اختبار قاعدة البيانات
    if test_database_operations():
        success_count += 1
    
    # اختبار شخصية أليكس
    if test_ai_personality():
        success_count += 1
    
    # اختبار مستخرج المحتوى
    if test_content_scraper():
        success_count += 1
    
    print(f"\n📊 النتائج النهائية: {success_count}/{total_tests} اختبارات نجحت")
    
    if success_count == total_tests:
        print("🎉 جميع الاختبارات نجحت! الوكيل جاهز للعمل")
    else:
        print("⚠️ بعض الاختبارات فشلت، يرجى مراجعة الأخطاء")
