#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لنظام Whisper المحسن
Quick Test for Enhanced Whisper System
"""

import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system():
    """اختبار سريع للنظام"""
    print("🧪 اختبار سريع لنظام Whisper المحسن")
    print("=" * 50)
    
    try:
        # اختبار 1: تحميل النظام المحسن
        print("1️⃣ اختبار تحميل النظام المحسن...")
        from modules.enhanced_whisper_manager import enhanced_whisper_manager
        print("✅ تم تحميل النظام المحسن بنجاح")
        
        # اختبار 2: فحص الإعدادات
        print("\n2️⃣ فحص الإعدادات...")
        print(f"🔗 API URL: {enhanced_whisper_manager.whisper_api_url}")
        print(f"🔑 API Key: {enhanced_whisper_manager.whisper_api_key[:10]}...")
        print(f"📊 طرق الرفع المتاحة: {len(enhanced_whisper_manager.upload_methods)}")
        
        # اختبار 3: فحص واجهة الويب
        print("\n3️⃣ فحص واجهة الويب...")
        if os.path.exists('whisper_web_interface.py'):
            print("✅ واجهة الويب متوفرة")
            
            # محاولة استيراد واجهة الويب
            try:
                import whisper_web_interface
                print("✅ يمكن استيراد واجهة الويب")
            except Exception as import_error:
                print(f"⚠️ مشكلة في استيراد واجهة الويب: {import_error}")
        else:
            print("❌ واجهة الويب غير متوفرة")
        
        # اختبار 4: فحص ملفات التكامل
        print("\n4️⃣ فحص ملفات التكامل...")
        integration_files = [
            'integrate_enhanced_whisper.py',
            'start_enhanced_whisper.py',
            'ENHANCED_WHISPER_README.md'
        ]
        
        for file in integration_files:
            if os.path.exists(file):
                print(f"✅ {file}")
            else:
                print(f"❌ {file}")
        
        # اختبار 5: فحص الإحصائيات
        print("\n5️⃣ فحص الإحصائيات...")
        stats = enhanced_whisper_manager.get_transcription_stats()
        print(f"📊 إجمالي التحويلات: {stats.get('total_transcriptions', 0)}")
        print(f"📈 معدل النجاح: {stats.get('success_rate', 0):.1f}%")
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز للاستخدام")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        return False

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n" + "🎤" * 50)
    print("📋 تعليمات الاستخدام")
    print("🎤" * 50)
    
    print("\n🚀 طرق التشغيل:")
    print("1. تشغيل النظام الكامل:")
    print("   python start_enhanced_whisper.py")
    
    print("\n2. تشغيل واجهة الويب فقط:")
    print("   python whisper_web_interface.py")
    
    print("\n3. تكامل مع النظام الحالي:")
    print("   python integrate_enhanced_whisper.py")
    
    print("\n🌐 الواجهات:")
    print("- واجهة الويب: http://localhost:5001")
    print("- API: http://localhost:5001/api/")
    
    print("\n📚 الميزات:")
    print("✅ تحويل صوت إلى نص محسن")
    print("✅ طرق رفع متعددة")
    print("✅ واجهة ويب تفاعلية")
    print("✅ إحصائيات مفصلة")
    print("✅ تصدير النتائج")
    
    print("\n🎤" * 50)

if __name__ == "__main__":
    success = test_system()
    
    if success:
        show_usage_instructions()
    
    sys.exit(0 if success else 1)
