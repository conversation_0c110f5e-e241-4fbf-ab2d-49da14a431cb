#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح النصوص العربية في النظام
Test Arabic text fix in the system
"""

import asyncio
import os
import sys
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.manual_image_generator import ManualImageGenerator
from modules.logger import logger

class ArabicTextTester:
    """فئة اختبار النصوص العربية"""
    
    def __init__(self):
        self.generator = ManualImageGenerator("اختبار النصوص العربية")
        self.output_dir = "test_results/arabic_text_fix"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # نصوص عربية للاختبار
        self.arabic_test_texts = [
            "سلام عليكم ورحمة الله وبركاته",
            "مرحباً بكم في عالم الألعاب الرائع",
            "أفضل ألعاب الكمبيوتر لعام 2024",
            "بطولة الرياضات الإلكترونية العالمية",
            "تحديث جديد للعبة فورتنايت",
            "مراجعة شاملة للعبة سايبر بانك",
            "نصائح وحيل للفوز في ألعاب الأكشن",
            "أخبار صناعة الألعاب اليوم"
        ]
        
        # مقالات اختبار مع نصوص عربية
        self.test_articles = [
            {
                'title': 'مراجعة شاملة للعبة Call of Duty الجديدة',
                'content': 'لعبة أكشن مليئة بالإثارة والتشويق مع جرافيك رائع ومعارك ملحمية',
                'expected_theme': 'action'
            },
            {
                'title': 'أفضل ألعاب النينتندو الكلاسيكية',
                'content': 'مجموعة من أروع الألعاب الكلاسيكية من نينتندو التي لا تُنسى',
                'expected_theme': 'classic'
            },
            {
                'title': 'بطولة الرياضات الإلكترونية العربية 2024',
                'content': 'انطلاق أكبر بطولة للرياضات الإلكترونية في المنطقة العربية',
                'expected_theme': 'esports'
            },
            {
                'title': 'تحديث جديد للعبة فيفا يضيف ميزات رائعة',
                'content': 'تحديث شامل للعبة كرة القدم الشهيرة مع إضافات جديدة',
                'expected_theme': 'sports'
            },
            {
                'title': 'مغامرة ملحمية في عالم زيلدا الجديد',
                'content': 'استكشف عالماً مفتوحاً مليئاً بالأسرار والمغامرات الشيقة',
                'expected_theme': 'adventure'
            }
        ]
    
    async def test_arabic_text_processing(self):
        """اختبار معالجة النصوص العربية"""
        logger.info("🔤 اختبار معالجة النصوص العربية...")
        
        results = {
            'total_tests': len(self.arabic_test_texts),
            'successful_processing': 0,
            'failed_processing': 0,
            'details': []
        }
        
        for i, text in enumerate(self.arabic_test_texts):
            logger.info(f"🔤 اختبار {i+1}/{len(self.arabic_test_texts)}: {text[:30]}...")
            
            try:
                # اختبار تحديد اللغة
                detected_lang = self.generator.detect_text_language(text)
                
                # اختبار معالجة النص العربي
                processed_text = self.generator.process_arabic_text(text)
                
                # اختبار تحسين النص
                enhanced_text = self.generator.enhance_text_for_display(text, detected_lang)
                
                success = (detected_lang == 'arabic' and 
                          processed_text != text and 
                          enhanced_text != text)
                
                if success:
                    results['successful_processing'] += 1
                else:
                    results['failed_processing'] += 1
                
                results['details'].append({
                    'original_text': text,
                    'detected_language': detected_lang,
                    'processed_text': processed_text,
                    'enhanced_text': enhanced_text,
                    'success': success
                })
                
                logger.info(f"{'✅' if success else '❌'} النص: {text[:20]}...")
                logger.info(f"   اللغة المكتشفة: {detected_lang}")
                logger.info(f"   النص المعالج: {processed_text[:30]}...")
                
            except Exception as e:
                results['failed_processing'] += 1
                results['details'].append({
                    'original_text': text,
                    'error': str(e),
                    'success': False
                })
                logger.error(f"❌ خطأ في معالجة النص: {e}")
        
        success_rate = (results['successful_processing'] / results['total_tests']) * 100
        logger.info(f"🔤 معدل نجاح معالجة النصوص العربية: {success_rate:.1f}%")
        
        return results
    
    async def test_arabic_image_generation(self):
        """اختبار إنشاء صور مع نصوص عربية"""
        logger.info("🎨 اختبار إنشاء صور مع نصوص عربية...")
        
        results = {
            'total_tests': len(self.test_articles),
            'successful_generations': 0,
            'failed_generations': 0,
            'details': []
        }
        
        for i, article in enumerate(self.test_articles):
            logger.info(f"🎨 اختبار {i+1}/{len(self.test_articles)}: {article['title'][:40]}...")
            
            try:
                # إنشاء الصورة
                image_result = await self.generator.generate_manual_image(article)
                
                if image_result:
                    results['successful_generations'] += 1
                    
                    # فحص الملف المُنشأ
                    file_exists = os.path.exists(image_result['local_path'])
                    file_size = os.path.getsize(image_result['local_path']) if file_exists else 0
                    
                    results['details'].append({
                        'article_title': article['title'],
                        'success': True,
                        'filename': image_result['filename'],
                        'file_exists': file_exists,
                        'file_size_kb': round(file_size / 1024, 2),
                        'theme': image_result.get('theme', 'unknown'),
                        'generation_method': image_result.get('generation_method', 'unknown')
                    })
                    
                    logger.info(f"✅ تم إنشاء الصورة: {image_result['filename']} ({file_size/1024:.1f} KB)")
                    
                else:
                    results['failed_generations'] += 1
                    results['details'].append({
                        'article_title': article['title'],
                        'success': False,
                        'error': 'No image result returned'
                    })
                    logger.error(f"❌ فشل في إنشاء الصورة للمقال: {article['title']}")
                    
            except Exception as e:
                results['failed_generations'] += 1
                results['details'].append({
                    'article_title': article['title'],
                    'success': False,
                    'error': str(e)
                })
                logger.error(f"❌ خطأ في إنشاء الصورة: {e}")
        
        success_rate = (results['successful_generations'] / results['total_tests']) * 100
        logger.info(f"🎨 معدل نجاح إنشاء الصور العربية: {success_rate:.1f}%")
        
        return results
    
    async def test_font_usage(self):
        """اختبار استخدام الخطوط المنسوخة"""
        logger.info("🔤 اختبار استخدام الخطوط المنسوخة...")
        
        # فحص الخطوط المتاحة
        arabic_fonts = self.generator.get_available_fonts('arabic')
        english_fonts = self.generator.get_available_fonts('english')
        
        logger.info(f"📊 خطوط عربية متاحة: {len(arabic_fonts)}")
        for font in arabic_fonts:
            logger.info(f"  - {os.path.basename(font)}")
        
        logger.info(f"📊 خطوط إنجليزية متاحة: {len(english_fonts)}")
        for font in english_fonts:
            logger.info(f"  - {os.path.basename(font)}")
        
        # اختبار تحميل الخطوط
        font_tests = []
        
        # اختبار خط عربي
        try:
            arabic_font = self.generator.get_font(32, arabic_support=True)
            font_tests.append({
                'type': 'arabic',
                'success': arabic_font is not None,
                'font_type': type(arabic_font).__name__
            })
            logger.info(f"✅ تم تحميل خط عربي: {type(arabic_font).__name__}")
        except Exception as e:
            font_tests.append({
                'type': 'arabic',
                'success': False,
                'error': str(e)
            })
            logger.error(f"❌ فشل في تحميل خط عربي: {e}")
        
        # اختبار خط إنجليزي
        try:
            english_font = self.generator.get_font(32, arabic_support=False)
            font_tests.append({
                'type': 'english',
                'success': english_font is not None,
                'font_type': type(english_font).__name__
            })
            logger.info(f"✅ تم تحميل خط إنجليزي: {type(english_font).__name__}")
        except Exception as e:
            font_tests.append({
                'type': 'english',
                'success': False,
                'error': str(e)
            })
            logger.error(f"❌ فشل في تحميل خط إنجليزي: {e}")
        
        return {
            'arabic_fonts_count': len(arabic_fonts),
            'english_fonts_count': len(english_fonts),
            'font_tests': font_tests,
            'total_fonts': len(arabic_fonts) + len(english_fonts)
        }
    
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل للنصوص العربية"""
        logger.info("🚀 بدء اختبار شامل للنصوص العربية...")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        # تشغيل جميع الاختبارات
        test_results = {
            'start_time': start_time.isoformat(),
            'arabic_text_processing': await self.test_arabic_text_processing(),
            'font_usage': await self.test_font_usage(),
            'arabic_image_generation': await self.test_arabic_image_generation()
        }
        
        end_time = datetime.now()
        test_results['end_time'] = end_time.isoformat()
        test_results['duration_seconds'] = (end_time - start_time).total_seconds()
        
        # حساب الإحصائيات العامة
        total_tests = (
            test_results['arabic_text_processing']['total_tests'] +
            len(test_results['font_usage']['font_tests']) +
            test_results['arabic_image_generation']['total_tests']
        )
        
        successful_tests = (
            test_results['arabic_text_processing']['successful_processing'] +
            sum(1 for test in test_results['font_usage']['font_tests'] if test['success']) +
            test_results['arabic_image_generation']['successful_generations']
        )
        
        overall_success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        test_results['summary'] = {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'failed_tests': total_tests - successful_tests,
            'overall_success_rate': round(overall_success_rate, 2),
            'duration_minutes': round(test_results['duration_seconds'] / 60, 2),
            'arabic_support_status': {
                'text_processing_success_rate': round(
                    (test_results['arabic_text_processing']['successful_processing'] / 
                     test_results['arabic_text_processing']['total_tests']) * 100, 1
                ),
                'fonts_available': test_results['font_usage']['total_fonts'] > 0,
                'image_generation_success_rate': round(
                    (test_results['arabic_image_generation']['successful_generations'] /
                     test_results['arabic_image_generation']['total_tests']) * 100, 1
                )
            }
        }
        
        # طباعة الملخص
        logger.info("=" * 60)
        logger.info("📊 ملخص نتائج اختبار النصوص العربية:")
        logger.info(f"⏱️ المدة الإجمالية: {test_results['summary']['duration_minutes']} دقيقة")
        logger.info(f"📈 إجمالي الاختبارات: {test_results['summary']['total_tests']}")
        logger.info(f"✅ الاختبارات الناجحة: {test_results['summary']['successful_tests']}")
        logger.info(f"❌ الاختبارات الفاشلة: {test_results['summary']['failed_tests']}")
        logger.info(f"🎯 معدل النجاح الإجمالي: {test_results['summary']['overall_success_rate']}%")
        logger.info("")
        logger.info("🔧 حالة دعم العربية:")
        logger.info(f"🔤 معدل نجاح معالجة النصوص: {test_results['summary']['arabic_support_status']['text_processing_success_rate']}%")
        logger.info(f"🔤 خطوط متاحة: {'✅' if test_results['summary']['arabic_support_status']['fonts_available'] else '❌'}")
        logger.info(f"🎨 معدل نجاح إنشاء الصور: {test_results['summary']['arabic_support_status']['image_generation_success_rate']}%")
        logger.info("=" * 60)
        
        return test_results

async def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🔤 بدء اختبار إصلاح النصوص العربية")
        
        tester = ArabicTextTester()
        results = await tester.run_comprehensive_test()
        
        logger.info("🎉 تم الانتهاء من اختبار النصوص العربية بنجاح!")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الاختبار: {e}")
        return None

if __name__ == "__main__":
    # تشغيل الاختبار
    results = asyncio.run(main())
