#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام الفهرسة الذكي
Comprehensive Test for Intelligent Indexing System
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger


class IndexingSystemTester:
    """فئة اختبار نظام الفهرسة الذكي"""
    
    def __init__(self):
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': [],
            'start_time': datetime.now().isoformat(),
            'end_time': None
        }
        
    def run_test(self, test_name: str, test_func):
        """تشغيل اختبار واحد"""
        try:
            self.test_results['total_tests'] += 1
            
            print(f"🧪 اختبار: {test_name}")
            
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            duration = end_time - start_time
            
            if result:
                print(f"✅ نجح الاختبار: {test_name} ({duration:.2f}s)")
                self.test_results['passed_tests'] += 1
                status = 'PASSED'
            else:
                print(f"❌ فشل الاختبار: {test_name} ({duration:.2f}s)")
                self.test_results['failed_tests'] += 1
                status = 'FAILED'
            
            self.test_results['test_details'].append({
                'name': test_name,
                'status': status,
                'duration': duration,
                'timestamp': datetime.now().isoformat()
            })
            
            return result
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار {test_name}: {e}")
            self.test_results['failed_tests'] += 1
            self.test_results['test_details'].append({
                'name': test_name,
                'status': 'ERROR',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False
    
    async def run_async_test(self, test_name: str, test_func):
        """تشغيل اختبار غير متزامن"""
        try:
            self.test_results['total_tests'] += 1
            
            print(f"🧪 اختبار غير متزامن: {test_name}")
            
            start_time = time.time()
            result = await test_func()
            end_time = time.time()
            
            duration = end_time - start_time
            
            if result:
                print(f"✅ نجح الاختبار: {test_name} ({duration:.2f}s)")
                self.test_results['passed_tests'] += 1
                status = 'PASSED'
            else:
                print(f"❌ فشل الاختبار: {test_name} ({duration:.2f}s)")
                self.test_results['failed_tests'] += 1
                status = 'FAILED'
            
            self.test_results['test_details'].append({
                'name': test_name,
                'status': status,
                'duration': duration,
                'timestamp': datetime.now().isoformat()
            })
            
            return result
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار {test_name}: {e}")
            self.test_results['failed_tests'] += 1
            self.test_results['test_details'].append({
                'name': test_name,
                'status': 'ERROR',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False
    
    def test_module_imports(self):
        """اختبار استيراد الوحدات"""
        try:
            from modules.intelligent_indexing_manager import IntelligentIndexingManager, get_indexing_manager
            from indexing_web_interface import app
            from integrate_intelligent_indexing import IndexingIntegration
            return True
        except Exception as e:
            print(f"خطأ في الاستيراد: {e}")
            return False
    
    def test_indexing_manager_creation(self):
        """اختبار إنشاء مدير الفهرسة"""
        try:
            from modules.intelligent_indexing_manager import get_indexing_manager
            
            site_url = "https://test-site.com"
            manager = get_indexing_manager(site_url)
            
            return manager is not None and manager.site_url == site_url
        except Exception as e:
            print(f"خطأ في إنشاء المدير: {e}")
            return False
    
    def test_quick_status(self):
        """اختبار الحصول على الحالة السريعة"""
        try:
            from modules.intelligent_indexing_manager import get_indexing_manager
            
            manager = get_indexing_manager("https://test-site.com")
            status = manager.get_quick_status()
            
            required_keys = ['monitoring', 'total_issues', 'fixed_issues', 'pending_issues', 'success_rate']
            return all(key in status for key in required_keys)
        except Exception as e:
            print(f"خطأ في الحالة السريعة: {e}")
            return False
    
    async def test_seo_recommendations(self):
        """اختبار توليد توصيات SEO"""
        try:
            from modules.intelligent_indexing_manager import get_indexing_manager
            
            manager = get_indexing_manager("https://test-site.com")
            recommendations = await manager.generate_seo_recommendations()
            
            return isinstance(recommendations, list) and len(recommendations) > 0
        except Exception as e:
            print(f"خطأ في التوصيات: {e}")
            return False
    
    async def test_robots_txt_creation(self):
        """اختبار إنشاء robots.txt"""
        try:
            from modules.intelligent_indexing_manager import get_indexing_manager
            
            manager = get_indexing_manager("https://test-site.com")
            await manager._create_optimized_robots_txt()
            
            # فحص وجود الملف
            if os.path.exists("robots.txt"):
                with open("robots.txt", 'r', encoding='utf-8') as f:
                    content = f.read()
                    return "User-agent:" in content and "Sitemap:" in content
            
            return False
        except Exception as e:
            print(f"خطأ في إنشاء robots.txt: {e}")
            return False
    
    async def test_sitemap_creation(self):
        """اختبار إنشاء sitemap.xml"""
        try:
            from modules.intelligent_indexing_manager import get_indexing_manager
            
            manager = get_indexing_manager("https://test-site.com")
            await manager._create_optimized_sitemap()
            
            # فحص وجود الملف
            if os.path.exists("sitemap.xml"):
                with open("sitemap.xml", 'r', encoding='utf-8') as f:
                    content = f.read()
                    return "<urlset" in content and "<url>" in content
            
            return False
        except Exception as e:
            print(f"خطأ في إنشاء sitemap.xml: {e}")
            return False
    
    def test_web_interface(self):
        """اختبار واجهة الويب"""
        try:
            from indexing_web_interface import app
            
            with app.test_client() as client:
                # اختبار الصفحة الرئيسية
                response = client.get('/')
                if response.status_code != 200:
                    return False
                
                # اختبار API endpoint
                response = client.get('/api/indexing-status?site_url=https://test-site.com')
                if response.status_code != 200:
                    return False
                
                data = response.get_json()
                return data is not None and 'success' in data
                
        except Exception as e:
            print(f"خطأ في واجهة الويب: {e}")
            return False
    
    def test_integration_class(self):
        """اختبار فئة التكامل"""
        try:
            from integrate_intelligent_indexing import IndexingIntegration
            
            integration = IndexingIntegration("https://test-site.com")
            
            return (integration.site_url == "https://test-site.com" and 
                   integration.indexing_manager is not None)
        except Exception as e:
            print(f"خطأ في فئة التكامل: {e}")
            return False
    
    async def test_issue_detection(self):
        """اختبار كشف المشاكل"""
        try:
            from modules.intelligent_indexing_manager import get_indexing_manager
            
            manager = get_indexing_manager("https://test-site.com")
            
            # إضافة مشكلة وهمية للاختبار
            manager.indexing_issues['test_issue'] = {
                'type': 'test_issue',
                'description': 'مشكلة اختبار',
                'severity': 'low',
                'detected_at': datetime.now().isoformat()
            }
            
            issues = await manager.detect_indexing_issues()
            
            return len(issues) > 0 and any(issue['type'] == 'test_issue' for issue in issues)
        except Exception as e:
            print(f"خطأ في كشف المشاكل: {e}")
            return False
    
    def test_seo_optimization_functions(self):
        """اختبار وظائف تحسين SEO"""
        try:
            from integrate_intelligent_indexing import IndexingIntegration
            
            integration = IndexingIntegration("https://test-site.com")
            
            # اختبار تحسين العنوان
            title = "عنوان طويل جداً يحتوي على كلمات كثيرة ويجب أن يتم تقصيره للحصول على SEO أفضل"
            optimized_title = integration._optimize_title_for_seo(title)
            
            if len(optimized_title) > 60:
                return False
            
            # اختبار توليد الوصف التعريفي
            content = "هذا محتوى اختبار طويل يحتوي على معلومات مفيدة حول الألعاب والتقنية."
            meta_desc = integration._generate_meta_description(content)
            
            if len(meta_desc) > 160:
                return False
            
            # اختبار استخراج الكلمات المفتاحية
            keywords = integration._extract_keywords(content)
            
            return isinstance(keywords, list) and len(keywords) > 0
            
        except Exception as e:
            print(f"خطأ في وظائف SEO: {e}")
            return False
    
    def generate_report(self):
        """توليد تقرير شامل للاختبارات"""
        self.test_results['end_time'] = datetime.now().isoformat()
        
        success_rate = (self.test_results['passed_tests'] / self.test_results['total_tests']) * 100 if self.test_results['total_tests'] > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 تقرير الاختبارات الشامل")
        print("=" * 60)
        print(f"📈 إجمالي الاختبارات: {self.test_results['total_tests']}")
        print(f"✅ اختبارات ناجحة: {self.test_results['passed_tests']}")
        print(f"❌ اختبارات فاشلة: {self.test_results['failed_tests']}")
        print(f"📊 معدل النجاح: {success_rate:.1f}%")
        print()
        
        if success_rate >= 90:
            print("🎉 ممتاز! النظام يعمل بشكل مثالي")
        elif success_rate >= 70:
            print("👍 جيد! النظام يعمل بشكل جيد مع بعض المشاكل البسيطة")
        else:
            print("⚠️ يحتاج تحسين! هناك مشاكل تحتاج إلى إصلاح")
        
        print("\n📋 تفاصيل الاختبارات:")
        for test in self.test_results['test_details']:
            status_icon = "✅" if test['status'] == 'PASSED' else "❌"
            print(f"  {status_icon} {test['name']}")
            if test['status'] == 'ERROR':
                print(f"    خطأ: {test.get('error', 'غير محدد')}")
        
        # حفظ التقرير في ملف
        report_filename = f"indexing_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ التقرير في: {report_filename}")
        
        return success_rate >= 70


async def main():
    """الدالة الرئيسية للاختبار"""
    try:
        print("🧪 بدء الاختبار الشامل لنظام الفهرسة الذكي")
        print("=" * 60)
        print()
        
        tester = IndexingSystemTester()
        
        # الاختبارات المتزامنة
        print("📦 اختبارات الوحدات والاستيراد:")
        tester.run_test("استيراد الوحدات", tester.test_module_imports)
        tester.run_test("إنشاء مدير الفهرسة", tester.test_indexing_manager_creation)
        tester.run_test("الحصول على الحالة السريعة", tester.test_quick_status)
        tester.run_test("واجهة الويب", tester.test_web_interface)
        tester.run_test("فئة التكامل", tester.test_integration_class)
        tester.run_test("وظائف تحسين SEO", tester.test_seo_optimization_functions)
        
        print("\n🔄 الاختبارات غير المتزامنة:")
        await tester.run_async_test("توليد توصيات SEO", tester.test_seo_recommendations)
        await tester.run_async_test("إنشاء robots.txt", tester.test_robots_txt_creation)
        await tester.run_async_test("إنشاء sitemap.xml", tester.test_sitemap_creation)
        await tester.run_async_test("كشف المشاكل", tester.test_issue_detection)
        
        # توليد التقرير النهائي
        success = tester.generate_report()
        
        if success:
            print("\n🚀 النظام جاهز للاستخدام!")
            print("يمكنك تشغيله باستخدام: START_INTELLIGENT_INDEXING.bat")
        else:
            print("\n⚠️ يرجى مراجعة الأخطاء وإصلاحها قبل الاستخدام")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ خطأ في الاختبار الشامل: {e}")
        print(f"❌ خطأ عام في الاختبار: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\n✅ تم إكمال جميع الاختبارات بنجاح!")
    else:
        print("\n❌ فشل في بعض الاختبارات")
        sys.exit(1)
