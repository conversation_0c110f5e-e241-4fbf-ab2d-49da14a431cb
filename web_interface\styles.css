/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo i {
    font-size: 2rem;
    color: #667eea;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 25px;
    border: 1px solid #e0e0e0;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: #4CAF50;
}

.status-dot.offline {
    background: #f44336;
}

.status-dot.processing {
    background: #ff9800;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.btn-warning {
    background: linear-gradient(45deg, #ff9800, #f57c00);
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, #f44336, #d32f2f);
    color: white;
}

.btn-info {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white;
}

/* Main Content */
.main-content {
    padding: 2rem 0;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-body {
    padding: 1.5rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* Activity Log */
.activity-log {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    font-weight: 600;
    color: #667eea;
    min-width: 60px;
}

.activity-text {
    color: #666;
}

/* Control Buttons */
.control-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

/* Tabs */
.tabs-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tabs-header {
    display: flex;
    background: rgba(102, 126, 234, 0.1);
}

.tab-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-bottom-color: #fff;
}

.tab-btn:hover:not(.active) {
    background: rgba(102, 126, 234, 0.2);
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

/* Content List */
.content-list {
    max-height: 400px;
    overflow-y: auto;
}

.content-item {
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.content-item:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.content-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.content-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #666;
}

/* Loading */
.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

/* Form Elements */
.settings-form {
    max-width: 500px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-left: 8px;
}

/* Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;
}

.chart-container {
    background: rgba(102, 126, 234, 0.05);
    padding: 1rem;
    border-radius: 10px;
}

.analytics-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.analytics-item {
    padding: 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    text-align: center;
}

.analytics-item h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.analytics-item span {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

/* Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 2000;
}

.notification {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    border-right: 4px solid #667eea;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-right-color: #4CAF50;
}

.notification.error {
    border-right-color: #f44336;
}

.notification.warning {
    border-right-color: #ff9800;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 3000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.logs-container {
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 1rem;
    border-radius: 8px;
    white-space: pre-wrap;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .tabs-header {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1 1 50%;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .control-buttons {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}