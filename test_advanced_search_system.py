#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام البحث المتقدم مع البدائل المتعددة
"""

import asyncio
import json
from datetime import datetime
from modules.advanced_search_manager import advanced_search_manager
from modules.content_scraper import ContentScraper
from modules.logger import logger

async def test_advanced_search_system():
    """اختبار شامل لنظام البحث المتقدم"""
    
    print("🚀 بدء اختبار نظام البحث المتقدم مع البدائل المتعددة")
    print("=" * 60)
    
    # 1. اختبار حالة المحركات
    print("\n📊 1. فحص حالة محركات البحث:")
    engine_status = advanced_search_manager.get_engine_status()
    
    for engine_name, status in engine_status.items():
        status_icon = "✅" if status['is_active'] else "❌"
        print(f"   {status_icon} {engine_name}: أولوية {status['priority']}, جودة {status['quality_score']}/10")
        print(f"      الاستخدام اليومي: {status['daily_usage']}/{status['daily_limit']}")
        if status['consecutive_failures'] > 0:
            print(f"      ⚠️ فشل متتالي: {status['consecutive_failures']}")
    
    # 2. اختبار جميع المحركات
    print("\n🧪 2. اختبار جميع محركات البحث:")
    test_results = await advanced_search_manager.test_all_engines()
    
    for engine_name, result in test_results.items():
        status_icon = "✅" if result['status'] == 'success' else "❌"
        print(f"   {status_icon} {engine_name}: {result['message']}")
        print(f"      وقت الاستجابة: {result['response_time']:.2f}s")
        if result['results_count'] > 0:
            print(f"      عدد النتائج: {result['results_count']}")
    
    # 3. اختبار البحث المتقدم
    print("\n🔍 3. اختبار البحث المتقدم:")
    test_queries = [
        "gaming news today",
        "new video game releases",
        "gaming industry updates"
    ]
    
    for query in test_queries:
        print(f"\n   🔍 البحث عن: '{query}'")
        try:
            start_time = datetime.now()
            results = await advanced_search_manager.advanced_search(query, max_results=5)
            end_time = datetime.now()
            
            response_time = (end_time - start_time).total_seconds()
            
            if results:
                print(f"   ✅ تم العثور على {len(results)} نتيجة في {response_time:.2f}s")
                
                # عرض أفضل نتيجة
                best_result = results[0]
                print(f"   📰 أفضل نتيجة:")
                print(f"      العنوان: {best_result['title'][:60]}...")
                print(f"      المصدر: {best_result['source']}")
                print(f"      درجة الصلة: {best_result.get('relevance_score', 'غير محدد')}")
                
            else:
                print(f"   ❌ لم يتم العثور على نتائج في {response_time:.2f}s")
                
        except Exception as e:
            print(f"   ❌ خطأ في البحث: {e}")
    
    # 4. اختبار ContentScraper الجديد
    print("\n📰 4. اختبار ContentScraper مع النظام الجديد:")
    scraper = ContentScraper()
    
    try:
        print("   🔍 اختبار البحث المتقدم مع البدائل...")
        start_time = datetime.now()
        results = await scraper.advanced_search_with_fallbacks("gaming news", max_results=3)
        end_time = datetime.now()
        
        response_time = (end_time - start_time).total_seconds()
        
        if results:
            print(f"   ✅ ContentScraper: {len(results)} نتيجة في {response_time:.2f}s")
            
            for i, result in enumerate(results, 1):
                print(f"   📄 نتيجة {i}:")
                print(f"      العنوان: {result['title'][:50]}...")
                print(f"      المصدر: {result['source']}")
                print(f"      جودة المحتوى: {result.get('content_quality', 'غير محدد')}/10")
                print(f"      صلة الألعاب: {result.get('gaming_relevance', 'غير محدد')}/10")
        else:
            print(f"   ❌ ContentScraper: لم يتم العثور على نتائج في {response_time:.2f}s")
            
    except Exception as e:
        print(f"   ❌ خطأ في ContentScraper: {e}")
    
    # 5. إحصائيات نهائية
    print("\n📊 5. الإحصائيات النهائية:")
    final_status = advanced_search_manager.get_engine_status()
    
    active_engines = sum(1 for status in final_status.values() if status['is_active'])
    total_engines = len(final_status)
    
    print(f"   المحركات النشطة: {active_engines}/{total_engines}")
    print(f"   حجم التخزين المؤقت: {len(advanced_search_manager.cache)} عنصر")
    
    # عرض المحركات المعطلة
    disabled_engines = [name for name, status in final_status.items() if not status['is_active']]
    if disabled_engines:
        print(f"   ⚠️ المحركات المعطلة: {', '.join(disabled_engines)}")
    
    print("\n" + "=" * 60)
    print("✅ اكتمل اختبار نظام البحث المتقدم")

async def test_fallback_system():
    """اختبار نظام البدائل التلقائي"""
    
    print("\n🔄 اختبار نظام البدائل التلقائي:")
    print("-" * 40)
    
    # محاكاة تعطيل المحرك الأول (Tavily)
    print("   🔧 محاكاة تعطيل المحرك الأول...")
    
    # تسجيل فشل متتالي لـ Tavily
    from modules.advanced_search_manager import SearchEngine
    
    for _ in range(3):
        advanced_search_manager._record_failure(SearchEngine.TAVILY)
    
    print("   ⚠️ تم تعطيل Tavily مؤقتاً")
    
    # اختبار البحث مع المحرك المعطل
    print("   🔍 اختبار البحث مع المحرك المعطل...")
    
    try:
        results = await advanced_search_manager.advanced_search("gaming news test", max_results=3)
        
        if results:
            print(f"   ✅ نجح النظام في التبديل للبديل: {len(results)} نتيجة")
            print(f"   📰 المصدر المستخدم: {results[0]['source']}")
        else:
            print("   ❌ فشل النظام في العثور على بديل")
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار البدائل: {e}")
    
    # إعادة تفعيل المحرك
    print("   🔧 إعادة تفعيل المحرك...")
    advanced_search_manager.reset_engine_status(SearchEngine.TAVILY)
    print("   ✅ تم إعادة تفعيل Tavily")

async def main():
    """الدالة الرئيسية للاختبار"""
    try:
        await test_advanced_search_system()
        await test_fallback_system()
        
        print("\n🎉 تم اكتمال جميع الاختبارات بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
