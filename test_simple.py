#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# Add current path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    print("Testing imports...")
    
    try:
        print("   Importing logger...")
        from modules.logger import logger
        print("   OK: logger")
        
        print("   Importing database...")
        from modules.database import db
        print("   OK: database")
        
        print("   Importing enhanced system...")
        
        try:
            from modules.agent_state_manager import agent_state_manager, AgentState
            print("   OK: agent_state_manager")
        except Exception as e:
            print(f"   ERROR: agent_state_manager: {e}")
            return False
        
        try:
            from modules.operation_manager import operation_manager, OperationType, OperationPriority
            print("   OK: operation_manager")
        except Exception as e:
            print(f"   ERROR: operation_manager: {e}")
            return False
        
        try:
            from modules.smart_lifecycle_manager import lifecycle_manager, StartupMode, ShutdownReason
            print("   OK: smart_lifecycle_manager")
        except Exception as e:
            print(f"   ERROR: smart_lifecycle_manager: {e}")
            return False
        
        print("   All imports successful!")
        return True
        
    except Exception as e:
        print(f"   ERROR in imports: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 50)
    print("Simple System Test")
    print("=" * 50)
    
    if test_imports():
        print("\nSUCCESS: System ready!")
        return True
    else:
        print("\nERROR: System has issues")
        return False

if __name__ == "__main__":
    try:
        result = main()
        if result:
            print("\nTest PASSED")
            sys.exit(0)
        else:
            print("\nTest FAILED")
            sys.exit(1)
    except Exception as e:
        print(f"\nERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
