#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام الجديد للواجهة الويب
يختبر جميع المكونات للتأكد من عملها بدون Telegram
"""

import asyncio
import sys
import os
import time
import requests
from pathlib import Path

# إضافة المسار الحالي لـ Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from modules.logger import logger
from modules.database import db
from modules.web_approval_system import web_approval_system
from modules.publisher import PublisherManager
from config.settings import BotConfig

class WebSystemTester:
    """فئة اختبار النظام الجديد"""

    def __init__(self):
        self.test_results = {}
        self.api_base_url = "http://localhost:5000/api"

    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار النظام الجديد للواجهة الويب")
        print("=" * 60)

        # اختبارات المكونات الأساسية
        self.test_database()
        self.test_web_approval_system()
        self.test_publisher_without_telegram()
        self.test_web_interface_files()

        # اختبارات API (إذا كان الخادم يعمل)
        if self.check_server_running():
            self.test_api_endpoints()
        else:
            print("⚠️ خادم الواجهة الويب غير مشغل - تخطي اختبارات API")

        # عرض النتائج
        self.display_results()

    def test_database(self):
        """اختبار قاعدة البيانات"""
        print("\n📊 اختبار قاعدة البيانات...")
        try:
            # اختبار الاتصال
            stats = db.get_stats_summary(1)

            # اختبار حفظ مقال تجريبي
            test_article = {
                'title': 'مقال اختبار',
                'content': 'محتوى اختبار للتأكد من عمل قاعدة البيانات',
                'source_type': 'test',
                'published_date': '2025-01-21'
            }

            article_id = db.save_article(test_article)

            if article_id:
                self.test_results['database'] = True
                print("✅ قاعدة البيانات تعمل بشكل صحيح")
            else:
                self.test_results['database'] = False
                print("❌ فشل في حفظ مقال تجريبي")

        except Exception as e:
            self.test_results['database'] = False
            print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")

    def test_web_approval_system(self):
        """اختبار نظام الموافقة الويب"""
        print("\n📋 اختبار نظام الموافقة الويب...")
        try:
            # اختبار إنشاء طلب موافقة
            test_content = {
                'title': 'محتوى اختبار',
                'summary': 'ملخص محتوى اختبار',
                'source': 'test'
            }

            async def test_callback(approved, reason):
                print(f"📋 تم استلام نتيجة الموافقة: {approved} - {reason}")

            async def run_approval_test():
                approval_id = await web_approval_system.request_content_approval(
                    test_content, test_callback, "test"
                )

                if approval_id:
                    # اختبار الحصول على الموافقات المعلقة
                    pending = web_approval_system.get_pending_approvals()

                    if len(pending) > 0:
                        # اختبار الموافقة
                        success = await web_approval_system.approve_content(approval_id, "test_user")
                        return success

                return False

            # تشغيل الاختبار
            result = asyncio.run(run_approval_test())

            self.test_results['web_approval'] = result
            if result:
                print("✅ نظام الموافقة الويب يعمل بشكل صحيح")
            else:
                print("❌ فشل في اختبار نظام الموافقة الويب")

        except Exception as e:
            self.test_results['web_approval'] = False
            print(f"❌ خطأ في اختبار نظام الموافقة: {e}")

    def test_publisher_without_telegram(self):
        """اختبار الناشر بدون Telegram"""
        print("\n📝 اختبار الناشر (Blogger فقط)...")
        try:
            # إعداد الناشر
            blogger_config = {
                'client_id': BotConfig.BLOGGER_CLIENT_ID,
                'client_secret': BotConfig.BLOGGER_CLIENT_SECRET,
                'blog_id': BotConfig.BLOGGER_BLOG_ID
            }

            publisher = PublisherManager(blogger_config, None)

            # اختبار الاتصالات
            async def test_connections():
                return await publisher.test_all_connections()

            results = asyncio.run(test_connections())

            # التحقق من النتائج
            if results['blogger'] and results['overall']:
                self.test_results['publisher'] = True
                print("✅ الناشر يعمل بشكل صحيح (Blogger فقط)")
                print(f"   • Blogger: {'✅' if results['blogger'] else '❌'}")
                print(f"   • Telegram: ❌ (تم إزالته)")
            else:
                self.test_results['publisher'] = False
                print("❌ فشل في اختبار الناشر")

        except Exception as e:
            self.test_results['publisher'] = False
            print(f"❌ خطأ في اختبار الناشر: {e}")

    def test_web_interface_files(self):
        """اختبار ملفات الواجهة الويب"""
        print("\n🌐 اختبار ملفات الواجهة الويب...")
        try:
            required_files = [
                'web_interface/index.html',
                'web_interface/styles.css',
                'web_interface/script.js',
                'web_api.py',
                'run_web.py'
            ]

            missing_files = []
            for file_path in required_files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)

            if not missing_files:
                self.test_results['web_files'] = True
                print("✅ جميع ملفات الواجهة الويب موجودة")
            else:
                self.test_results['web_files'] = False
                print("❌ ملفات مفقودة:")
                for file in missing_files:
                    print(f"   • {file}")

        except Exception as e:
            self.test_results['web_files'] = False
            print(f"❌ خطأ في اختبار ملفات الواجهة: {e}")

    def check_server_running(self):
        """التحقق من تشغيل الخادم"""
        try:
            response = requests.get(f"{self.api_base_url}/status", timeout=5)
            return response.status_code == 200
        except:
            return False

    def test_api_endpoints(self):
        """اختبار نقاط API"""
        print("\n🔌 اختبار نقاط API...")
        try:
            endpoints = [
                '/status',
                '/content',
                '/approvals/pending',
                '/settings'
            ]

            working_endpoints = 0
            for endpoint in endpoints:
                try:
                    response = requests.get(f"{self.api_base_url}{endpoint}", timeout=5)
                    if response.status_code == 200:
                        working_endpoints += 1
                        print(f"   ✅ {endpoint}")
                    else:
                        print(f"   ❌ {endpoint} - Status: {response.status_code}")
                except Exception as e:
                    print(f"   ❌ {endpoint} - Error: {e}")

            self.test_results['api_endpoints'] = working_endpoints == len(endpoints)

            if working_endpoints == len(endpoints):
                print("✅ جميع نقاط API تعمل بشكل صحيح")
            else:
                print(f"⚠️ {working_endpoints}/{len(endpoints)} نقاط API تعمل")

        except Exception as e:
            self.test_results['api_endpoints'] = False
            print(f"❌ خطأ في اختبار نقاط API: {e}")

    def display_results(self):
        """عرض نتائج الاختبارات"""
        print("\n" + "=" * 60)
        print("📊 نتائج الاختبارات:")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)

        for test_name, result in self.test_results.items():
            status = "✅ نجح" if result else "❌ فشل"
            print(f"   • {test_name}: {status}")

        print("=" * 60)
        print(f"📈 الإجمالي: {passed_tests}/{total_tests} اختبار نجح")

        if passed_tests == total_tests:
            print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        else:
            print("⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء أعلاه")

        print("=" * 60)

def main():
    """تشغيل الاختبارات"""
    tester = WebSystemTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()