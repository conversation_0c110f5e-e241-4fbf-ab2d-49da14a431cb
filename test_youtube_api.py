#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مفتاح YouTube API الجديد
"""

import os
import sys
import asyncio
import logging
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_youtube_api_key(api_key: str) -> bool:
    """اختبار مفتاح YouTube API"""
    try:
        logger.info(f"🧪 اختبار مفتاح YouTube API: {api_key[:20]}...")
        
        # إنشاء خدمة YouTube
        youtube = build('youtube', 'v3', developerKey=api_key)
        
        # اختبار بسيط: البحث عن قناة
        request = youtube.search().list(
            part="snippet",
            q="gaming news",
            type="channel",
            maxResults=1
        )
        
        response = request.execute()
        
        if response and 'items' in response:
            logger.info(f"✅ المفتاح يعمل! تم العثور على {len(response['items'])} نتيجة")
            return True
        else:
            logger.warning("⚠️ المفتاح يعمل لكن لم يتم العثور على نتائج")
            return True
            
    except HttpError as e:
        if e.resp.status == 403:
            logger.error(f"❌ المفتاح محظور أو غير صالح: {e}")
        elif e.resp.status == 400:
            logger.error(f"❌ طلب غير صالح: {e}")
        else:
            logger.error(f"❌ خطأ HTTP: {e}")
        return False
        
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        return False

def test_video_details(api_key: str, video_id: str = "dQw4w9WgXcQ") -> bool:
    """اختبار الحصول على تفاصيل فيديو"""
    try:
        logger.info(f"🎥 اختبار الحصول على تفاصيل الفيديو: {video_id}")
        
        youtube = build('youtube', 'v3', developerKey=api_key)
        
        request = youtube.videos().list(
            part="snippet,contentDetails",
            id=video_id
        )
        
        response = request.execute()
        
        if response and 'items' in response and len(response['items']) > 0:
            video = response['items'][0]
            title = video['snippet']['title']
            duration = video['contentDetails']['duration']
            
            logger.info(f"✅ تم الحصول على تفاصيل الفيديو:")
            logger.info(f"   العنوان: {title}")
            logger.info(f"   المدة: {duration}")
            return True
        else:
            logger.warning("⚠️ لم يتم العثور على الفيديو")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار تفاصيل الفيديو: {e}")
        return False

def test_channel_videos(api_key: str, channel_id: str = "UCdHyMy7pxEYUUEJYhAK7pwQ") -> bool:
    """اختبار الحصول على فيديوهات قناة"""
    try:
        logger.info(f"📺 اختبار الحصول على فيديوهات القناة: {channel_id}")
        
        youtube = build('youtube', 'v3', developerKey=api_key)
        
        # الحصول على معرف قائمة التشغيل للقناة
        channel_request = youtube.channels().list(
            part="contentDetails",
            id=channel_id
        )
        
        channel_response = channel_request.execute()
        
        if not channel_response['items']:
            logger.warning("⚠️ لم يتم العثور على القناة")
            return False
            
        uploads_playlist_id = channel_response['items'][0]['contentDetails']['relatedPlaylists']['uploads']
        
        # الحصول على أحدث الفيديوهات
        videos_request = youtube.playlistItems().list(
            part="snippet",
            playlistId=uploads_playlist_id,
            maxResults=5
        )
        
        videos_response = videos_request.execute()
        
        if videos_response and 'items' in videos_response:
            logger.info(f"✅ تم العثور على {len(videos_response['items'])} فيديو من القناة")
            
            for i, video in enumerate(videos_response['items'][:3]):
                title = video['snippet']['title']
                logger.info(f"   {i+1}. {title[:50]}...")
                
            return True
        else:
            logger.warning("⚠️ لم يتم العثور على فيديوهات في القناة")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار فيديوهات القناة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    logger.info("🚀 بدء اختبار مفتاح YouTube API الجديد...")
    
    # المفتاح الجديد
    new_api_key = "AIzaSyAxLImn6q9_UOIS14BQ1qu537uohDlT16Y"
    
    tests = [
        ("اختبار أساسي للمفتاح", lambda: test_youtube_api_key(new_api_key)),
        ("اختبار تفاصيل الفيديو", lambda: test_video_details(new_api_key)),
        ("اختبار فيديوهات القناة", lambda: test_channel_videos(new_api_key))
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ نجح: {test_name}")
            else:
                logger.error(f"❌ فشل: {test_name}")
                
        except Exception as e:
            logger.error(f"💥 خطأ في {test_name}: {e}")
            results[test_name] = False
    
    # تقرير النتائج النهائي
    logger.info(f"\n{'='*50}")
    logger.info("📊 تقرير النتائج النهائي")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\n📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        logger.info("🎉 جميع الاختبارات نجحت! المفتاح يعمل بشكل ممتاز.")
        logger.info("✅ يمكن الآن استخدام هذا المفتاح في الوكيل.")
    elif passed > 0:
        logger.warning(f"⚠️ {passed} من {total} اختبارات نجحت. المفتاح يعمل جزئياً.")
    else:
        logger.error("❌ جميع الاختبارات فشلت. المفتاح لا يعمل.")

if __name__ == "__main__":
    main()
