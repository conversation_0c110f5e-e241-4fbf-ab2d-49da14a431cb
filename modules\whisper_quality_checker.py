# نظام فحص جودة النص المستخرج من Whisper
import re
import asyncio
import aiohttp
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import json
import hashlib
from .logger import logger
from .database import db

class WhisperQualityChecker:
    """نظام ذكي لفحص جودة النص المستخرج من Whisper"""
    
    def __init__(self):
        self.min_text_length = 50  # الحد الأدنى لطول النص
        self.max_repetition_ratio = 0.3  # نسبة التكرار المسموحة
        self.min_word_count = 10  # الحد الأدنى لعدد الكلمات
        self.quality_thresholds = {
            'excellent': 90,
            'good': 70,
            'acceptable': 50,
            'poor': 30
        }
        
        # قاموس الكلمات الشائعة للتحقق من المعنى
        self.common_words = {
            'ar': ['في', 'من', 'إلى', 'على', 'هذا', 'هذه', 'التي', 'الذي', 'كان', 'يكون', 'لعبة', 'ألعاب'],
            'en': ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'game', 'games']
        }
        
        # أنماط النص المشبوهة
        self.suspicious_patterns = [
            r'^[^a-zA-Zأ-ي\s]*$',  # نص بدون أحرف
            r'^(.)\1{10,}',  # تكرار حرف واحد
            r'^[0-9\s\-_.,!@#$%^&*()]+$',  # أرقام ورموز فقط
            r'^\s*$',  # مساحات فارغة فقط
        ]
    
    async def check_transcript_quality(self, transcript: str, video_data: Dict = None) -> Dict:
        """فحص شامل لجودة النص المستخرج"""
        try:
            logger.info("🔍 بدء فحص جودة النص المستخرج من Whisper...")
            
            if not transcript or not transcript.strip():
                return self._create_quality_report(0, "نص فارغ", {
                    'empty_text': True,
                    'length': 0
                })
            
            # تنظيف النص
            cleaned_text = self._clean_text(transcript)
            
            # الفحوصات الأساسية
            basic_checks = self._perform_basic_checks(cleaned_text)
            
            # فحص التكرار والأنماط
            pattern_checks = self._check_patterns_and_repetition(cleaned_text)
            
            # فحص المعنى والسياق
            context_checks = await self._check_context_and_meaning(cleaned_text, video_data)
            
            # فحص اللغة والترميز
            language_checks = self._check_language_encoding(cleaned_text)
            
            # حساب النقاط الإجمالية
            total_score = self._calculate_total_score(
                basic_checks, pattern_checks, context_checks, language_checks
            )
            
            # تحديد مستوى الجودة
            quality_level = self._determine_quality_level(total_score)
            
            # إنشاء التقرير النهائي
            quality_report = self._create_quality_report(
                total_score, quality_level, {
                    'basic_checks': basic_checks,
                    'pattern_checks': pattern_checks,
                    'context_checks': context_checks,
                    'language_checks': language_checks,
                    'cleaned_text_length': len(cleaned_text),
                    'original_text_length': len(transcript)
                }
            )
            
            # تسجيل النتائج
            self._log_quality_results(quality_report, video_data)
            
            logger.info(f"✅ اكتمل فحص الجودة - النقاط: {total_score}/100 ({quality_level})")
            return quality_report
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص جودة النص: {e}")
            return self._create_quality_report(0, "خطأ في الفحص", {'error': str(e)})
    
    def _clean_text(self, text: str) -> str:
        """تنظيف النص من الرموز غير المرغوبة"""
        try:
            # إزالة الرموز الغريبة والمساحات الزائدة
            cleaned = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]', ' ', text)
            cleaned = re.sub(r'\s+', ' ', cleaned)
            cleaned = cleaned.strip()
            
            return cleaned
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تنظيف النص: {e}")
            return text.strip()
    
    def _perform_basic_checks(self, text: str) -> Dict:
        """الفحوصات الأساسية للنص"""
        try:
            checks = {
                'length_check': len(text) >= self.min_text_length,
                'word_count_check': len(text.split()) >= self.min_word_count,
                'not_empty': bool(text.strip()),
                'has_letters': bool(re.search(r'[a-zA-Zأ-ي]', text)),
                'reasonable_length': 10 <= len(text) <= 10000
            }
            
            # حساب النقاط
            score = sum(checks.values()) * 20  # كل فحص يستحق 20 نقطة
            
            return {
                'score': min(score, 100),
                'checks': checks,
                'details': {
                    'text_length': len(text),
                    'word_count': len(text.split()),
                    'character_types': self._analyze_character_types(text)
                }
            }
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في الفحوصات الأساسية: {e}")
            return {'score': 0, 'checks': {}, 'details': {}}
    
    def _check_patterns_and_repetition(self, text: str) -> Dict:
        """فحص الأنماط والتكرار في النص"""
        try:
            # فحص الأنماط المشبوهة
            suspicious_found = []
            for pattern in self.suspicious_patterns:
                if re.search(pattern, text):
                    suspicious_found.append(pattern)
            
            # فحص التكرار
            words = text.split()
            if len(words) > 0:
                word_counts = {}
                for word in words:
                    word_counts[word] = word_counts.get(word, 0) + 1
                
                # حساب نسبة التكرار
                max_repetition = max(word_counts.values()) if word_counts else 0
                repetition_ratio = max_repetition / len(words) if len(words) > 0 else 0
            else:
                repetition_ratio = 0
            
            # فحص التنوع في الكلمات
            unique_words = len(set(words)) if words else 0
            diversity_ratio = unique_words / len(words) if len(words) > 0 else 0
            
            # حساب النقاط
            pattern_score = 100 - (len(suspicious_found) * 25)  # خصم 25 نقطة لكل نمط مشبوه
            repetition_score = 100 if repetition_ratio <= self.max_repetition_ratio else 50
            diversity_score = min(diversity_ratio * 100, 100)
            
            total_score = (pattern_score + repetition_score + diversity_score) / 3
            
            return {
                'score': max(total_score, 0),
                'suspicious_patterns': suspicious_found,
                'repetition_ratio': repetition_ratio,
                'diversity_ratio': diversity_ratio,
                'unique_words': unique_words,
                'total_words': len(words)
            }
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في فحص الأنماط: {e}")
            return {'score': 50, 'error': str(e)}
    
    async def _check_context_and_meaning(self, text: str, video_data: Dict = None) -> Dict:
        """فحص السياق والمعنى"""
        try:
            # فحص وجود كلمات ذات معنى
            words = text.lower().split()
            meaningful_words = 0
            
            # فحص الكلمات الشائعة
            for lang, common_list in self.common_words.items():
                for word in words:
                    if word in common_list:
                        meaningful_words += 1
            
            # فحص الكلمات المتعلقة بالألعاب
            gaming_keywords = [
                'game', 'play', 'player', 'level', 'score', 'win', 'lose',
                'لعبة', 'لعب', 'لاعب', 'مستوى', 'نقاط', 'فوز', 'خسارة'
            ]
            
            gaming_words = 0
            for word in words:
                if word in gaming_keywords:
                    gaming_words += 1
            
            # فحص التطابق مع بيانات الفيديو
            context_match_score = 0
            if video_data:
                context_match_score = await self._check_video_context_match(text, video_data)
            
            # حساب النقاط
            meaning_score = min((meaningful_words / len(words)) * 100, 100) if words else 0
            gaming_relevance = min((gaming_words / len(words)) * 200, 100) if words else 0
            
            total_score = (meaning_score + gaming_relevance + context_match_score) / 3
            
            return {
                'score': total_score,
                'meaningful_words': meaningful_words,
                'gaming_words': gaming_words,
                'context_match_score': context_match_score,
                'total_words_analyzed': len(words)
            }
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في فحص السياق: {e}")
            return {'score': 50, 'error': str(e)}
    
    async def _check_video_context_match(self, text: str, video_data: Dict) -> float:
        """فحص تطابق النص مع سياق الفيديو"""
        try:
            title = video_data.get('title', '').lower()
            description = video_data.get('description', '').lower()
            
            # استخراج الكلمات المفتاحية من العنوان والوصف
            title_words = set(re.findall(r'\w+', title))
            desc_words = set(re.findall(r'\w+', description))
            text_words = set(re.findall(r'\w+', text.lower()))
            
            # حساب التطابق
            title_matches = len(title_words.intersection(text_words))
            desc_matches = len(desc_words.intersection(text_words))
            
            # حساب النقاط بناءً على التطابق
            title_score = min((title_matches / len(title_words)) * 100, 100) if title_words else 0
            desc_score = min((desc_matches / len(desc_words)) * 100, 100) if desc_words else 0
            
            return (title_score + desc_score) / 2
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في فحص تطابق السياق: {e}")
            return 50.0
    
    def _check_language_encoding(self, text: str) -> Dict:
        """فحص اللغة والترميز"""
        try:
            # فحص الترميز
            try:
                text.encode('utf-8')
                encoding_ok = True
            except UnicodeEncodeError:
                encoding_ok = False
            
            # تحديد اللغة المحتملة
            arabic_chars = len(re.findall(r'[\u0600-\u06FF]', text))
            english_chars = len(re.findall(r'[a-zA-Z]', text))
            total_chars = len(re.findall(r'[a-zA-Z\u0600-\u06FF]', text))
            
            if total_chars > 0:
                arabic_ratio = arabic_chars / total_chars
                english_ratio = english_chars / total_chars
            else:
                arabic_ratio = english_ratio = 0
            
            # تحديد اللغة الرئيسية
            if arabic_ratio > 0.5:
                detected_language = 'arabic'
            elif english_ratio > 0.5:
                detected_language = 'english'
            else:
                detected_language = 'mixed'
            
            # حساب النقاط
            encoding_score = 100 if encoding_ok else 0
            language_score = 100 if detected_language in ['arabic', 'english'] else 70
            
            total_score = (encoding_score + language_score) / 2
            
            return {
                'score': total_score,
                'encoding_ok': encoding_ok,
                'detected_language': detected_language,
                'arabic_ratio': arabic_ratio,
                'english_ratio': english_ratio
            }
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في فحص اللغة: {e}")
            return {'score': 50, 'error': str(e)}
    
    def _analyze_character_types(self, text: str) -> Dict:
        """تحليل أنواع الأحرف في النص"""
        try:
            return {
                'letters': len(re.findall(r'[a-zA-Zأ-ي]', text)),
                'digits': len(re.findall(r'\d', text)),
                'spaces': len(re.findall(r'\s', text)),
                'punctuation': len(re.findall(r'[^\w\s]', text)),
                'arabic_chars': len(re.findall(r'[\u0600-\u06FF]', text)),
                'english_chars': len(re.findall(r'[a-zA-Z]', text))
            }
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحليل الأحرف: {e}")
            return {}
    
    def _calculate_total_score(self, basic: Dict, pattern: Dict, context: Dict, language: Dict) -> float:
        """حساب النقاط الإجمالية"""
        try:
            scores = [
                basic.get('score', 0) * 0.3,  # 30% للفحوصات الأساسية
                pattern.get('score', 0) * 0.25,  # 25% للأنماط
                context.get('score', 0) * 0.25,  # 25% للسياق
                language.get('score', 0) * 0.2   # 20% للغة
            ]
            
            return sum(scores)
        except Exception as e:
            logger.warning(f"⚠️ خطأ في حساب النقاط: {e}")
            return 0.0
    
    def _determine_quality_level(self, score: float) -> str:
        """تحديد مستوى الجودة"""
        if score >= self.quality_thresholds['excellent']:
            return 'ممتاز'
        elif score >= self.quality_thresholds['good']:
            return 'جيد'
        elif score >= self.quality_thresholds['acceptable']:
            return 'مقبول'
        elif score >= self.quality_thresholds['poor']:
            return 'ضعيف'
        else:
            return 'سيء جداً'
    
    def _create_quality_report(self, score: float, quality_level: str, details: Dict) -> Dict:
        """إنشاء تقرير الجودة"""
        return {
            'score': round(score, 2),
            'quality_level': quality_level,
            'is_acceptable': score >= self.quality_thresholds['acceptable'],
            'timestamp': datetime.now().isoformat(),
            'details': details,
            'recommendations': self._generate_recommendations(score, details)
        }
    
    def _generate_recommendations(self, score: float, details: Dict) -> List[str]:
        """إنشاء توصيات لتحسين الجودة"""
        recommendations = []
        
        if score < self.quality_thresholds['acceptable']:
            recommendations.append("النص يحتاج إلى تحسين - قد يكون Whisper فشل في التحويل")
            
        if details.get('basic_checks', {}).get('details', {}).get('text_length', 0) < self.min_text_length:
            recommendations.append("النص قصير جداً - قد تحتاج لفيديو أطول")
            
        if details.get('pattern_checks', {}).get('repetition_ratio', 0) > self.max_repetition_ratio:
            recommendations.append("يوجد تكرار مفرط في النص")
            
        if details.get('context_checks', {}).get('gaming_words', 0) == 0:
            recommendations.append("النص لا يحتوي على كلمات متعلقة بالألعاب")
            
        if not recommendations:
            recommendations.append("جودة النص مقبولة")
            
        return recommendations
    
    def _log_quality_results(self, quality_report: Dict, video_data: Dict = None):
        """تسجيل نتائج فحص الجودة"""
        try:
            # التأكد من وجود النقاط
            score = quality_report.get('score', 0.0)
            if score is None:
                score = 0.0

            log_data = {
                'timestamp': datetime.now().isoformat(),
                'quality_score': float(score),  # التأكد من أنه رقم
                'quality_level': quality_report.get('quality_level', 'unknown'),
                'is_acceptable': quality_report.get('is_acceptable', False),
                'video_id': video_data.get('id') if video_data else None,
                'video_title': video_data.get('title') if video_data else None
            }

            # حفظ في قاعدة البيانات
            db.log_whisper_quality_check(log_data)

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تسجيل نتائج الجودة: {e}")

# إنشاء مثيل عام
whisper_quality_checker = WhisperQualityChecker()
