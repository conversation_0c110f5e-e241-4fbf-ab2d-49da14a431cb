# 🤖 نظام البحث العميق Gemini 2.5 Pro

## نظرة عامة

تم تطوير نظام البحث العميق باستخدام **Gemini 2.5 Pro** كبديل متقدم لـ Tavily في وكيل أخبار الألعاب. هذا النظام يستفيد من قدرات البحث المدمجة في Gemini 2.5 Pro لتوفير نتائج بحث عالية الجودة مع تحليل ذكي للمحتوى.

## ✨ الميزات الرئيسية

### 🔍 البحث العميق المتقدم
- **استخدام Gemini 2.5 Pro فقط** - لا يستخدم نماذج أقل
- **تقنية البحث المدمجة** - يستفيد من قدرات البحث الأصلية في Gemini
- **مستويات عمق متعددة** - من البحث الأساسي إلى الشامل
- **تحليل ذكي للمحتوى** - فهم عميق للسياق والصلة

### 🎯 التكامل الذكي
- **بديل تلقائي لـ Tavily** - يتم التحول تلقائياً عند فشل Tavily
- **فلترة جودة متقدمة** - نتائج عالية الجودة فقط
- **تحليل الصلة** - حساب دقيق لمدى صلة النتائج بالاستعلام
- **كشف البحث على الويب** - تحديد ما إذا كان Gemini استخدم البحث الفعلي

## 🚀 كيفية العمل

### 1. التسلسل الهرمي للبحث

```
1. Tavily Search (الأولوية الأولى)
   ↓ (في حالة الفشل أو عدم توفر نتائج كافية)
2. Gemini 2.5 Pro Deep Search (البديل الذكي)
   ↓ (في حالة الفشل)
3. SerpAPI (احتياطي)
   ↓ (في حالة الفشل)
4. Google Search (احتياطي أخير)
```

### 2. مستويات العمق

#### 🔹 BASIC
- بحث أساسي سريع
- معلومات أساسية حول الموضوع
- مناسب للاستعلامات البسيطة

#### 🔹 STANDARD  
- بحث شامل متوازن
- تحليل للاتجاهات الحالية
- مصادر موثوقة ومراجع

#### 🔹 ADVANCED
- بحث عميق ومتقدم
- تحليل شامل ومعمق
- رؤى تحليلية عميقة
- **الافتراضي للوكيل**

#### 🔹 COMPREHENSIVE
- بحث شامل ومتكامل
- تقرير مفصل مع جميع الجوانب
- تحليل نقدي وتوقعات مستقبلية

## 📊 معايير الجودة

### فلترة النتائج
- **جودة المحتوى**: ≥ 6/10
- **طول المحتوى**: > 200 حرف  
- **نقاط الصلة**: > 0.5
- **وجود كلمات مفتاحية**: متطلب أساسي

### حساب نقاط الصلة
- **تطابق الكلمات المفتاحية** (40%)
- **طول وجودة المحتوى** (20%)
- **مؤشرات الجودة** (30%)
- **التنظيم والهيكلة** (10%)

## 🛠️ الاستخدام

### في الكود

```python
from modules.gemini_deep_search import gemini_deep_search, SearchDepth

# بحث أساسي
results = await gemini_deep_search.deep_search(
    query="latest gaming news",
    search_depth=SearchDepth.BASIC,
    max_results=5
)

# بحث متقدم (الافتراضي)
results = await gemini_deep_search.deep_search(
    query="PlayStation 5 updates",
    search_depth=SearchDepth.ADVANCED,
    max_results=10
)
```

### في ContentScraper

```python
# يتم استخدامه تلقائياً كبديل لـ Tavily
results = await scraper.advanced_search_and_extract_with_tavily(
    keyword="gaming news",
    max_results=15
)
# إذا فشل Tavily، سيتم التحول تلقائياً إلى Gemini 2.5 Pro
```

## 🧪 الاختبار

### تشغيل الاختبارات

```bash
# اختبار شامل
python test_gemini_deep_search.py

# اختبار سريع للقدرة
python -c "
import asyncio
from modules.gemini_deep_search import gemini_deep_search
asyncio.run(gemini_deep_search.test_search_capability())
"
```

### نتائج الاختبار المتوقعة

```
✅ النظام مفعل: نعم
✅ النموذج: gemini-2.5-pro
✅ البحث ناجح: 3-5 نتائج
✅ وقت التنفيذ: 2-5 ثوان
✅ بحث ويب مكتشف: 80%+
✅ متوسط الصلة: 0.7+
```

## 📈 الإحصائيات والمراقبة

### إحصائيات الاستخدام
- إجمالي عمليات البحث
- معدل النجاح
- متوسط وقت الاستجابة
- معدل اكتشاف البحث على الويب
- الاستخدام اليومي

### مراقبة الأداء
```python
# الحصول على الإحصائيات
stats = gemini_deep_search.get_usage_stats()
print(f"معدل النجاح: {stats['success_rate']}%")
print(f"بحث ويب: {stats['web_search_rate']}%")
```

## ⚙️ التكوين

### متطلبات API
- **مفتاح Gemini API** صالح
- **Gemini 2.5 Pro** متاح في المنطقة
- **حصة API** كافية للاستخدام

### الإعدادات القابلة للتخصيص

```python
# في modules/gemini_deep_search.py
search_config = {
    'timeout': 60,           # مهلة الطلب
    'max_retries': 3,        # عدد المحاولات
    'retry_delay': 5,        # تأخير بين المحاولات
    'max_tokens': 8192,      # حد الرموز
    'temperature': 0.7       # درجة الإبداع
}
```

## 🔧 استكشاف الأخطاء

### المشاكل الشائعة

#### ❌ النظام غير مفعل
```
السبب: مفتاح API غير صحيح أو غير موجود
الحل: تحقق من GEMINI_API_KEY في متغيرات البيئة
```

#### ❌ لا توجد نتائج
```
السبب: الاستعلام غير واضح أو محدود جداً
الحل: استخدم كلمات مفتاحية أوسع وأوضح
```

#### ❌ بطء في الاستجابة
```
السبب: استعلام معقد أو مشاكل في الشبكة
الحل: استخدم مستوى عمق أقل أو قلل عدد النتائج
```

### سجلات التشخيص

```python
# تفعيل السجلات المفصلة
import logging
logging.getLogger('modules.gemini_deep_search').setLevel(logging.DEBUG)
```

## 🎯 أفضل الممارسات

### 1. اختيار مستوى العمق المناسب
- **BASIC**: للاستعلامات السريعة
- **STANDARD**: للاستخدام العام
- **ADVANCED**: للوكيل (افتراضي)
- **COMPREHENSIVE**: للتحليل المعمق

### 2. تحسين الاستعلامات
```python
# ✅ جيد - واضح ومحدد
"latest PlayStation 5 game releases 2025"

# ❌ ضعيف - غامض
"games"
```

### 3. مراقبة الاستخدام
- تتبع الإحصائيات بانتظام
- مراقبة معدل النجاح
- تحسين الاستعلامات بناءً على النتائج

## 🔄 التحديثات المستقبلية

### المخطط لها
- [ ] دعم البحث متعدد اللغات
- [ ] تحسين خوارزميات الصلة
- [ ] إضافة تخزين مؤقت ذكي
- [ ] تكامل مع نماذج أخرى كاحتياط

### التحسينات المستمرة
- تحسين دقة كشف البحث على الويب
- تطوير معايير جودة أكثر دقة
- تحسين سرعة المعالجة

---

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل:
1. تحقق من السجلات في `logs/bot.log`
2. شغل `test_gemini_deep_search.py` للتشخيص
3. راجع إحصائيات الاستخدام

**تم التطوير بواسطة**: فريق تطوير وكيل أخبار الألعاب  
**التاريخ**: يناير 2025  
**الإصدار**: 1.0.0
