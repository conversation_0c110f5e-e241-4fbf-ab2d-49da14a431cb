#!/usr/bin/env python3
"""
نظام كشف الألعاب المحسن
"""

import re
import logging
from typing import List, Dict, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class EnhancedGameDetector:
    """كاشف الألعاب المحسن"""
    
    def __init__(self):
        # قاعدة بيانات الألعاب الشائعة
        self.popular_games = [
            # ألعاب شائعة حديثة
            "Cyberpunk 2077", "The Witcher 3", "Elden Ring", "God of War",
            "Spider-Man", "Horizon Zero Dawn", "Red Dead Redemption 2",
            "Call of Duty", "FIFA", "Fortnite", "Minecraft", "Roblox",
            "League of Legends", "Valorant", "Apex Legends", "Overwatch",
            "Counter-Strike", "Dota 2", "PUBG", "Among Us", "Fall Guys",
            
            # ألعاب كلاسيكية
            "Grand Theft Auto", "Assassin's Creed", "Final Fantasy",
            "The Elder Scrolls", "Fallout", "Battlefield", "Halo",
            "Super Mario", "The Legend of Zelda", "Pokemon",
            
            # ألعاب موبايل شائعة
            "Clash of Clans", "Clash Royale", "Candy Crush", "PUBG Mobile",
            "Free Fire", "Mobile Legends", "Genshin Impact", "Honkai",
            
            # ألعاب عربية
            "صراع الملوك", "لعبة الحرب", "أساطير العرب"
        ]
        
        # أنماط كشف الألعاب
        self.game_patterns = [
            # أنماط مباشرة
            r'لعبة\s+([A-Za-z0-9\s:]+?)(?:\s|$|،|\.|!|\?)',
            r'game\s+([A-Za-z0-9\s:]+?)(?:\s|$|،|\.|!|\?)',
            r'([A-Z][a-zA-Z0-9\s:]{2,30})\s+(?:game|لعبة)',
            
            # أنماط متقدمة
            r'في\s+([A-Za-z0-9\s:]{3,25})',
            r'من\s+([A-Za-z0-9\s:]{3,25})',
            r'تحديث\s+([A-Za-z0-9\s:]{3,25})',
            r'إصدار\s+([A-Za-z0-9\s:]{3,25})',
            
            # أنماط للألعاب بالإنجليزية
            r'\b([A-Z][a-zA-Z0-9\s]{2,25}\s(?:2077|2023|2024|2025))\b',
            r'\b([A-Z][a-zA-Z\s]{3,25}\s(?:Edition|Remastered|Ultimate))\b',
            r'\b([A-Z][a-zA-Z\s]{3,25}\s(?:III|IV|V|VI|VII|VIII|IX|X))\b',
        ]
        
        # كلمات مفتاحية للألعاب
        self.gaming_keywords = [
            'gameplay', 'trailer', 'review', 'مراجعة', 'تقييم',
            'release', 'إصدار', 'launch', 'إطلاق', 'beta', 'alpha',
            'update', 'تحديث', 'patch', 'تصحيح', 'DLC', 'expansion'
        ]
    
    def detect_games_in_content(self, title: str, content: str) -> List[Dict]:
        """كشف الألعاب في المحتوى"""
        try:
            detected_games = []
            text_to_analyze = f"{title} {content}"
            
            # 1. البحث عن الألعاب الشائعة أولاً
            popular_games_found = self._find_popular_games(text_to_analyze)
            detected_games.extend(popular_games_found)
            
            # 2. استخدام الأنماط للكشف عن ألعاب جديدة
            pattern_games_found = self._find_games_by_patterns(text_to_analyze)
            detected_games.extend(pattern_games_found)
            
            # 3. تحليل السياق للتأكد
            context_verified_games = self._verify_games_by_context(detected_games, text_to_analyze)
            
            # 4. إزالة التكرار وترتيب حسب الثقة
            final_games = self._deduplicate_and_rank(context_verified_games)
            
            logger.info(f"🎮 تم كشف {len(final_games)} لعبة في المحتوى")
            return final_games
            
        except Exception as e:
            logger.error(f"❌ خطأ في كشف الألعاب: {e}")
            return []
    
    def _find_popular_games(self, text: str) -> List[Dict]:
        """البحث عن الألعاب الشائعة"""
        found_games = []
        text_lower = text.lower()
        
        for game in self.popular_games:
            if game.lower() in text_lower:
                # حساب عدد المرات المذكورة
                mentions = text_lower.count(game.lower())
                
                found_games.append({
                    'name': game,
                    'confidence': 0.9,  # ثقة عالية للألعاب الشائعة
                    'mentions': mentions,
                    'source': 'popular_games_database',
                    'category': self._categorize_game(game)
                })
        
        return found_games
    
    def _find_games_by_patterns(self, text: str) -> List[Dict]:
        """البحث عن الألعاب باستخدام الأنماط"""
        found_games = []
        
        for pattern in self.game_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            
            for match in matches:
                game_name = match.strip()
                
                # تنظيف اسم اللعبة
                game_name = self._clean_game_name(game_name)
                
                if self._is_valid_game_name(game_name):
                    confidence = self._calculate_confidence(game_name, text)
                    
                    found_games.append({
                        'name': game_name,
                        'confidence': confidence,
                        'mentions': text.lower().count(game_name.lower()),
                        'source': 'pattern_detection',
                        'category': 'unknown'
                    })
        
        return found_games
    
    def _clean_game_name(self, name: str) -> str:
        """تنظيف اسم اللعبة"""
        # إزالة الكلمات غير المرغوبة
        unwanted_words = ['news', 'أخبار', 'update', 'تحديث', 'review', 'مراجعة']
        
        words = name.split()
        cleaned_words = [word for word in words if word.lower() not in unwanted_words]
        
        return ' '.join(cleaned_words).strip()
    
    def _is_valid_game_name(self, name: str) -> bool:
        """فحص صحة اسم اللعبة"""
        if not name or len(name) < 3 or len(name) > 50:
            return False
        
        # تجنب الكلمات العامة
        common_words = ['the', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'of', 'with']
        if name.lower() in common_words:
            return False
        
        # يجب أن يحتوي على حرف واحد على الأقل
        if not re.search(r'[a-zA-Z]', name):
            return False
        
        return True
    
    def _calculate_confidence(self, game_name: str, text: str) -> float:
        """حساب مستوى الثقة"""
        confidence = 0.5  # ثقة أساسية
        
        # زيادة الثقة بناءً على السياق
        gaming_context_words = ['لعبة', 'game', 'gaming', 'player', 'لاعب']
        for word in gaming_context_words:
            if word in text.lower():
                confidence += 0.1
        
        # زيادة الثقة إذا كان الاسم يحتوي على أرقام (إصدارات)
        if re.search(r'\d+', game_name):
            confidence += 0.1
        
        # زيادة الثقة إذا كان الاسم بالإنجليزية
        if re.search(r'[A-Z]', game_name):
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def _verify_games_by_context(self, games: List[Dict], text: str) -> List[Dict]:
        """التحقق من الألعاب بناءً على السياق"""
        verified_games = []
        
        for game in games:
            # فحص السياق المحيط باسم اللعبة
            game_name = game['name']
            
            # البحث عن السياق
            context_pattern = f".{{0,50}}{re.escape(game_name)}.{{0,50}}"
            context_matches = re.findall(context_pattern, text, re.IGNORECASE)
            
            if context_matches:
                context = context_matches[0].lower()
                
                # زيادة الثقة إذا كان السياق يدل على الألعاب
                gaming_indicators = ['لعبة', 'game', 'تحديث', 'إصدار', 'مراجعة', 'تقييم']
                if any(indicator in context for indicator in gaming_indicators):
                    game['confidence'] = min(1.0, game['confidence'] + 0.2)
                    game['context_verified'] = True
                else:
                    game['context_verified'] = False
            
            verified_games.append(game)
        
        return verified_games
    
    def _deduplicate_and_rank(self, games: List[Dict]) -> List[Dict]:
        """إزالة التكرار وترتيب حسب الثقة"""
        # إزالة التكرار بناءً على الاسم
        unique_games = {}
        
        for game in games:
            name = game['name'].lower()
            if name not in unique_games or game['confidence'] > unique_games[name]['confidence']:
                unique_games[name] = game
        
        # ترتيب حسب الثقة
        sorted_games = sorted(unique_games.values(), key=lambda x: x['confidence'], reverse=True)
        
        # الاحتفاظ بأفضل 10 ألعاب فقط
        return sorted_games[:10]
    
    def _categorize_game(self, game_name: str) -> str:
        """تصنيف اللعبة"""
        game_lower = game_name.lower()
        
        if any(word in game_lower for word in ['mobile', 'clash', 'candy', 'free fire']):
            return 'mobile'
        elif any(word in game_lower for word in ['call of duty', 'battlefield', 'counter-strike']):
            return 'fps'
        elif any(word in game_lower for word in ['fifa', 'pes', 'football']):
            return 'sports'
        elif any(word in game_lower for word in ['rpg', 'witcher', 'elder scrolls', 'final fantasy']):
            return 'rpg'
        elif any(word in game_lower for word in ['strategy', 'civilization', 'age of empires']):
            return 'strategy'
        else:
            return 'general'
    
    def get_trending_games(self) -> List[str]:
        """الحصول على الألعاب الرائجة"""
        # قائمة بالألعاب الرائجة حالياً
        trending = [
            "Elden Ring", "Cyberpunk 2077", "God of War Ragnarök",
            "Horizon Forbidden West", "Call of Duty Modern Warfare II",
            "FIFA 23", "Valorant", "Apex Legends", "Fortnite",
            "Genshin Impact", "League of Legends", "Minecraft"
        ]
        
        return trending

# إنشاء مثيل عام
enhanced_game_detector = EnhancedGameDetector()
