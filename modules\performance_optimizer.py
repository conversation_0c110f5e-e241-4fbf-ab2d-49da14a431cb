#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحسينات الأداء الأساسية للوكيل
"""

import asyncio
import time
from functools import wraps
from typing import Dict, Any, Optional
import sqlite3
from datetime import datetime, timedelta

class PerformanceOptimizer:
    """محسن الأداء الأساسي"""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = {}
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'db_queries': 0,
            'api_calls': 0
        }
    
    def cache_result(self, key: str, value: Any, ttl: int = 3600):
        """تخزين نتيجة في الذاكرة المؤقتة"""
        self.cache[key] = value
        self.cache_ttl[key] = time.time() + ttl
    
    def get_cached_result(self, key: str) -> Optional[Any]:
        """الحصول على نتيجة من الذاكرة المؤقتة"""
        if key in self.cache:
            if time.time() < self.cache_ttl[key]:
                self.stats['cache_hits'] += 1
                return self.cache[key]
            else:
                # انتهت صلاحية الكاش
                del self.cache[key]
                del self.cache_ttl[key]
        
        self.stats['cache_misses'] += 1
        return None
    
    def clear_expired_cache(self):
        """مسح الكاش المنتهي الصلاحية"""
        current_time = time.time()
        expired_keys = [
            key for key, ttl in self.cache_ttl.items() 
            if current_time >= ttl
        ]
        
        for key in expired_keys:
            del self.cache[key]
            del self.cache_ttl[key]
    
    def get_stats(self) -> Dict:
        """الحصول على إحصائيات الأداء"""
        total_requests = self.stats['cache_hits'] + self.stats['cache_misses']
        hit_rate = (self.stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.stats,
            'cache_hit_rate': round(hit_rate, 2),
            'cache_size': len(self.cache)
        }

def performance_monitor(func):
    """ديكوريتر لمراقبة الأداء"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # تسجيل الأداء
            if execution_time > 5.0:  # أكثر من 5 ثواني
                print(f"⚠️ دالة بطيئة: {func.__name__} استغرقت {execution_time:.2f} ثانية")
            
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"❌ خطأ في {func.__name__} بعد {execution_time:.2f} ثانية: {e}")
            raise
    
    return wrapper

class DatabaseOptimizer:
    """محسن قاعدة البيانات"""
    
    @staticmethod
    def add_missing_indexes():
        """إضافة فهارس مفقودة لتحسين الأداء"""
        try:
            with sqlite3.connect("data/articles.db") as conn:
                cursor = conn.cursor()
                
                # فهارس للجداول الرئيسية (مصحح)
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_published_articles_date ON published_articles(published_at)",
                    "CREATE INDEX IF NOT EXISTS idx_published_articles_category ON published_articles(category)",
                    "CREATE INDEX IF NOT EXISTS idx_published_articles_engagement ON published_articles(engagement_score)",
                    "CREATE INDEX IF NOT EXISTS idx_published_articles_content_hash ON published_articles(content_hash)",
                    "CREATE INDEX IF NOT EXISTS idx_performance_stats_date ON performance_stats(date)",
                ]
                
                for index_sql in indexes:
                    cursor.execute(index_sql)
                
                conn.commit()
                print("✅ تم إضافة فهارس قاعدة البيانات")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في إضافة الفهارس: {e}")
            return False
    
    @staticmethod
    def optimize_database():
        """تحسين قاعدة البيانات"""
        try:
            with sqlite3.connect("data/articles.db") as conn:
                cursor = conn.cursor()
                
                # تحليل وتحسين الجداول
                cursor.execute("ANALYZE")
                cursor.execute("VACUUM")
                
                conn.commit()
                print("✅ تم تحسين قاعدة البيانات")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في تحسين قاعدة البيانات: {e}")
            return False

# إنشاء مثيل عام لمحسن الأداء
performance_optimizer = PerformanceOptimizer()
