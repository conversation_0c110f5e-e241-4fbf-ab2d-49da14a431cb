#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة ويب لإدارة نظام SEOwl
SEOwl Web Interface
"""

from flask import Flask, render_template_string, jsonify, request
import json
import asyncio
from datetime import datetime
from typing import Dict, List

from modules.seowl_integration import seowl_integration
from modules.seowl_indexing_checker import seowl_checker
from modules.database import db
from modules.logger import logger

app = Flask(__name__)

# HTML Template للواجهة الرئيسية
HTML_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 نظام SEOwl للفهرسة الذكية</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: linear-gradient(45deg, #f8f9ff, #e8f4fd);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #667eea;
        }
        .status-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .status-label {
            font-size: 0.9em;
            color: #666;
        }
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        button.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        button.success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }
        button.warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        .cooldown-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .cooldown-timer {
            font-size: 1.2em;
            font-weight: bold;
            color: #856404;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .checks-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .check-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .check-item.pending {
            border-left-color: #f39c12;
        }
        .check-item.checking {
            border-left-color: #3498db;
        }
        .check-item.completed {
            border-left-color: #27ae60;
        }
        .check-item.failed {
            border-left-color: #e74c3c;
        }
        .check-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .check-url {
            font-weight: bold;
            color: #333;
        }
        .check-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-pending { background: #f39c12; color: white; }
        .status-checking { background: #3498db; color: white; }
        .status-completed { background: #27ae60; color: white; }
        .status-failed { background: #e74c3c; color: white; }
        .check-time {
            color: #666;
            font-size: 0.9em;
        }
        .issues-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .issue-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #e74c3c;
        }
        .issue-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .issue-type {
            font-weight: bold;
            color: #333;
        }
        .issue-count {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
        .loading {
            text-align: center;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .integration-status {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .integration-active {
            background: #d4edda;
        }
        .integration-inactive {
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 نظام SEOwl للفهرسة الذكية</h1>
        
        <!-- حالة التكامل -->
        <div class="integration-status" id="integrationStatus">
            <h3>🔗 حالة التكامل</h3>
            <div id="integrationInfo">
                <div class="loading">⏳ جاري تحميل حالة التكامل...</div>
            </div>
        </div>

        <!-- معلومات فترة الانتظار -->
        <div class="cooldown-info" id="cooldownInfo" style="display: none;">
            <h4>⏰ فترة انتظار النشر</h4>
            <div class="cooldown-timer" id="cooldownTimer">--:--:--</div>
            <p>يجب انتظار 3 ساعات بين كل نشر للسماح بفحص المقالات وإصلاحها</p>
        </div>
        
        <!-- إحصائيات النظام -->
        <div class="status-grid" id="statusGrid">
            <div class="status-card">
                <div class="status-number" id="totalChecks">0</div>
                <div class="status-label">إجمالي الفحوصات</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="successfulChecks">0</div>
                <div class="status-label">فحوصات ناجحة</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="issuesFound">0</div>
                <div class="status-label">مشاكل مكتشفة</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="issuesFixed">0</div>
                <div class="status-label">مشاكل محلولة</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="pendingChecks">0</div>
                <div class="status-label">فحوصات معلقة</div>
            </div>
        </div>

        <!-- أدوات التحكم -->
        <div class="controls">
            <button onclick="startIntegration()" class="success" id="startBtn">🚀 تفعيل التكامل</button>
            <button onclick="stopIntegration()" class="danger" id="stopBtn">⏹️ إيقاف التكامل</button>
            <button onclick="forceCheck()" class="warning">🧪 فحص فوري</button>
            <button onclick="refreshData()">🔄 تحديث البيانات</button>
            <button onclick="downloadReport()">📊 تحميل التقرير</button>
        </div>

        <!-- التبويبات -->
        <div class="tabs">
            <div class="tab active" onclick="showTab('checks')">📋 الفحوصات المجدولة</div>
            <div class="tab" onclick="showTab('issues')">🚨 المشاكل المكتشفة</div>
            <div class="tab" onclick="showTab('stats')">📊 الإحصائيات</div>
            <div class="tab" onclick="showTab('settings')">⚙️ الإعدادات</div>
        </div>

        <!-- رسائل النظام -->
        <div id="systemMessage"></div>

        <!-- محتوى التبويبات -->
        <div id="checks" class="tab-content active">
            <div class="checks-list">
                <h3>📋 الفحوصات المجدولة</h3>
                <div id="checksList">
                    <div class="loading">⏳ جاري تحميل الفحوصات...</div>
                </div>
            </div>
        </div>

        <div id="issues" class="tab-content">
            <div class="issues-section">
                <h3>🚨 المشاكل المكتشفة</h3>
                <div id="issuesList">
                    <div class="loading">⏳ جاري تحميل المشاكل...</div>
                </div>
            </div>
        </div>

        <div id="stats" class="tab-content">
            <div class="issues-section">
                <h3>📊 إحصائيات مفصلة</h3>
                <div id="detailedStats">
                    <div class="loading">⏳ جاري تحميل الإحصائيات...</div>
                </div>
            </div>
        </div>

        <div id="settings" class="tab-content">
            <div class="issues-section">
                <h3>⚙️ إعدادات النظام</h3>
                <div id="systemSettings">
                    <div class="loading">⏳ جاري تحميل الإعدادات...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentData = {};
        let integrationActive = false;
        let cooldownTimer = null;

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            setInterval(refreshData, 30000); // تحديث كل 30 ثانية
        });

        async function refreshData() {
            try {
                showMessage('⏳ جاري تحميل البيانات...', 'loading');
                
                const response = await fetch('/api/seowl-status');
                const data = await response.json();
                
                if (data.success) {
                    currentData = data;
                    updateIntegrationStatus(data.integration_status);
                    updateStatusCards(data.seowl_stats);
                    updateChecksList(data.recent_checks);
                    updateIssuesList(data.issues_summary);
                    updateCooldownInfo(data.integration_status);
                    
                    showMessage('✅ تم تحديث البيانات بنجاح', 'success');
                } else {
                    showMessage('❌ خطأ في تحميل البيانات: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        function updateIntegrationStatus(status) {
            const container = document.getElementById('integrationInfo');
            const statusDiv = document.getElementById('integrationStatus');

            integrationActive = status.integration_active;

            if (integrationActive) {
                statusDiv.className = 'integration-status integration-active';
                container.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>✅ التكامل نشط</strong><br>
                            <small>آخر فحص: ${status.last_check || 'لم يتم بعد'}</small>
                        </div>
                        <div style="text-align: left;">
                            <div>فحوصات معلقة: ${status.pending_checks}</div>
                            <div>يمكن النشر: ${status.can_publish_now ? '✅ نعم' : '❌ لا'}</div>
                        </div>
                    </div>
                `;
            } else {
                statusDiv.className = 'integration-status integration-inactive';
                container.innerHTML = `
                    <div>
                        <strong>❌ التكامل غير نشط</strong><br>
                        <small>يرجى تفعيل التكامل لبدء فحص المقالات</small>
                    </div>
                `;
            }

            // تحديث أزرار التحكم
            document.getElementById('startBtn').disabled = integrationActive;
            document.getElementById('stopBtn').disabled = !integrationActive;
        }

        function updateCooldownInfo(status) {
            const cooldownDiv = document.getElementById('cooldownInfo');
            const timerDiv = document.getElementById('cooldownTimer');

            if (status.time_until_next_publish && !status.can_publish_now) {
                cooldownDiv.style.display = 'block';

                // تحديث العداد التنازلي
                if (cooldownTimer) clearInterval(cooldownTimer);

                cooldownTimer = setInterval(() => {
                    updateCountdown(status.time_until_next_publish);
                }, 1000);

            } else {
                cooldownDiv.style.display = 'none';
                if (cooldownTimer) {
                    clearInterval(cooldownTimer);
                    cooldownTimer = null;
                }
            }
        }

        function updateCountdown(timeString) {
            try {
                // تحليل الوقت المتبقي
                const parts = timeString.split(':');
                let hours = parseInt(parts[0]) || 0;
                let minutes = parseInt(parts[1]) || 0;
                let seconds = parseInt(parts[2]) || 0;

                // تقليل ثانية واحدة
                if (seconds > 0) {
                    seconds--;
                } else if (minutes > 0) {
                    minutes--;
                    seconds = 59;
                } else if (hours > 0) {
                    hours--;
                    minutes = 59;
                    seconds = 59;
                } else {
                    // انتهى الوقت
                    clearInterval(cooldownTimer);
                    cooldownTimer = null;
                    document.getElementById('cooldownInfo').style.display = 'none';
                    refreshData();
                    return;
                }

                // تحديث العرض
                const timerDiv = document.getElementById('cooldownTimer');
                timerDiv.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            } catch (error) {
                console.error('خطأ في تحديث العداد:', error);
            }
        }

        function updateStatusCards(stats) {
            document.getElementById('totalChecks').textContent = stats.total_checks || 0;
            document.getElementById('successfulChecks').textContent = stats.successful_checks || 0;
            document.getElementById('issuesFound').textContent = stats.issues_found || 0;
            document.getElementById('issuesFixed').textContent = stats.issues_fixed || 0;
            document.getElementById('pendingChecks').textContent = stats.pending_checks || 0;
        }

        function updateChecksList(checks) {
            const container = document.getElementById('checksList');

            if (!checks || checks.length === 0) {
                container.innerHTML = '<div class="no-data">لا توجد فحوصات مجدولة حالياً</div>';
                return;
            }

            let html = '';
            checks.forEach(check => {
                const statusClass = `status-${check.status}`;
                const itemClass = `check-item ${check.status}`;

                html += `
                    <div class="${itemClass}">
                        <div class="check-header">
                            <div class="check-url">${check.url}</div>
                            <div class="check-status ${statusClass}">${getStatusText(check.status)}</div>
                        </div>
                        <div class="check-time">
                            مجدول: ${new Date(check.scheduled_time).toLocaleString('ar-SA')}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function updateIssuesList(issuesSummary) {
            const container = document.getElementById('issuesList');

            if (!issuesSummary || issuesSummary.total_issues === 0) {
                container.innerHTML = '<div class="no-data">🎉 لا توجد مشاكل مكتشفة!</div>';
                return;
            }

            let html = '';

            if (issuesSummary.seo_issues > 0) {
                html += `
                    <div class="issue-item">
                        <div class="issue-header">
                            <div class="issue-type">🔍 مشاكل SEO</div>
                            <div class="issue-count">${issuesSummary.seo_issues}</div>
                        </div>
                        <div>مشاكل في تحسين محركات البحث تحتاج إلى إصلاح</div>
                    </div>
                `;
            }

            if (issuesSummary.performance_issues > 0) {
                html += `
                    <div class="issue-item">
                        <div class="issue-header">
                            <div class="issue-type">⚡ مشاكل الأداء</div>
                            <div class="issue-count">${issuesSummary.performance_issues}</div>
                        </div>
                        <div>مشاكل في سرعة وأداء الصفحات</div>
                    </div>
                `;
            }

            if (issuesSummary.link_issues > 0) {
                html += `
                    <div class="issue-item">
                        <div class="issue-header">
                            <div class="issue-type">🔗 مشاكل الروابط</div>
                            <div class="issue-count">${issuesSummary.link_issues}</div>
                        </div>
                        <div>روابط مكسورة تحتاج إلى إصلاح</div>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        function getStatusText(status) {
            const statusTexts = {
                'pending': 'معلق',
                'checking': 'جاري الفحص',
                'completed': 'مكتمل',
                'failed': 'فشل'
            };
            return statusTexts[status] || status;
        }

        async function startIntegration() {
            try {
                showMessage('🚀 تفعيل التكامل...', 'loading');

                const response = await fetch('/api/start-integration', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage('✅ تم تفعيل التكامل بنجاح', 'success');
                    integrationActive = true;
                    refreshData();
                } else {
                    showMessage('❌ خطأ في تفعيل التكامل: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function stopIntegration() {
            try {
                showMessage('⏹️ إيقاف التكامل...', 'loading');

                const response = await fetch('/api/stop-integration', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage('✅ تم إيقاف التكامل', 'success');
                    integrationActive = false;
                    refreshData();
                } else {
                    showMessage('❌ خطأ في إيقاف التكامل: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function forceCheck() {
            try {
                const url = prompt('أدخل رابط المقال للفحص الفوري:');
                if (!url) return;

                showMessage('🧪 بدء الفحص الفوري...', 'loading');

                const response = await fetch('/api/force-check', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ url: url })
                });

                const data = await response.json();

                if (data.success) {
                    showMessage('✅ تم الفحص الفوري بنجاح', 'success');
                    refreshData();
                } else {
                    showMessage('❌ خطأ في الفحص: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function downloadReport() {
            try {
                const response = await fetch('/api/download-seowl-report');
                const blob = await response.blob();

                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `seowl_report_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                showMessage('✅ تم تحميل التقرير بنجاح', 'success');
            } catch (error) {
                showMessage('❌ خطأ في تحميل التقرير: ' + error.message, 'error');
            }
        }

        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('systemMessage');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;

            // إخفاء الرسالة بعد 5 ثوان (عدا رسائل التحميل)
            if (type !== 'loading') {
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 5000);
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/seowl-status')
def get_seowl_status():
    """الحصول على حالة نظام SEOwl"""
    try:
        # الحصول على حالة التكامل
        integration_status = seowl_integration.get_integration_status()

        # الحصول على إحصائيات SEOwl
        seowl_stats = seowl_checker.get_stats()

        # الحصول على الفحوصات الحديثة
        recent_checks = seowl_integration.get_recent_checks(10)

        # الحصول على ملخص المشاكل
        issues_summary = seowl_integration.get_issues_summary()

        return jsonify({
            'success': True,
            'integration_status': integration_status,
            'seowl_stats': seowl_stats,
            'recent_checks': recent_checks,
            'issues_summary': issues_summary,
            'last_updated': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على حالة SEOwl: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/start-integration', methods=['POST'])
def start_integration():
    """تفعيل التكامل مع SEOwl"""
    try:
        seowl_integration.start_integration()

        logger.info("🚀 تم تفعيل تكامل SEOwl")

        return jsonify({
            'success': True,
            'message': 'تم تفعيل التكامل بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في تفعيل التكامل: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stop-integration', methods=['POST'])
def stop_integration():
    """إيقاف التكامل مع SEOwl"""
    try:
        seowl_integration.stop_integration()

        logger.info("⏹️ تم إيقاف تكامل SEOwl")

        return jsonify({
            'success': True,
            'message': 'تم إيقاف التكامل'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إيقاف التكامل: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/force-check', methods=['POST'])
def force_check():
    """فحص فوري لمقال"""
    try:
        data = request.get_json()
        url = data.get('url')

        if not url:
            return jsonify({
                'success': False,
                'error': 'رابط المقال مطلوب'
            }), 400

        # إنشاء بيانات مقال وهمية للفحص
        article_data = {
            'id': f"force_check_{int(datetime.now().timestamp())}",
            'title': 'فحص فوري',
            'url': url
        }

        # تشغيل الفحص الفوري
        success = seowl_integration.force_check_article(article_data, url)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم الفحص الفوري بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في الفحص الفوري'
            }), 500

    except Exception as e:
        logger.error(f"❌ خطأ في الفحص الفوري: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/download-seowl-report')
def download_seowl_report():
    """تحميل تقرير شامل لـ SEOwl"""
    try:
        # إنشاء التقرير الشامل
        report = {
            'generated_at': datetime.now().isoformat(),
            'integration_status': seowl_integration.get_integration_status(),
            'seowl_stats': seowl_checker.get_stats(),
            'recent_checks': seowl_integration.get_recent_checks(50),
            'issues_summary': seowl_integration.get_issues_summary(),
            'pending_seo_improvements': db.get_pending_seo_improvements(100),
            'pending_performance_improvements': db.get_pending_performance_improvements(100),
            'pending_link_fixes': db.get_pending_link_fixes(100),
            'seowl_stats_history': db.get_seowl_stats_history(30)
        }

        # إنشاء استجابة JSON للتحميل
        from flask import Response

        response = Response(
            json.dumps(report, ensure_ascii=False, indent=2),
            mimetype='application/json',
            headers={
                'Content-Disposition': f'attachment; filename=seowl_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            }
        )

        return response

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل تقرير SEOwl: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/pending-checks')
def get_pending_checks():
    """الحصول على الفحوصات المعلقة"""
    try:
        pending_checks = seowl_checker.get_pending_checks()

        return jsonify({
            'success': True,
            'pending_checks': pending_checks,
            'count': len(pending_checks)
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على الفحوصات المعلقة: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/issues-summary')
def get_issues_summary():
    """الحصول على ملخص المشاكل"""
    try:
        issues_summary = seowl_integration.get_issues_summary()

        return jsonify({
            'success': True,
            'issues_summary': issues_summary
        })

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على ملخص المشاكل: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/can-publish')
def can_publish():
    """فحص إمكانية النشر الآن"""
    try:
        can_publish_now = seowl_integration.can_publish_now()
        time_until_next = seowl_integration.get_time_until_next_publish()

        return jsonify({
            'success': True,
            'can_publish': can_publish_now,
            'time_until_next_publish': str(time_until_next) if time_until_next else None,
            'cooldown_hours': seowl_integration.publish_cooldown_hours
        })

    except Exception as e:
        logger.error(f"❌ خطأ في فحص إمكانية النشر: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("🚀 بدء واجهة إدارة SEOwl...")
    logger.info("🌐 الواجهة متاحة على: http://localhost:5003")

    app.run(host='0.0.0.0', port=5003, debug=False, use_reloader=False)
