<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المحسنة - وكيل أخبار الألعاب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .status-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .status-card:hover {
            transform: translateY(-2px);
        }
        .status-running { border-left: 5px solid #28a745; }
        .status-stopped { border-left: 5px solid #dc3545; }
        .status-starting { border-left: 5px solid #ffc107; }
        .status-error { border-left: 5px solid #fd7e14; }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .health-excellent { color: #28a745; }
        .health-good { color: #17a2b8; }
        .health-average { color: #ffc107; }
        .health-poor { color: #dc3545; }
        
        .operation-item {
            border-radius: 10px;
            margin-bottom: 10px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .log-container {
            background: #1e1e1e;
            color: #ffffff;
            border-radius: 10px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .btn-control {
            border-radius: 25px;
            padding: 10px 20px;
            font-weight: bold;
        }
        
        .progress-custom {
            height: 8px;
            border-radius: 10px;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .refresh-indicator {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-robot me-2"></i>
                وكيل أخبار الألعاب - النظام الذكي
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
                    <i class="fas fa-sync-alt" id="refresh-icon"></i>
                    تحديث
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- بطاقات الحالة الرئيسية -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card status-card" id="agent-status-card">
                    <div class="card-body text-center">
                        <i class="fas fa-robot fa-3x mb-3 text-primary"></i>
                        <h5 class="card-title">حالة الوكيل</h5>
                        <h3 id="agent-state" class="mb-2">جاري التحميل...</h3>
                        <p id="agent-uptime" class="text-muted">-</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-database fa-3x mb-3"></i>
                        <h5 class="card-title">قاعدة البيانات</h5>
                        <h3 id="db-health" class="mb-2">-</h3>
                        <p id="db-size" class="mb-0">-</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-tasks fa-3x mb-3"></i>
                        <h5 class="card-title">العمليات</h5>
                        <h3 id="active-operations" class="mb-2">-</h3>
                        <p id="total-operations" class="mb-0">-</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-heart fa-3x mb-3"></i>
                        <h5 class="card-title">صحة النظام</h5>
                        <h3 id="system-health" class="mb-2">-</h3>
                        <p id="health-score" class="mb-0">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-gamepad me-2"></i>التحكم في الوكيل</h5>
                    </div>
                    <div class="card-body">
                        <div class="btn-group me-2" role="group">
                            <button type="button" class="btn btn-success btn-control" onclick="startAgent('normal')">
                                <i class="fas fa-play me-1"></i>تشغيل عادي
                            </button>
                            <button type="button" class="btn btn-info btn-control" onclick="startAgent('recovery')">
                                <i class="fas fa-redo me-1"></i>تشغيل استعادة
                            </button>
                            <button type="button" class="btn btn-warning btn-control" onclick="startAgent('safe_mode')">
                                <i class="fas fa-shield-alt me-1"></i>الوضع الآمن
                            </button>
                        </div>
                        
                        <div class="btn-group me-2" role="group">
                            <button type="button" class="btn btn-warning btn-control" onclick="stopAgent('user_request')">
                                <i class="fas fa-pause me-1"></i>إيقاف آمن
                            </button>
                            <button type="button" class="btn btn-danger btn-control" onclick="stopAgent('emergency', true)">
                                <i class="fas fa-stop me-1"></i>إيقاف طارئ
                            </button>
                        </div>
                        
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-secondary btn-control" onclick="createBackup()">
                                <i class="fas fa-save me-1"></i>نسخة احتياطية
                            </button>
                            <button type="button" class="btn btn-primary btn-control" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>تحديث البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- العمليات النشطة والسجلات -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>العمليات النشطة</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="active-operations-list">
                            <div class="text-center text-muted">
                                <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                <p>جاري تحميل العمليات...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>السجلات الأخيرة</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="log-container p-3" id="logs-container">
                            <div class="text-center text-muted">
                                <i class="fas fa-spinner fa-spin"></i>
                                جاري تحميل السجلات...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الأخطاء -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>إحصائيات الأخطاء (آخر 24 ساعة)</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="error-stats">
                            <div class="col-md-3 text-center">
                                <h4 id="total-errors" class="text-danger">-</h4>
                                <p class="text-muted">إجمالي الأخطاء</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 id="resolved-errors" class="text-success">-</h4>
                                <p class="text-muted">الأخطاء المحلولة</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 id="resolution-rate" class="text-info">-</h4>
                                <p class="text-muted">معدل الحل</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 id="critical-errors" class="text-warning">-</h4>
                                <p class="text-muted">أخطاء حرجة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة التأكيد -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد العملية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="confirm-message">
                    هل أنت متأكد من هذه العملية؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="confirm-action">تأكيد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة الإشعارات -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toast-container">
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let refreshInterval;
        let confirmCallback = null;

        // بدء التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            
            refreshData();
            startAutoRefresh();
        });

        // تحديث الوقت الحالي
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('ar-SA');
        }

        // بدء التحديث التلقائي
        function startAutoRefresh() {
            refreshInterval = setInterval(refreshData, 10000); // كل 10 ثوان
        }

        // إيقاف التحديث التلقائي
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        // تحديث البيانات
        async function refreshData() {
            const refreshIcon = document.getElementById('refresh-icon');
            refreshIcon.classList.add('refresh-indicator');

            try {
                await Promise.all([
                    updateSystemStatus(),
                    updateOperations(),
                    updateLogs(),
                    updateErrorStats()
                ]);
            } catch (error) {
                console.error('خطأ في تحديث البيانات:', error);
                showToast('خطأ في تحديث البيانات', 'error');
            } finally {
                refreshIcon.classList.remove('refresh-indicator');
            }
        }

        // تحديث حالة النظام
        async function updateSystemStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();

                if (data.success) {
                    // حالة الوكيل
                    const agentState = data.agent_status.state;
                    const agentCard = document.getElementById('agent-status-card');

                    document.getElementById('agent-state').textContent = getStateText(agentState);
                    document.getElementById('agent-uptime').textContent =
                        `وقت التشغيل: ${(data.agent_status.uptime_seconds / 3600).toFixed(1)} ساعة`;

                    // تحديث لون البطاقة
                    agentCard.className = `card status-card status-${agentState}`;

                    // قاعدة البيانات
                    const dbHealth = data.database_health.integrity_check === 'ok' ? 'سليمة' : 'تحتاج إصلاح';
                    document.getElementById('db-health').textContent = dbHealth;
                    document.getElementById('db-size').textContent =
                        `الحجم: ${data.database_health.file_size_mb.toFixed(1)} MB`;

                    // العمليات
                    document.getElementById('active-operations').textContent =
                        data.operation_stats.running_operations;
                    document.getElementById('total-operations').textContent =
                        `إجمالي: ${data.operation_stats.total_tasks}`;

                    // صحة النظام
                    const healthStatus = data.health_report.health_status;
                    const healthScore = data.health_report.health_score;

                    document.getElementById('system-health').textContent = healthStatus;
                    document.getElementById('health-score').textContent = `${healthScore}%`;

                    // تحديث لون صحة النظام
                    const healthElement = document.getElementById('system-health');
                    healthElement.className = getHealthClass(healthStatus);
                }
            } catch (error) {
                console.error('خطأ في تحديث حالة النظام:', error);
            }
        }

        // تحديث العمليات
        async function updateOperations() {
            try {
                const response = await fetch('/api/operations');
                const data = await response.json();

                if (data.success) {
                    const container = document.getElementById('active-operations-list');

                    if (data.active_operations.length === 0) {
                        container.innerHTML = `
                            <div class="text-center text-muted">
                                <i class="fas fa-check-circle fa-2x mb-3"></i>
                                <p>لا توجد عمليات نشطة حالياً</p>
                            </div>
                        `;
                    } else {
                        container.innerHTML = data.active_operations.map(op => `
                            <div class="operation-item">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">${op.operation_type}</h6>
                                    <span class="badge bg-${getOperationBadgeClass(op.state)}">${getOperationStateText(op.state)}</span>
                                </div>
                                <div class="progress progress-custom mb-2">
                                    <div class="progress-bar" style="width: ${op.progress}%"></div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">ID: ${op.task_id.substring(0, 8)}...</small>
                                    <div class="btn-group btn-group-sm">
                                        ${op.state === 'running' ? `
                                            <button class="btn btn-warning btn-sm" onclick="pauseOperation('${op.task_id}')">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        ` : ''}
                                        ${op.state === 'paused' ? `
                                            <button class="btn btn-success btn-sm" onclick="resumeOperation('${op.task_id}')">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        ` : ''}
                                        <button class="btn btn-danger btn-sm" onclick="cancelOperation('${op.task_id}')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('');
                    }
                }
            } catch (error) {
                console.error('خطأ في تحديث العمليات:', error);
            }
        }

        // تحديث السجلات
        async function updateLogs() {
            try {
                const response = await fetch('/api/logs');
                const data = await response.json();

                if (data.success) {
                    const container = document.getElementById('logs-container');

                    if (data.logs.length === 0) {
                        container.innerHTML = '<div class="text-center text-muted">لا توجد سجلات متاحة</div>';
                    } else {
                        container.innerHTML = data.logs.map(log =>
                            `<div class="log-line">${log.trim()}</div>`
                        ).join('');

                        // التمرير للأسفل
                        container.scrollTop = container.scrollHeight;
                    }
                }
            } catch (error) {
                console.error('خطأ في تحديث السجلات:', error);
            }
        }

        // تحديث إحصائيات الأخطاء
        async function updateErrorStats() {
            try {
                const response = await fetch('/api/errors');
                const data = await response.json();

                if (data.success) {
                    const stats = data.error_stats;

                    document.getElementById('total-errors').textContent = stats.total_errors;
                    document.getElementById('resolved-errors').textContent = stats.resolved_errors;
                    document.getElementById('resolution-rate').textContent = `${stats.resolution_rate_percent.toFixed(1)}%`;
                    document.getElementById('critical-errors').textContent =
                        stats.severity_breakdown.critical || 0;
                }
            } catch (error) {
                console.error('خطأ في تحديث إحصائيات الأخطاء:', error);
            }
        }

        // بدء تشغيل الوكيل
        function startAgent(mode) {
            const message = `هل تريد بدء تشغيل الوكيل في وضع "${mode}"؟`;
            showConfirm(message, async () => {
                try {
                    const response = await fetch('/api/control/start', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ mode: mode })
                    });

                    const data = await response.json();

                    if (data.success) {
                        showToast(data.message, 'success');
                        setTimeout(refreshData, 2000);
                    } else {
                        showToast(data.error, 'error');
                    }
                } catch (error) {
                    showToast('خطأ في بدء تشغيل الوكيل', 'error');
                }
            });
        }

        // إيقاف الوكيل
        function stopAgent(reason, emergency = false) {
            const message = emergency ?
                'هل تريد إيقاف الوكيل بشكل طارئ؟ (قد يؤدي لفقدان البيانات)' :
                'هل تريد إيقاف الوكيل بشكل آمن؟';

            showConfirm(message, async () => {
                try {
                    const response = await fetch('/api/control/stop', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ reason: reason, emergency: emergency })
                    });

                    const data = await response.json();

                    if (data.success) {
                        showToast(data.message, 'success');
                        setTimeout(refreshData, 2000);
                    } else {
                        showToast(data.error, 'error');
                    }
                } catch (error) {
                    showToast('خطأ في إيقاف الوكيل', 'error');
                }
            });
        }

        // إلغاء عملية
        async function cancelOperation(operationId) {
            try {
                const response = await fetch(`/api/operations/${operationId}/cancel`, {
                    method: 'POST'
                });

                const data = await response.json();

                if (data.success) {
                    showToast(data.message, 'success');
                    refreshData();
                } else {
                    showToast(data.error, 'error');
                }
            } catch (error) {
                showToast('خطأ في إلغاء العملية', 'error');
            }
        }

        // إيقاف عملية مؤقتاً
        async function pauseOperation(operationId) {
            try {
                const response = await fetch(`/api/operations/${operationId}/pause`, {
                    method: 'POST'
                });

                const data = await response.json();

                if (data.success) {
                    showToast(data.message, 'success');
                    refreshData();
                } else {
                    showToast(data.error, 'error');
                }
            } catch (error) {
                showToast('خطأ في إيقاف العملية', 'error');
            }
        }

        // استئناف عملية
        async function resumeOperation(operationId) {
            try {
                const response = await fetch(`/api/operations/${operationId}/resume`, {
                    method: 'POST'
                });

                const data = await response.json();

                if (data.success) {
                    showToast(data.message, 'success');
                    refreshData();
                } else {
                    showToast(data.error, 'error');
                }
            } catch (error) {
                showToast('خطأ في استئناف العملية', 'error');
            }
        }

        // إنشاء نسخة احتياطية
        async function createBackup() {
            showConfirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟', async () => {
                try {
                    const response = await fetch('/api/database/backup', {
                        method: 'POST'
                    });

                    const data = await response.json();

                    if (data.success) {
                        showToast(data.message, 'success');
                    } else {
                        showToast(data.error, 'error');
                    }
                } catch (error) {
                    showToast('خطأ في إنشاء النسخة الاحتياطية', 'error');
                }
            });
        }

        // دوال مساعدة
        function getStateText(state) {
            const states = {
                'running': 'يعمل',
                'stopped': 'متوقف',
                'starting': 'بدء التشغيل',
                'stopping': 'جاري الإيقاف',
                'paused': 'متوقف مؤقتاً',
                'error': 'خطأ',
                'maintenance': 'صيانة'
            };
            return states[state] || state;
        }

        function getOperationStateText(state) {
            const states = {
                'pending': 'في الانتظار',
                'running': 'جاري التنفيذ',
                'completed': 'مكتمل',
                'failed': 'فشل',
                'cancelled': 'ملغي',
                'paused': 'متوقف مؤقتاً'
            };
            return states[state] || state;
        }

        function getOperationBadgeClass(state) {
            const classes = {
                'pending': 'secondary',
                'running': 'primary',
                'completed': 'success',
                'failed': 'danger',
                'cancelled': 'warning',
                'paused': 'info'
            };
            return classes[state] || 'secondary';
        }

        function getHealthClass(status) {
            const classes = {
                'ممتاز': 'health-excellent',
                'جيد': 'health-good',
                'متوسط': 'health-average',
                'ضعيف': 'health-poor'
            };
            return classes[status] || '';
        }

        // عرض نافذة التأكيد
        function showConfirm(message, callback) {
            document.getElementById('confirm-message').textContent = message;
            confirmCallback = callback;

            const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
            modal.show();
        }

        // معالج زر التأكيد
        document.getElementById('confirm-action').addEventListener('click', function() {
            if (confirmCallback) {
                confirmCallback();
                confirmCallback = null;
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
            modal.hide();
        });

        // عرض إشعار
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container');
            const toastId = 'toast-' + Date.now();

            const bgClass = {
                'success': 'bg-success',
                'error': 'bg-danger',
                'warning': 'bg-warning',
                'info': 'bg-info'
            }[type] || 'bg-info';

            const icon = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            }[type] || 'fas fa-info-circle';

            const toastHtml = `
                <div id="${toastId}" class="toast ${bgClass} text-white" role="alert">
                    <div class="toast-header ${bgClass} text-white border-0">
                        <i class="${icon} me-2"></i>
                        <strong class="me-auto">إشعار</strong>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
            toast.show();

            // إزالة العنصر بعد الإخفاء
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }
    </script>
</body>
</html>
